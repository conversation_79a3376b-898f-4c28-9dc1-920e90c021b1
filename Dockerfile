# Dockerfile para Sistema de Relatórios 3CX - Amvox
FROM python:3.11-slim

# Metadados
LABEL maintainer="Amvox Development Team"
LABEL description="Sistema de Relatórios 3CX - Análise de métricas de pós-venda"
LABEL version="2.1.0"

# Variáveis de ambiente
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV APP_HOST=0.0.0.0
ENV APP_PORT=8000

# Diretório de trabalho
WORKDIR /app

# Instala dependências do sistema
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copia arquivos de dependências
COPY requirements.txt .

# Instala dependências Python
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

# Copia código da aplicação
COPY . .

# Cria diretórios necessários
RUN mkdir -p static temp logs

# Cria usuário não-root
RUN useradd --create-home --shell /bin/bash amvox
RUN chown -R amvox:amvox /app
USER amvox

# Expõe porta
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Comando de inicialização
CMD ["python", "main.py"]
