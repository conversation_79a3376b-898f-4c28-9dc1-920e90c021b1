"""
Modelos de dados para relatórios
"""
from typing import List, Optional, Dict, Any
from datetime import date, datetime
from pydantic import BaseModel, Field
from enum import Enum


class ReportType(str, Enum):
    """Tipos de relatórios disponíveis"""
    DISTRIBUTION = "distribution"
    SATISFACTION = "satisfaction"
    TIMING_METRICS = "timing_metrics"
    AGENT_PERFORMANCE = "agent_performance"
    QUEUE_ANALYSIS = "queue_analysis"
    SLA_COMPLIANCE = "sla_compliance"
    COMPARATIVE = "comparative"


class ReportRequest(BaseModel):
    """Requisição base para relatórios"""
    start_date: date
    end_date: date
    queues: Optional[List[int]] = None
    report_type: ReportType
    
    class Config:
        json_encoders = {
            date: lambda v: v.isoformat()
        }


class QueueInfo(BaseModel):
    """Informações de uma fila"""
    queue_id: int
    queue_name: str
    calls: int
    percentage: float
    answered_calls: int = 0
    abandoned_calls: int = 0
    avg_wait_time: float = 0.0
    avg_talk_time: float = 0.0


class TimingMetrics(BaseModel):
    """Métricas de timing"""
    asa: float = Field(description="Average Speed of Answer (segundos)")
    aht: float = Field(description="Average Handle Time (segundos)")
    att: float = Field(description="Average Talk Time (segundos)")
    acw: float = Field(description="After Call Work (segundos)")
    service_level: float = Field(description="Service Level (%)")
    abandonment_rate: float = Field(description="Taxa de abandono (%)")
    total_calls: int = Field(description="Total de chamadas")
    answered_calls: int = Field(description="Chamadas atendidas")
    abandoned_calls: int = Field(description="Chamadas abandonadas")
    first_call_resolution: float = Field(default=0.0, description="FCR (%)")


class AgentPerformance(BaseModel):
    """Performance de um agente"""
    agent_id: str
    agent_name: str
    calls_handled: int
    talk_time: float = Field(description="Tempo total de conversa (segundos)")
    break_time: float = Field(description="Tempo de pausa (segundos)")
    login_time: float = Field(description="Tempo logado (segundos)")
    occupancy_rate: float = Field(description="Taxa de ocupação (%)")
    productivity_score: float = Field(description="Score de produtividade")
    calls_per_hour: float = Field(description="Chamadas por hora")
    average_call_duration: float = Field(description="Duração média das chamadas")
    first_call_resolution: float = Field(description="FCR (%)")
    customer_satisfaction: float = Field(default=0.0, description="Satisfação do cliente (%)")


class SatisfactionMetrics(BaseModel):
    """Métricas de satisfação"""
    nps: float = Field(description="Net Promoter Score")
    csat: float = Field(description="Customer Satisfaction")
    total_responses: int = Field(description="Total de respostas")
    promoters: int = Field(description="Promotores (9-10)")
    passives: int = Field(description="Neutros (7-8)")
    detractors: int = Field(description="Detratores (0-6)")
    average_rating: float = Field(description="Nota média")
    response_rate: float = Field(description="Taxa de resposta (%)")


class DistributionReport(BaseModel):
    """Relatório de distribuição"""
    period_start: date
    period_end: date
    total_calls: int
    answered_calls: int
    abandoned_calls: int
    distribution_by_queue: List[QueueInfo]
    peak_hours: Dict[str, int] = Field(default_factory=dict)
    daily_distribution: Dict[str, int] = Field(default_factory=dict)


class SatisfactionReport(BaseModel):
    """Relatório de satisfação"""
    period_start: date
    period_end: date
    metrics: SatisfactionMetrics
    distribution_by_queue: List[Dict[str, Any]] = Field(default_factory=list)
    rating_distribution: Dict[str, int] = Field(default_factory=dict)
    comments: List[str] = Field(default_factory=list)


class TimingReport(BaseModel):
    """Relatório de métricas de timing"""
    period_start: date
    period_end: date
    overall_metrics: TimingMetrics
    metrics_by_queue: List[Dict[str, Any]] = Field(default_factory=list)
    hourly_metrics: Dict[str, TimingMetrics] = Field(default_factory=dict)


class AgentReport(BaseModel):
    """Relatório de performance de agentes"""
    period_start: date
    period_end: date
    agents: List[AgentPerformance]
    team_averages: AgentPerformance
    top_performers: List[str] = Field(default_factory=list)
    improvement_opportunities: List[str] = Field(default_factory=list)


class SLACompliance(BaseModel):
    """Compliance de SLA"""
    sla_target: float = Field(description="Meta de SLA (%)")
    actual_performance: float = Field(description="Performance atual (%)")
    compliance_status: str = Field(description="Status do compliance")
    calls_within_sla: int = Field(description="Chamadas dentro do SLA")
    calls_outside_sla: int = Field(description="Chamadas fora do SLA")
    average_response_time: float = Field(description="Tempo médio de resposta")


class ComparativeMetrics(BaseModel):
    """Métricas comparativas entre períodos"""
    current_period: Dict[str, Any]
    previous_period: Dict[str, Any]
    variance: Dict[str, float] = Field(default_factory=dict)
    trends: Dict[str, str] = Field(default_factory=dict)


class ReportResponse(BaseModel):
    """Resposta padrão para relatórios"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    generated_at: datetime = Field(default_factory=datetime.now)
    report_type: ReportType
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            date: lambda v: v.isoformat()
        }
