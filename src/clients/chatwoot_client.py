"""
Cliente para conexão com o banco de dados do Chatwoot
"""
import asyncpg
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, date
from config import settings

logger = logging.getLogger(__name__)


class ChatwootClient:
    """Cliente para acessar dados do Chatwoot"""
    
    def __init__(self):
        self.connection_pool = None
        self.account_id = settings.chatwoot_account_id
    
    async def connect(self):
        """Estabelece conexão com o banco de dados"""
        try:
            self.connection_pool = await asyncpg.create_pool(
                settings.chatwoot_database_url,
                min_size=1,
                max_size=10,
                command_timeout=60
            )
            logger.info("Conexão com Chatwoot estabelecida com sucesso")
        except Exception as e:
            logger.error(f"Erro ao conectar com Chatwoot: {e}")
            raise
    
    async def disconnect(self):
        """Fecha conexão com o banco de dados"""
        if self.connection_pool:
            await self.connection_pool.close()
            logger.info("Conexão com Chatwoot fechada")
    
    async def test_connection(self) -> bool:
        """Testa a conexão com o banco"""
        try:
            if not self.connection_pool:
                await self.connect()
            
            async with self.connection_pool.acquire() as conn:
                result = await conn.fetchval("SELECT version()")
                logger.info(f"Teste de conexão Chatwoot bem-sucedido: {result}")
                return True
        except Exception as e:
            logger.error(f"Erro no teste de conexão Chatwoot: {e}")
            return False
    
    async def get_satisfaction_metrics(
        self, 
        start_date: date, 
        end_date: date, 
        agent_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Busca métricas de satisfação"""
        query = """
            SELECT
                u.name as agent_name,
                u.id as agent_id,
                COUNT(csr.id) as total_ratings,
                AVG(csr.rating) as average_rating,
                COUNT(CASE WHEN csr.rating >= 4 THEN 1 END) as positive_ratings,
                COUNT(CASE WHEN csr.rating <= 2 THEN 1 END) as negative_ratings,
                ROUND(
                    (COUNT(CASE WHEN csr.rating >= 4 THEN 1 END) * 100.0 / NULLIF(COUNT(csr.id), 0)), 2
                ) as satisfaction_percentage
            FROM csat_survey_responses csr
            JOIN users u ON csr.assigned_agent_id = u.id
            WHERE csr.created_at BETWEEN $1 AND $2
                AND csr.account_id = $3
        """
        
        params = [start_date, end_date, self.account_id]
        
        if agent_id:
            query += " AND csr.assigned_agent_id = $4"
            params.append(agent_id)
        
        query += " GROUP BY u.id, u.name ORDER BY average_rating DESC"
        
        try:
            async with self.connection_pool.acquire() as conn:
                rows = await conn.fetch(query, *params)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Erro ao buscar métricas de satisfação: {e}")
            raise
    
    async def get_response_time_metrics(
        self, 
        start_date: date, 
        end_date: date, 
        agent_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Busca métricas de tempo de resposta"""
        query = """
            SELECT
                u.name as agent_name,
                u.id as agent_id,
                COUNT(c.id) as total_conversations,
                AVG(
                    EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at)) / 60
                ) as avg_first_response_time_minutes,
                MIN(
                    EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at)) / 60
                ) as min_response_time_minutes,
                MAX(
                    EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at)) / 60
                ) as max_response_time_minutes
            FROM conversations c
            JOIN users u ON c.assignee_id = u.id
            WHERE c.first_reply_created_at IS NOT NULL
                AND c.created_at BETWEEN $1 AND $2
                AND c.account_id = $3
        """
        
        params = [start_date, end_date, self.account_id]
        
        if agent_id:
            query += " AND c.assignee_id = $4"
            params.append(agent_id)
        
        query += " GROUP BY u.id, u.name ORDER BY avg_first_response_time_minutes ASC"
        
        try:
            async with self.connection_pool.acquire() as conn:
                rows = await conn.fetch(query, *params)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Erro ao buscar métricas de tempo de resposta: {e}")
            raise
    
    async def get_conversation_volume(
        self, 
        start_date: date, 
        end_date: date
    ) -> List[Dict[str, Any]]:
        """Busca volume de conversas por dia"""
        query = """
            SELECT
                DATE(c.created_at) as date,
                COUNT(*) as total_conversations,
                COUNT(CASE WHEN c.status = 0 THEN 1 END) as open_conversations,
                COUNT(CASE WHEN c.status = 1 THEN 1 END) as resolved_conversations,
                COUNT(CASE WHEN c.status = 2 THEN 1 END) as pending_conversations,
                AVG(
                    CASE 
                        WHEN c.first_reply_created_at IS NOT NULL 
                        THEN EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at)) / 60
                    END
                ) as avg_response_time_minutes
            FROM conversations c
            WHERE c.created_at BETWEEN $1 AND $2
                AND c.account_id = $3
            GROUP BY DATE(c.created_at)
            ORDER BY date DESC
        """
        
        try:
            async with self.connection_pool.acquire() as conn:
                rows = await conn.fetch(query, start_date, end_date, self.account_id)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Erro ao buscar volume de conversas: {e}")
            raise
    
    async def get_dashboard_summary(
        self, 
        start_date: date, 
        end_date: date
    ) -> Dict[str, Any]:
        """Busca resumo para dashboard"""
        # Query para métricas gerais
        summary_query = """
            SELECT
                COUNT(*) as total_conversations,
                COUNT(CASE WHEN status = 1 THEN 1 END) as resolved_conversations,
                COUNT(CASE WHEN status = 0 THEN 1 END) as open_conversations,
                COUNT(CASE WHEN status = 2 THEN 1 END) as pending_conversations,
                AVG(
                    CASE
                        WHEN first_reply_created_at IS NOT NULL
                        THEN EXTRACT(EPOCH FROM (first_reply_created_at - created_at)) / 60
                    END
                ) as avg_first_response_time_minutes,
                COUNT(DISTINCT assignee_id) as active_agents
            FROM conversations
            WHERE created_at BETWEEN $1 AND $2
                AND account_id = $3
        """
        
        # Query para satisfação geral
        satisfaction_query = """
            SELECT
                COUNT(*) as total_ratings,
                AVG(rating) as average_rating,
                COUNT(CASE WHEN rating >= 4 THEN 1 END) as positive_ratings,
                COUNT(CASE WHEN rating <= 2 THEN 1 END) as negative_ratings
            FROM csat_survey_responses
            WHERE created_at BETWEEN $1 AND $2
                AND account_id = $3
        """
        
        try:
            async with self.connection_pool.acquire() as conn:
                summary_result = await conn.fetchrow(summary_query, start_date, end_date, self.account_id)
                satisfaction_result = await conn.fetchrow(satisfaction_query, start_date, end_date, self.account_id)
                
                summary = dict(summary_result) if summary_result else {}
                satisfaction = dict(satisfaction_result) if satisfaction_result else {}
                
                # Calcular percentuais
                total_conversations = summary.get('total_conversations', 0)
                resolved_conversations = summary.get('resolved_conversations', 0)
                resolution_rate = (resolved_conversations * 100 / total_conversations) if total_conversations > 0 else 0
                
                total_ratings = satisfaction.get('total_ratings', 0)
                positive_ratings = satisfaction.get('positive_ratings', 0)
                satisfaction_rate = (positive_ratings * 100 / total_ratings) if total_ratings > 0 else 0
                
                return {
                    'period': {'start_date': start_date, 'end_date': end_date},
                    'conversations': {
                        'total': total_conversations,
                        'resolved': resolved_conversations,
                        'open': summary.get('open_conversations', 0),
                        'pending': summary.get('pending_conversations', 0),
                        'resolution_rate': round(resolution_rate, 2)
                    },
                    'response_time': {
                        'average_minutes': round(summary.get('avg_first_response_time_minutes', 0) or 0, 2)
                    },
                    'satisfaction': {
                        'total_ratings': total_ratings,
                        'average_rating': round(satisfaction.get('average_rating', 0) or 0, 2),
                        'positive_ratings': positive_ratings,
                        'negative_ratings': satisfaction.get('negative_ratings', 0),
                        'satisfaction_rate': round(satisfaction_rate, 2)
                    },
                    'agents': {
                        'active': summary.get('active_agents', 0)
                    }
                }
        except Exception as e:
            logger.error(f"Erro ao buscar resumo do dashboard: {e}")
            raise
    
    async def get_agents_list(self) -> List[Dict[str, Any]]:
        """Busca lista de agentes"""
        query = """
            SELECT id, name, email
            FROM users
            ORDER BY name
        """

        try:
            async with self.connection_pool.acquire() as conn:
                rows = await conn.fetch(query)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Erro ao buscar lista de agentes: {e}")
            raise


# Instância global do cliente
chatwoot_client = ChatwootClient()
