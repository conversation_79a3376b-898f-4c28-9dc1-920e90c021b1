"""
Cliente para comunicação com a API do 3CX
"""
import httpx
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, date
from pydantic import BaseModel
import logging

from config import settings

logger = logging.getLogger(__name__)


class ThreeCXRequest(BaseModel):
    """Modelo para requisições ao 3CX"""
    user: str
    password: str
    data_inicial: str
    data_final: str
    filas: List[int]
    relatorio: str


class ThreeCXClient:
    """Cliente para comunicação com a API do 3CX"""
    
    def __init__(self):
        self.base_url = settings.threecx_api_url
        self.user = settings.threecx_user
        self.password = settings.threecx_password
        self.timeout = 30.0
    
    async def _make_request(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Faz requisição para a API do 3CX"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    self.base_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                response.raise_for_status()
                return response.json()
        except httpx.TimeoutException:
            logger.error(f"Timeout na requisição para 3CX: {payload}")
            raise Exception("Timeout na comunicação com 3CX")
        except httpx.HTTPStatusError as e:
            logger.error(f"Erro HTTP na requisição para 3CX: {e.response.status_code}")
            raise Exception(f"Erro na API 3CX: {e.response.status_code}")
        except Exception as e:
            logger.error(f"Erro na requisição para 3CX: {str(e)}")
            raise Exception(f"Erro na comunicação com 3CX: {str(e)}")
    
    def _format_date(self, date_obj: date) -> str:
        """Formata data para o formato esperado pelo 3CX"""
        return date_obj.strftime("%Y-%m-%d")
    
    async def get_distribution_report(
        self, 
        start_date: date, 
        end_date: date, 
        queues: List[int]
    ) -> Dict[str, Any]:
        """Obtém relatório de distribuição"""
        payload = {
            "user": self.user,
            "password": self.password,
            "data_inicial": self._format_date(start_date),
            "data_final": self._format_date(end_date),
            "filas": queues,
            "relatorio": "Distribuição"
        }
        
        logger.info(f"Solicitando relatório de distribuição: {start_date} a {end_date}")
        return await self._make_request(payload)
    
    async def get_satisfaction_survey(
        self, 
        start_date: date, 
        end_date: date, 
        queues: List[int]
    ) -> Dict[str, Any]:
        """Obtém pesquisa de satisfação"""
        payload = {
            "user": self.user,
            "password": self.password,
            "data_inicial": self._format_date(start_date),
            "data_final": self._format_date(end_date),
            "filas": queues,
            "relatorio": "Pesquisa Satisfação"
        }
        
        logger.info(f"Solicitando pesquisa de satisfação: {start_date} a {end_date}")
        return await self._make_request(payload)
    
    async def get_timing_metrics(
        self, 
        start_date: date, 
        end_date: date, 
        queues: List[int]
    ) -> Dict[str, Any]:
        """Obtém métricas de timing (usando relatório de distribuição como base)"""
        # Para métricas de timing, usamos o relatório de distribuição
        # e extraímos as métricas de tempo
        return await self.get_distribution_report(start_date, end_date, queues)
    
    async def get_agent_performance(
        self, 
        start_date: date, 
        end_date: date, 
        queues: Optional[List[int]] = None
    ) -> Dict[str, Any]:
        """Obtém performance de agentes"""
        if queues is None:
            queues = settings.available_queues_list
        
        # Para performance de agentes, também usamos distribuição como base
        return await self.get_distribution_report(start_date, end_date, queues)
    
    async def test_connection(self) -> bool:
        """Testa conexão com a API do 3CX"""
        try:
            from datetime import date, timedelta
            today = date.today()
            yesterday = today - timedelta(days=1)
            
            await self.get_distribution_report(
                yesterday, 
                yesterday, 
                [settings.available_queues_list[0]]
            )
            return True
        except Exception as e:
            logger.error(f"Falha no teste de conexão: {str(e)}")
            return False


# Instância global do cliente
threecx_client = ThreeCXClient()
