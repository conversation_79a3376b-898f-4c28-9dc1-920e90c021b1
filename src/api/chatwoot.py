"""
API endpoints para métricas do Chatwoot
"""
from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import JSONResponse
from datetime import date, datetime, timedelta
from typing import Optional, Dict, Any
import logging

from src.services.chatwoot_metrics import chatwoot_metrics_service
from src.clients.chatwoot_client import chatwoot_client

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/chatwoot", tags=["Chatwoot Metrics"])


async def validate_date_range(
    start_date: date = Query(..., description="Data inicial (YYYY-MM-DD)"),
    end_date: date = Query(..., description="Data final (YYYY-MM-DD)")
) -> tuple[date, date]:
    """Valida intervalo de datas"""
    if start_date > end_date:
        raise HTTPException(
            status_code=400,
            detail="Data inicial deve ser anterior à data final"
        )
    
    # Limitar a 1 ano
    if (end_date - start_date).days > 365:
        raise HTTPException(
            status_code=400,
            detail="Período máximo permitido é de 1 ano"
        )
    
    return start_date, end_date


@router.get("/health")
async def health_check():
    """Verifica status da conexão com Chatwoot"""
    try:
        is_connected = await chatwoot_client.test_connection()
        return {
            "status": "healthy" if is_connected else "unhealthy",
            "service": "chatwoot",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Erro no health check: {e}")
        return {
            "status": "unhealthy",
            "service": "chatwoot",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@router.get("/dashboard")
async def get_dashboard_summary(
    dates: tuple[date, date] = Depends(validate_date_range)
):
    """Retorna resumo geral para o dashboard"""
    try:
        start_date, end_date = dates
        data = await chatwoot_metrics_service.get_dashboard_summary(start_date, end_date)

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "data": data,
                "message": "Dashboard carregado com sucesso"
            }
        )

    except Exception as e:
        logger.error(f"Erro ao buscar dashboard: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno do servidor: {str(e)}"
        )


@router.get("/satisfaction")
async def get_satisfaction_metrics(
    dates: tuple[date, date] = Depends(validate_date_range),
    agent_id: Optional[int] = Query(None, description="ID do agente (opcional)")
):
    """Retorna métricas de satisfação (CSAT/NPS)"""
    try:
        start_date, end_date = dates
        data = await chatwoot_metrics_service.get_satisfaction_metrics(start_date, end_date, agent_id)

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "data": data,
                "message": "Métricas de satisfação carregadas com sucesso"
            }
        )

    except Exception as e:
        logger.error(f"Erro ao buscar métricas de satisfação: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno do servidor: {str(e)}"
        )


@router.get("/response-time")
async def get_response_time_metrics(
    dates: tuple[date, date] = Depends(validate_date_range),
    agent_id: Optional[int] = Query(None, description="ID do agente (opcional)")
):
    """Retorna métricas de tempo de resposta"""
    try:
        start_date, end_date = dates
        data = await chatwoot_metrics_service.get_response_time_metrics(start_date, end_date, agent_id)

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "data": data,
                "message": "Métricas de tempo de resposta carregadas com sucesso"
            }
        )

    except Exception as e:
        logger.error(f"Erro ao buscar métricas de tempo de resposta: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno do servidor: {str(e)}"
        )


@router.get("/volume")
async def get_volume_metrics(
    dates: tuple[date, date] = Depends(validate_date_range),
    agent_id: Optional[int] = Query(None, description="ID do agente (opcional)")
):
    """Retorna métricas de volume de conversas"""
    try:
        start_date, end_date = dates
        data = await chatwoot_metrics_service.get_volume_metrics(start_date, end_date, agent_id)

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "data": data,
                "message": "Métricas de volume carregadas com sucesso"
            }
        )

    except Exception as e:
        logger.error(f"Erro ao buscar métricas de volume: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno do servidor: {str(e)}"
        )


@router.get("/performance")
async def get_performance_metrics(
    dates: tuple[date, date] = Depends(validate_date_range)
):
    """Retorna métricas de performance dos agentes"""
    try:
        start_date, end_date = dates
        data = await chatwoot_metrics_service.get_performance_metrics(start_date, end_date)

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "data": data,
                "message": "Métricas de performance carregadas com sucesso"
            }
        )

    except Exception as e:
        logger.error(f"Erro ao buscar métricas de performance: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno do servidor: {str(e)}"
        )


@router.get("/agents")
async def get_agents_list():
    """Retorna lista de agentes disponíveis"""
    try:
        agents = await chatwoot_metrics_service.get_agents_list()

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "data": agents,
                "message": "Lista de agentes carregada com sucesso"
            }
        )

    except Exception as e:
        logger.error(f"Erro ao buscar lista de agentes: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro interno do servidor: {str(e)}"

