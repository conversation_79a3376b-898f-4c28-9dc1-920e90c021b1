"""
API endpoints para métricas do Chatwoot
"""
from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import JSONResponse
from datetime import date, datetime, timedelta
from typing import Optional, Dict, Any
import logging

from src.services.chatwoot_metrics import chatwoot_metrics_service
from src.clients.chatwoot_client import chatwoot_client

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/chatwoot", tags=["Chatwoot Metrics"])


async def validate_date_range(
    start_date: date = Query(..., description="Data inicial (YYYY-MM-DD)"),
    end_date: date = Query(..., description="Data final (YYYY-MM-DD)")
) -> tuple[date, date]:
    """Valida intervalo de datas"""
    if start_date > end_date:
        raise HTTPException(
            status_code=400,
            detail="Data inicial deve ser anterior à data final"
        )
    
    # Limitar a 1 ano
    if (end_date - start_date).days > 365:
        raise HTTPException(
            status_code=400,
            detail="Período máximo permitido é de 1 ano"
        )
    
    return start_date, end_date


@router.get("/health")
async def health_check():
    """Verifica status da conexão com Chatwoot"""
    try:
        is_connected = await chatwoot_client.test_connection()
        return {
            "status": "healthy" if is_connected else "unhealthy",
            "service": "chatwoot",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Erro no health check: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": "chatwoot",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )


@router.get("/dashboard")
async def get_dashboard(
    dates: tuple[date, date] = Depends(validate_date_range)
) -> Dict[str, Any]:
    """Retorna dados para dashboard do Chatwoot"""
    start_date, end_date = dates
    
    try:
        data = await chatwoot_metrics_service.get_dashboard_data(start_date, end_date)
        return {
            "success": True,
            "data": data,
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        }
    except Exception as e:
        logger.error(f"Erro ao buscar dados do dashboard: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )


@router.get("/satisfaction")
async def get_satisfaction_metrics(
    dates: tuple[date, date] = Depends(validate_date_range),
    agent_id: Optional[int] = Query(None, description="ID do agente (opcional)")
) -> Dict[str, Any]:
    """Retorna métricas de satisfação"""
    start_date, end_date = dates
    
    try:
        report = await chatwoot_metrics_service.get_satisfaction_report(
            start_date, end_date, agent_id
        )
        return {
            "success": True,
            "data": report['data'],
            "summary": report['summary'],
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "agent_filter": agent_id
        }
    except Exception as e:
        logger.error(f"Erro ao buscar métricas de satisfação: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )


@router.get("/response-time")
async def get_response_time_metrics(
    dates: tuple[date, date] = Depends(validate_date_range),
    agent_id: Optional[int] = Query(None, description="ID do agente (opcional)")
) -> Dict[str, Any]:
    """Retorna métricas de tempo de resposta"""
    start_date, end_date = dates
    
    try:
        report = await chatwoot_metrics_service.get_response_time_report(
            start_date, end_date, agent_id
        )
        return {
            "success": True,
            "data": report['data'],
            "summary": report['summary'],
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "agent_filter": agent_id
        }
    except Exception as e:
        logger.error(f"Erro ao buscar métricas de tempo de resposta: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )


@router.get("/volume")
async def get_volume_metrics(
    dates: tuple[date, date] = Depends(validate_date_range)
) -> Dict[str, Any]:
    """Retorna métricas de volume de conversas"""
    start_date, end_date = dates
    
    try:
        report = await chatwoot_metrics_service.get_volume_report(start_date, end_date)
        return {
            "success": True,
            "data": report['data'],
            "summary": report['summary'],
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        }
    except Exception as e:
        logger.error(f"Erro ao buscar métricas de volume: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )


@router.get("/agents")
async def get_agents():
    """Retorna lista de agentes"""
    try:
        agents = await chatwoot_client.get_agents_list()
        return {
            "success": True,
            "data": agents,
            "total": len(agents)
        }
    except Exception as e:
        logger.error(f"Erro ao buscar lista de agentes: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )


@router.get("/performance")
async def get_performance_report(
    dates: tuple[date, date] = Depends(validate_date_range),
    agent_id: Optional[int] = Query(None, description="ID do agente (opcional)")
) -> Dict[str, Any]:
    """Retorna relatório completo de performance"""
    start_date, end_date = dates

    try:
        # Buscar dados de satisfação e tempo de resposta
        satisfaction_report = await chatwoot_metrics_service.get_satisfaction_report(
            start_date, end_date, agent_id
        )
        response_time_report = await chatwoot_metrics_service.get_response_time_report(
            start_date, end_date, agent_id
        )
        volume_report = await chatwoot_metrics_service.get_volume_report(start_date, end_date)

        # Combinar dados por agente
        combined_data = {}

        # Processar dados de satisfação
        for row in satisfaction_report['data']:
            agent_id_key = row['agent_id']
            combined_data[agent_id_key] = {
                'agent_id': agent_id_key,
                'agent_name': row['agent_name'],
                'total_ratings': row['total_ratings'],
                'average_rating': row['average_rating'],
                'positive_ratings': row['positive_ratings'],
                'negative_ratings': row['negative_ratings'],
                'satisfaction_percentage': row['satisfaction_percentage'],
                'satisfaction_rating': row['average_rating'],
                'total_conversations': 0,
                'avg_response_time_minutes': 0,
                'resolution_rate': 85,  # Valor padrão
                'performance_score': 0
            }

        # Processar dados de tempo de resposta
        for row in response_time_report['data']:
            agent_id_key = row['agent_id']
            if agent_id_key in combined_data:
                combined_data[agent_id_key].update({
                    'total_conversations': row['total_conversations'],
                    'avg_response_time_minutes': row['avg_first_response_time_minutes'],
                    'min_response_time_minutes': row['min_response_time_minutes'],
                    'max_response_time_minutes': row['max_response_time_minutes'],
                    'sla_compliance': calculate_sla_compliance(row['avg_first_response_time_minutes'])
                })
            else:
                combined_data[agent_id_key] = {
                    'agent_id': agent_id_key,
                    'agent_name': row['agent_name'],
                    'total_conversations': row['total_conversations'],
                    'avg_response_time_minutes': row['avg_first_response_time_minutes'],
                    'min_response_time_minutes': row['min_response_time_minutes'],
                    'max_response_time_minutes': row['max_response_time_minutes'],
                    'sla_compliance': calculate_sla_compliance(row['avg_first_response_time_minutes']),
                    'total_ratings': 0,
                    'average_rating': 0,
                    'positive_ratings': 0,
                    'negative_ratings': 0,
                    'satisfaction_percentage': 0,
                    'satisfaction_rating': 0,
                    'resolution_rate': 85,
                    'performance_score': 0
                }

        # Calcular performance score para cada agente
        for agent_data in combined_data.values():
            agent_data['performance_score'] = calculate_performance_score(agent_data)

        # Converter para lista e ordenar por performance
        performance_data = sorted(
            list(combined_data.values()),
            key=lambda x: x['performance_score'],
            reverse=True
        )

        # Calcular estatísticas de distribuição
        performance_distribution = calculate_performance_distribution(performance_data)

        # Calcular métricas resumo
        summary = {
            'total_agents': len(performance_data),
            'active_agents': len([a for a in performance_data if a['total_conversations'] > 0]),
            'avg_performance_score': sum(a['performance_score'] for a in performance_data) / len(performance_data) if performance_data else 0,
            'top_performer_score': performance_data[0]['performance_score'] if performance_data else 0
        }

        return {
            "success": True,
            "data": {
                "agents": performance_data,
                "summary": summary,
                "performance_distribution": performance_distribution
            },
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "agent_filter": agent_id
        }

    except Exception as e:
        logger.error(f"Erro ao buscar relatório de performance: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )


def calculate_sla_compliance(avg_response_time: float) -> int:
    """Calcula compliance de SLA baseado no tempo de resposta"""
    if avg_response_time <= 2:
        return 95
    elif avg_response_time <= 5:
        return 85
    elif avg_response_time <= 10:
        return 70
    else:
        return 50


def calculate_performance_score(agent_data: dict) -> float:
    """Calcula score de performance baseado em múltiplas métricas"""
    # Pesos para cada métrica
    weights = {
        'satisfaction': 0.3,
        'response_time': 0.25,
        'resolution': 0.25,
        'volume': 0.2
    }

    # Normalizar satisfação (0-5 para 0-10)
    satisfaction_score = (agent_data.get('satisfaction_rating', 0) / 5) * 10

    # Normalizar tempo de resposta (inverso, menor é melhor)
    response_time = agent_data.get('avg_response_time_minutes', 10)
    if response_time <= 2:
        response_time_score = 10
    elif response_time <= 5:
        response_time_score = 8
    elif response_time <= 10:
        response_time_score = 6
    else:
        response_time_score = 4

    # Normalizar taxa de resolução
    resolution_score = (agent_data.get('resolution_rate', 0) / 100) * 10

    # Normalizar volume (baseado em conversas)
    conversations = agent_data.get('total_conversations', 0)
    if conversations >= 100:
        volume_score = 10
    elif conversations >= 50:
        volume_score = 8
    elif conversations >= 20:
        volume_score = 6
    else:
        volume_score = 4

    # Calcular score final
    final_score = (
        satisfaction_score * weights['satisfaction'] +
        response_time_score * weights['response_time'] +
        resolution_score * weights['resolution'] +
        volume_score * weights['volume']
    )

    return round(final_score, 1)


def calculate_performance_distribution(agents: list) -> dict:
    """Calcula distribuição de performance"""
    distribution = {
        'excellent': 0,  # 8.5+
        'good': 0,       # 7.0-8.4
        'average': 0,    # 5.5-6.9
        'poor': 0        # <5.5
    }

    for agent in agents:
        score = agent['performance_score']
        if score >= 8.5:
            distribution['excellent'] += 1
        elif score >= 7.0:
            distribution['good'] += 1
        elif score >= 5.5:
            distribution['average'] += 1
        else:
            distribution['poor'] += 1

    return distribution


@router.get("/satisfaction/export")
async def export_satisfaction_data(
    dates: tuple[date, date] = Depends(validate_date_range),
    agent_id: Optional[int] = Query(None, description="ID do agente (opcional)"),
    format: str = Query("json", description="Formato de exportação (json, excel)")
) -> Dict[str, Any]:
    """Exporta dados de satisfação"""
    start_date, end_date = dates

    try:
        report = await chatwoot_metrics_service.get_satisfaction_report(
            start_date, end_date, agent_id
        )

        if format.lower() == "excel":
            # TODO: Implementar exportação Excel
            raise HTTPException(
                status_code=501,
                detail="Exportação Excel não implementada ainda"
            )

        return {
            "success": True,
            "format": format,
            "data": report,
            "exported_at": datetime.now().isoformat(),
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        }

    except Exception as e:
        logger.error(f"Erro ao exportar dados de satisfação: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )


@router.get("/response-time/export")
async def export_response_time_data(
    dates: tuple[date, date] = Depends(validate_date_range),
    agent_id: Optional[int] = Query(None, description="ID do agente (opcional)"),
    format: str = Query("json", description="Formato de exportação (json, excel)")
) -> Dict[str, Any]:
    """Exporta dados de tempo de resposta"""
    start_date, end_date = dates

    try:
        report = await chatwoot_metrics_service.get_response_time_report(
            start_date, end_date, agent_id
        )

        if format.lower() == "excel":
            # TODO: Implementar exportação Excel
            raise HTTPException(
                status_code=501,
                detail="Exportação Excel não implementada ainda"
            )

        return {
            "success": True,
            "format": format,
            "data": report,
            "exported_at": datetime.now().isoformat(),
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        }

    except Exception as e:
        logger.error(f"Erro ao exportar dados de tempo de resposta: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )


@router.get("/volume/export")
async def export_volume_data(
    dates: tuple[date, date] = Depends(validate_date_range),
    format: str = Query("json", description="Formato de exportação (json, excel)")
) -> Dict[str, Any]:
    """Exporta dados de volume"""
    start_date, end_date = dates

    try:
        report = await chatwoot_metrics_service.get_volume_report(start_date, end_date)

        if format.lower() == "excel":
            # TODO: Implementar exportação Excel
            raise HTTPException(
                status_code=501,
                detail="Exportação Excel não implementada ainda"
            )

        return {
            "success": True,
            "format": format,
            "data": report,
            "exported_at": datetime.now().isoformat(),
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        }

    except Exception as e:
        logger.error(f"Erro ao exportar dados de volume: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )


@router.get("/performance/export")
async def export_performance_data(
    dates: tuple[date, date] = Depends(validate_date_range),
    agent_id: Optional[int] = Query(None, description="ID do agente (opcional)"),
    format: str = Query("json", description="Formato de exportação (json, excel)")
) -> Dict[str, Any]:
    """Exporta dados de performance"""
    start_date, end_date = dates

    try:
        # Reutilizar a lógica do endpoint de performance
        performance_data = await get_performance_report(
            dates=(start_date, end_date),
            agent_id=agent_id
        )

        if format.lower() == "excel":
            # TODO: Implementar exportação Excel
            raise HTTPException(
                status_code=501,
                detail="Exportação Excel não implementada ainda"
            )

        return {
            "success": True,
            "format": format,
            "data": performance_data["data"],
            "exported_at": datetime.now().isoformat(),
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        }

    except Exception as e:
        logger.error(f"Erro ao exportar dados de performance: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )
