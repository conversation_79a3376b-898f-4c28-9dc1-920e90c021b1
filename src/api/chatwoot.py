"""
API endpoints para métricas do Chatwoot
"""
from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import JSONResponse
from datetime import date, datetime, timedelta
from typing import Optional, Dict, Any
import logging

from src.services.chatwoot_metrics import chatwoot_metrics_service
from src.clients.chatwoot_client import chatwoot_client

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/chatwoot", tags=["Chatwoot Metrics"])


async def validate_date_range(
    start_date: date = Query(..., description="Data inicial (YYYY-MM-DD)"),
    end_date: date = Query(..., description="Data final (YYYY-MM-DD)")
) -> tuple[date, date]:
    """Valida intervalo de datas"""
    if start_date > end_date:
        raise HTTPException(
            status_code=400,
            detail="Data inicial deve ser anterior à data final"
        )
    
    # Limitar a 1 ano
    if (end_date - start_date).days > 365:
        raise HTTPException(
            status_code=400,
            detail="Período máximo permitido é de 1 ano"
        )
    
    return start_date, end_date


@router.get("/health")
async def health_check():
    """Verifica status da conexão com Chatwoot"""
    try:
        is_connected = await chatwoot_client.test_connection()
        return {
            "status": "healthy" if is_connected else "unhealthy",
            "service": "chatwoot",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Erro no health check: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": "chatwoot",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )


@router.get("/dashboard")
async def get_dashboard(
    dates: tuple[date, date] = Depends(validate_date_range)
) -> Dict[str, Any]:
    """Retorna dados para dashboard do Chatwoot"""
    start_date, end_date = dates
    
    try:
        data = await chatwoot_metrics_service.get_dashboard_data(start_date, end_date)
        return {
            "success": True,
            "data": data,
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        }
    except Exception as e:
        logger.error(f"Erro ao buscar dados do dashboard: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )


@router.get("/satisfaction")
async def get_satisfaction_metrics(
    dates: tuple[date, date] = Depends(validate_date_range),
    agent_id: Optional[int] = Query(None, description="ID do agente (opcional)")
) -> Dict[str, Any]:
    """Retorna métricas de satisfação"""
    start_date, end_date = dates
    
    try:
        report = await chatwoot_metrics_service.get_satisfaction_report(
            start_date, end_date, agent_id
        )
        return {
            "success": True,
            "data": report['data'],
            "summary": report['summary'],
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "agent_filter": agent_id
        }
    except Exception as e:
        logger.error(f"Erro ao buscar métricas de satisfação: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )


@router.get("/response-time")
async def get_response_time_metrics(
    dates: tuple[date, date] = Depends(validate_date_range),
    agent_id: Optional[int] = Query(None, description="ID do agente (opcional)")
) -> Dict[str, Any]:
    """Retorna métricas de tempo de resposta"""
    start_date, end_date = dates
    
    try:
        report = await chatwoot_metrics_service.get_response_time_report(
            start_date, end_date, agent_id
        )
        return {
            "success": True,
            "data": report['data'],
            "summary": report['summary'],
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "agent_filter": agent_id
        }
    except Exception as e:
        logger.error(f"Erro ao buscar métricas de tempo de resposta: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )


@router.get("/volume")
async def get_volume_metrics(
    dates: tuple[date, date] = Depends(validate_date_range)
) -> Dict[str, Any]:
    """Retorna métricas de volume de conversas"""
    start_date, end_date = dates
    
    try:
        report = await chatwoot_metrics_service.get_volume_report(start_date, end_date)
        return {
            "success": True,
            "data": report['data'],
            "summary": report['summary'],
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        }
    except Exception as e:
        logger.error(f"Erro ao buscar métricas de volume: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )


@router.get("/agents")
async def get_agents():
    """Retorna lista de agentes"""
    try:
        agents = await chatwoot_client.get_agents_list()
        return {
            "success": True,
            "data": agents,
            "total": len(agents)
        }
    except Exception as e:
        logger.error(f"Erro ao buscar lista de agentes: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )


@router.get("/performance")
async def get_performance_report(
    dates: tuple[date, date] = Depends(validate_date_range),
    agent_id: Optional[int] = Query(None, description="ID do agente (opcional)")
) -> Dict[str, Any]:
    """Retorna relatório completo de performance"""
    start_date, end_date = dates
    
    try:
        # Buscar dados de satisfação e tempo de resposta
        satisfaction_report = await chatwoot_metrics_service.get_satisfaction_report(
            start_date, end_date, agent_id
        )
        response_time_report = await chatwoot_metrics_service.get_response_time_report(
            start_date, end_date, agent_id
        )
        
        # Combinar dados por agente
        combined_data = {}
        
        # Processar dados de satisfação
        for row in satisfaction_report['data']:
            agent_id_key = row['agent_id']
            combined_data[agent_id_key] = {
                'agent_id': agent_id_key,
                'agent_name': row['agent_name'],
                'satisfaction': {
                    'total_ratings': row['total_ratings'],
                    'average_rating': row['average_rating'],
                    'positive_ratings': row['positive_ratings'],
                    'negative_ratings': row['negative_ratings'],
                    'satisfaction_percentage': row['satisfaction_percentage']
                }
            }
        
        # Processar dados de tempo de resposta
        for row in response_time_report['data']:
            agent_id_key = row['agent_id']
            if agent_id_key in combined_data:
                combined_data[agent_id_key]['response_time'] = {
                    'total_conversations': row['total_conversations'],
                    'avg_first_response_time_minutes': row['avg_first_response_time_minutes'],
                    'min_response_time_minutes': row['min_response_time_minutes'],
                    'max_response_time_minutes': row['max_response_time_minutes']
                }
            else:
                combined_data[agent_id_key] = {
                    'agent_id': agent_id_key,
                    'agent_name': row['agent_name'],
                    'satisfaction': {
                        'total_ratings': 0,
                        'average_rating': 0,
                        'positive_ratings': 0,
                        'negative_ratings': 0,
                        'satisfaction_percentage': 0
                    },
                    'response_time': {
                        'total_conversations': row['total_conversations'],
                        'avg_first_response_time_minutes': row['avg_first_response_time_minutes'],
                        'min_response_time_minutes': row['min_response_time_minutes'],
                        'max_response_time_minutes': row['max_response_time_minutes']
                    }
                }
        
        # Converter para lista
        performance_data = list(combined_data.values())
        
        return {
            "success": True,
            "data": performance_data,
            "summary": {
                "satisfaction": satisfaction_report['summary'],
                "response_time": response_time_report['summary']
            },
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "agent_filter": agent_id
        }
        
    except Exception as e:
        logger.error(f"Erro ao buscar relatório de performance: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )


@router.get("/export/satisfaction")
async def export_satisfaction_data(
    dates: tuple[date, date] = Depends(validate_date_range),
    agent_id: Optional[int] = Query(None, description="ID do agente (opcional)"),
    format: str = Query("json", description="Formato de exportação (json, csv)")
) -> Dict[str, Any]:
    """Exporta dados de satisfação"""
    start_date, end_date = dates
    
    try:
        report = await chatwoot_metrics_service.get_satisfaction_report(
            start_date, end_date, agent_id
        )
        
        if format.lower() == "csv":
            # TODO: Implementar exportação CSV
            raise HTTPException(
                status_code=501,
                detail="Exportação CSV não implementada ainda"
            )
        
        return {
            "success": True,
            "format": format,
            "data": report['data'],
            "summary": report['summary'],
            "exported_at": datetime.now().isoformat(),
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Erro ao exportar dados de satisfação: {e}")
        raise HTTPException(
            status_code=500,
            detail="Erro interno do servidor"
        )
