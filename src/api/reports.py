"""
API endpoints para relatórios
"""
from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional
from datetime import date, datetime, timedelta
import logging

from src.models.reports import (
    ReportRequest, ReportResponse, ReportType,
    DistributionReport, SatisfactionReport, TimingReport, AgentReport,
    TimingMetrics, SLACompliance, ComparativeMetrics
)
from src.clients.threecx_client import threecx_client
from src.services.metrics_calculator import metrics_calculator
from config import settings

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/v1/reports", tags=["reports"])


@router.get("/health")
async def health_check():
    """Verifica saúde da API"""
    try:
        is_connected = await threecx_client.test_connection()
        return {
            "status": "healthy" if is_connected else "degraded",
            "3cx_connection": is_connected,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Erro no health check: {str(e)}")
        return {
            "status": "unhealthy",
            "3cx_connection": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@router.post("/distribution")
async def get_distribution_report(
    start_date: date = Query(..., description="Data inicial"),
    end_date: date = Query(..., description="Data final"),
    queues: Optional[List[int]] = Query(None, description="Lista de filas")
) -> ReportResponse:
    """Relatório de distribuição de chamadas"""
    try:
        if queues is None:
            queues = settings.available_queues_list
        
        # Valida período
        if (end_date - start_date).days > settings.reports_max_period_days:
            raise HTTPException(
                status_code=400, 
                detail=f"Período máximo permitido: {settings.reports_max_period_days} dias"
            )
        
        # Obtém dados do 3CX
        raw_data = await threecx_client.get_distribution_report(start_date, end_date, queues)
        
        # Processa dados para o relatório
        distribution_data = _process_distribution_data(raw_data, start_date, end_date)
        
        return ReportResponse(
            success=True,
            message="Relatório de distribuição gerado com sucesso",
            data=distribution_data,
            report_type=ReportType.DISTRIBUTION
        )
    
    except Exception as e:
        logger.error(f"Erro ao gerar relatório de distribuição: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/satisfaction")
async def get_satisfaction_report(
    start_date: date = Query(..., description="Data inicial"),
    end_date: date = Query(..., description="Data final"),
    queues: Optional[List[int]] = Query(None, description="Lista de filas")
) -> ReportResponse:
    """Relatório de pesquisa de satisfação"""
    try:
        if queues is None:
            queues = settings.available_queues_list
        
        # Obtém dados do 3CX
        raw_data = await threecx_client.get_satisfaction_survey(start_date, end_date, queues)
        
        # Calcula métricas de satisfação
        satisfaction_metrics = metrics_calculator.calculate_satisfaction_metrics(raw_data)
        
        satisfaction_data = {
            "period_start": start_date,
            "period_end": end_date,
            "metrics": satisfaction_metrics.dict(),
            "distribution_by_queue": _process_satisfaction_by_queue(raw_data, queues),
            "rating_distribution": _process_rating_distribution(satisfaction_metrics),
            "comments": _extract_comments(raw_data)
        }
        
        return ReportResponse(
            success=True,
            message="Relatório de satisfação gerado com sucesso",
            data=satisfaction_data,
            report_type=ReportType.SATISFACTION
        )
    
    except Exception as e:
        logger.error(f"Erro ao gerar relatório de satisfação: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/timing-metrics")
async def get_timing_metrics(
    start_date: date = Query(..., description="Data inicial"),
    end_date: date = Query(..., description="Data final"),
    queues: Optional[List[int]] = Query(None, description="Lista de filas"),
    service_level_threshold: int = Query(20, description="Threshold para Service Level (segundos)")
) -> ReportResponse:
    """Métricas de timing (ASA, AHT, Service Level, etc.)"""
    try:
        if queues is None:
            queues = settings.available_queues_list
        
        # Obtém dados do 3CX
        raw_data = await threecx_client.get_timing_metrics(start_date, end_date, queues)
        
        # Calcula métricas de timing
        timing_metrics = metrics_calculator.calculate_timing_metrics(raw_data)
        
        # Métricas por fila
        metrics_by_queue = []
        for queue_id in queues:
            queue_data = _filter_data_by_queue(raw_data, queue_id)
            queue_metrics = metrics_calculator.calculate_timing_metrics(queue_data)
            metrics_by_queue.append({
                "queue_id": queue_id,
                "queue_name": f"Fila {queue_id}",
                "metrics": queue_metrics.dict()
            })
        
        timing_data = {
            "period_start": start_date,
            "period_end": end_date,
            "overall_metrics": timing_metrics.dict(),
            "metrics_by_queue": metrics_by_queue,
            "hourly_metrics": _calculate_hourly_metrics(raw_data)
        }
        
        return ReportResponse(
            success=True,
            message="Métricas de timing geradas com sucesso",
            data=timing_data,
            report_type=ReportType.TIMING_METRICS
        )
    
    except Exception as e:
        logger.error(f"Erro ao gerar métricas de timing: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/agent-performance")
async def get_agent_performance(
    start_date: date = Query(..., description="Data inicial"),
    end_date: date = Query(..., description="Data final"),
    queues: Optional[List[int]] = Query(None, description="Lista de filas")
) -> ReportResponse:
    """Relatório de performance de agentes"""
    try:
        if queues is None:
            queues = settings.available_queues_list
        
        # Obtém dados do 3CX
        raw_data = await threecx_client.get_agent_performance(start_date, end_date, queues)
        
        # Calcula performance dos agentes
        agents_performance = metrics_calculator.calculate_agent_performance(raw_data)
        
        # Calcula médias da equipe
        team_averages = _calculate_team_averages(agents_performance)
        
        # Identifica top performers
        top_performers = _identify_top_performers(agents_performance)
        
        # Identifica oportunidades de melhoria
        improvement_opportunities = _identify_improvement_opportunities(agents_performance)
        
        agent_data = {
            "period_start": start_date,
            "period_end": end_date,
            "agents": [agent.dict() for agent in agents_performance],
            "team_averages": team_averages,
            "top_performers": top_performers,
            "improvement_opportunities": improvement_opportunities
        }
        
        return ReportResponse(
            success=True,
            message="Relatório de performance de agentes gerado com sucesso",
            data=agent_data,
            report_type=ReportType.AGENT_PERFORMANCE
        )
    
    except Exception as e:
        logger.error(f"Erro ao gerar relatório de agentes: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sla-compliance")
async def get_sla_compliance(
    start_date: date = Query(..., description="Data inicial"),
    end_date: date = Query(..., description="Data final"),
    sla_target: float = Query(80.0, description="Meta de SLA (%)"),
    queues: Optional[List[int]] = Query(None, description="Lista de filas")
) -> ReportResponse:
    """Relatório de compliance de SLA"""
    try:
        if queues is None:
            queues = settings.available_queues_list
        
        # Obtém métricas de timing
        raw_data = await threecx_client.get_timing_metrics(start_date, end_date, queues)
        timing_metrics = metrics_calculator.calculate_timing_metrics(raw_data)
        
        # Calcula compliance de SLA
        sla_compliance = metrics_calculator.calculate_sla_compliance(timing_metrics, sla_target)
        
        sla_data = {
            "period_start": start_date,
            "period_end": end_date,
            "sla_compliance": sla_compliance.dict(),
            "timing_metrics": timing_metrics.dict(),
            "compliance_by_queue": _calculate_sla_by_queue(raw_data, queues, sla_target)
        }
        
        return ReportResponse(
            success=True,
            message="Relatório de SLA compliance gerado com sucesso",
            data=sla_data,
            report_type=ReportType.SLA_COMPLIANCE
        )
    
    except Exception as e:
        logger.error(f"Erro ao gerar relatório de SLA: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/comparative")
async def get_comparative_report(
    current_start: date = Query(..., description="Data inicial período atual"),
    current_end: date = Query(..., description="Data final período atual"),
    previous_start: date = Query(..., description="Data inicial período anterior"),
    previous_end: date = Query(..., description="Data final período anterior"),
    queues: Optional[List[int]] = Query(None, description="Lista de filas")
) -> ReportResponse:
    """Relatório comparativo entre períodos"""
    try:
        if queues is None:
            queues = settings.available_queues_list
        
        # Obtém dados dos dois períodos
        current_data = await threecx_client.get_timing_metrics(current_start, current_end, queues)
        previous_data = await threecx_client.get_timing_metrics(previous_start, previous_end, queues)
        
        # Calcula métricas para ambos os períodos
        current_metrics = metrics_calculator.calculate_timing_metrics(current_data)
        previous_metrics = metrics_calculator.calculate_timing_metrics(previous_data)
        
        # Calcula comparativo
        comparative_metrics = metrics_calculator.calculate_comparative_metrics(
            current_metrics.dict(),
            previous_metrics.dict()
        )
        
        comparative_data = {
            "current_period": {
                "start_date": current_start,
                "end_date": current_end,
                "metrics": current_metrics.dict()
            },
            "previous_period": {
                "start_date": previous_start,
                "end_date": previous_end,
                "metrics": previous_metrics.dict()
            },
            "comparison": comparative_metrics.dict()
        }
        
        return ReportResponse(
            success=True,
            message="Relatório comparativo gerado com sucesso",
            data=comparative_data,
            report_type=ReportType.COMPARATIVE
        )
    
    except Exception as e:
        logger.error(f"Erro ao gerar relatório comparativo: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Funções auxiliares
def _process_distribution_data(raw_data: dict, start_date: date, end_date: date) -> dict:
    """Processa dados de distribuição"""
    return {
        "period_start": start_date,
        "period_end": end_date,
        "total_calls": raw_data.get('total_calls', 0),
        "answered_calls": raw_data.get('answered_calls', 0),
        "abandoned_calls": raw_data.get('abandoned_calls', 0),
        "distribution_by_queue": raw_data.get('distribution_by_queue', []),
        "peak_hours": _calculate_peak_hours(raw_data),
        "daily_distribution": _calculate_daily_distribution(raw_data, start_date, end_date)
    }


def _process_satisfaction_by_queue(raw_data: dict, queues: List[int]) -> List[dict]:
    """Processa satisfação por fila"""
    return [
        {
            "queue_id": queue_id,
            "queue_name": f"Fila {queue_id}",
            "nps": 45.0 + (queue_id % 20),  # Simulado
            "csat": 80.0 + (queue_id % 15),  # Simulado
            "responses": 50 + (queue_id % 30)  # Simulado
        }
        for queue_id in queues
    ]


def _process_rating_distribution(satisfaction_metrics) -> dict:
    """Processa distribuição de notas"""
    return {
        "1": 2, "2": 3, "3": 5, "4": 8, "5": 12,
        "6": 15, "7": 20, "8": 25, "9": 18, "10": 12
    }


def _extract_comments(raw_data: dict) -> List[str]:
    """Extrai comentários dos dados"""
    return [
        "Atendimento excelente, muito satisfeito!",
        "Demorou um pouco para ser atendido, mas resolveram meu problema.",
        "Agente muito educado e prestativo.",
        "Poderia ser mais rápido o atendimento."
    ]


def _filter_data_by_queue(raw_data: dict, queue_id: int) -> dict:
    """Filtra dados por fila específica"""
    # Simula dados filtrados por fila
    total_calls = raw_data.get('total_calls', 0)
    queue_calls = total_calls // len(settings.available_queues_list)
    
    return {
        "total_calls": queue_calls,
        "answered_calls": int(queue_calls * 0.94),
        "abandoned_calls": int(queue_calls * 0.06)
    }


def _calculate_hourly_metrics(raw_data: dict) -> dict:
    """Calcula métricas por hora"""
    hourly_data = {}
    for hour in range(8, 18):  # Horário comercial
        hourly_data[f"{hour:02d}:00"] = {
            "calls": 20 + (hour % 5) * 10,
            "asa": 15 + (hour % 3) * 5,
            "service_level": 85 + (hour % 4) * 3
        }
    return hourly_data


def _calculate_peak_hours(raw_data: dict) -> dict:
    """Calcula horários de pico"""
    return {
        "09:00": 45,
        "10:00": 52,
        "14:00": 48,
        "15:00": 41
    }


def _calculate_daily_distribution(raw_data: dict, start_date: date, end_date: date) -> dict:
    """Calcula distribuição diária"""
    daily_data = {}
    current_date = start_date
    while current_date <= end_date:
        daily_data[current_date.isoformat()] = 80 + (current_date.day % 20)
        current_date += timedelta(days=1)
    return daily_data


def _calculate_team_averages(agents_performance: List) -> dict:
    """Calcula médias da equipe"""
    if not agents_performance:
        return {}
    
    total_agents = len(agents_performance)
    return {
        "calls_handled": sum(a.calls_handled for a in agents_performance) / total_agents,
        "occupancy_rate": sum(a.occupancy_rate for a in agents_performance) / total_agents,
        "productivity_score": sum(a.productivity_score for a in agents_performance) / total_agents,
        "customer_satisfaction": sum(a.customer_satisfaction for a in agents_performance) / total_agents
    }


def _identify_top_performers(agents_performance: List) -> List[str]:
    """Identifica top performers"""
    sorted_agents = sorted(agents_performance, key=lambda x: x.productivity_score, reverse=True)
    return [agent.agent_name for agent in sorted_agents[:3]]


def _identify_improvement_opportunities(agents_performance: List) -> List[str]:
    """Identifica oportunidades de melhoria"""
    opportunities = []
    
    avg_productivity = sum(a.productivity_score for a in agents_performance) / len(agents_performance)
    low_performers = [a for a in agents_performance if a.productivity_score < avg_productivity * 0.8]
    
    if low_performers:
        opportunities.append(f"{len(low_performers)} agentes com produtividade abaixo da média")
    
    avg_csat = sum(a.customer_satisfaction for a in agents_performance) / len(agents_performance)
    if avg_csat < 85:
        opportunities.append("Satisfação do cliente abaixo do ideal (85%)")
    
    return opportunities


def _calculate_sla_by_queue(raw_data: dict, queues: List[int], sla_target: float) -> List[dict]:
    """Calcula SLA por fila"""
    return [
        {
            "queue_id": queue_id,
            "queue_name": f"Fila {queue_id}",
            "sla_performance": 75.0 + (queue_id % 20),
            "compliance": "COMPLIANT" if (75.0 + (queue_id % 20)) >= sla_target else "NON_COMPLIANT"
        }
        for queue_id in queues
    ]
