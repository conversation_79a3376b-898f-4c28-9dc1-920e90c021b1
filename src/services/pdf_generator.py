"""
Gerador de relatórios em PDF com tabelas e gráficos
"""
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.graphics.shapes import Drawing
from reportlab.graphics.charts.piecharts import Pie
from reportlab.graphics.charts.barcharts import VerticalBarChart
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
import plotly.graph_objects as go
import plotly.io as pio
from datetime import datetime, date
from typing import Dict, List, Any, Optional
import tempfile
import os
import logging

from config import settings
from src.services.chart_generator import chart_generator

logger = logging.getLogger(__name__)


class PDFGenerator:
    """Gerador de relatórios em PDF"""
    
    def __init__(self):
        self.page_size = A4
        self.margin = 2*cm
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Configura estilos customizados"""
        # Título principal
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#2c3e50')
        ))
        
        # Subtítulo
        self.styles.add(ParagraphStyle(
            name='CustomSubtitle',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=20,
            textColor=colors.HexColor('#34495e')
        ))
        
        # Texto de métricas
        self.styles.add(ParagraphStyle(
            name='MetricText',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=10,
            textColor=colors.HexColor('#2c3e50')
        ))
        
        # Cabeçalho de seção
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading3'],
            fontSize=14,
            spaceAfter=15,
            textColor=colors.HexColor('#3498db'),
            borderWidth=1,
            borderColor=colors.HexColor('#3498db'),
            borderPadding=5
        ))
    
    def generate_distribution_report(self, data: Dict[str, Any], output_path: str) -> str:
        """Gera relatório de distribuição em PDF"""
        try:
            doc = SimpleDocTemplate(
                output_path,
                pagesize=self.page_size,
                rightMargin=self.margin,
                leftMargin=self.margin,
                topMargin=self.margin,
                bottomMargin=self.margin
            )
            
            story = []
            
            # Cabeçalho
            story.extend(self._create_header("Relatório de Distribuição de Chamadas"))
            
            # Período
            period_text = f"Período: {data.get('period_start')} a {data.get('period_end')}"
            story.append(Paragraph(period_text, self.styles['MetricText']))
            story.append(Spacer(1, 20))
            
            # Resumo executivo
            story.extend(self._create_distribution_summary(data))
            
            # Tabela de distribuição por fila
            story.extend(self._create_distribution_table(data))
            
            # Gráfico de distribuição
            story.extend(self._create_chart_section(data, 'distribution'))
            
            # Análise por horário
            story.extend(self._create_hourly_analysis(data))
            
            # Rodapé
            story.extend(self._create_footer())
            
            doc.build(story)
            return output_path
        
        except Exception as e:
            logger.error(f"Erro ao gerar PDF de distribuição: {str(e)}")
            raise Exception(f"Falha na geração do PDF: {str(e)}")
    
    def generate_timing_report(self, data: Dict[str, Any], output_path: str) -> str:
        """Gera relatório de métricas de timing em PDF"""
        try:
            doc = SimpleDocTemplate(output_path, pagesize=self.page_size)
            story = []
            
            # Cabeçalho
            story.extend(self._create_header("Relatório de Métricas de Timing"))
            
            # Período
            period_text = f"Período: {data.get('period_start')} a {data.get('period_end')}"
            story.append(Paragraph(period_text, self.styles['MetricText']))
            story.append(Spacer(1, 20))
            
            # Métricas principais
            story.extend(self._create_timing_metrics_section(data))
            
            # Tabela de métricas por fila
            story.extend(self._create_timing_by_queue_table(data))
            
            # Gráficos
            story.extend(self._create_chart_section(data, 'timing'))
            
            # Análise de SLA
            story.extend(self._create_sla_analysis(data))
            
            story.extend(self._create_footer())
            
            doc.build(story)
            return output_path
        
        except Exception as e:
            logger.error(f"Erro ao gerar PDF de timing: {str(e)}")
            raise Exception(f"Falha na geração do PDF: {str(e)}")
    
    def generate_satisfaction_report(self, data: Dict[str, Any], output_path: str) -> str:
        """Gera relatório de satisfação em PDF"""
        try:
            doc = SimpleDocTemplate(output_path, pagesize=self.page_size)
            story = []
            
            # Cabeçalho
            story.extend(self._create_header("Relatório de Pesquisa de Satisfação"))
            
            # Período
            period_text = f"Período: {data.get('period_start')} a {data.get('period_end')}"
            story.append(Paragraph(period_text, self.styles['MetricText']))
            story.append(Spacer(1, 20))
            
            # Métricas de satisfação
            story.extend(self._create_satisfaction_metrics_section(data))
            
            # Distribuição NPS
            story.extend(self._create_nps_distribution_table(data))
            
            # Gráficos
            story.extend(self._create_chart_section(data, 'satisfaction'))
            
            # Comentários dos clientes
            story.extend(self._create_comments_section(data))
            
            story.extend(self._create_footer())
            
            doc.build(story)
            return output_path
        
        except Exception as e:
            logger.error(f"Erro ao gerar PDF de satisfação: {str(e)}")
            raise Exception(f"Falha na geração do PDF: {str(e)}")
    
    def generate_agent_report(self, data: Dict[str, Any], output_path: str) -> str:
        """Gera relatório de performance de agentes em PDF"""
        try:
            doc = SimpleDocTemplate(output_path, pagesize=self.page_size)
            story = []
            
            # Cabeçalho
            story.extend(self._create_header("Relatório de Performance de Agentes"))
            
            # Período
            period_text = f"Período: {data.get('period_start')} a {data.get('period_end')}"
            story.append(Paragraph(period_text, self.styles['MetricText']))
            story.append(Spacer(1, 20))
            
            # Resumo da equipe
            story.extend(self._create_team_summary(data))
            
            # Tabela de performance individual
            story.extend(self._create_agent_performance_table(data))
            
            # Top performers
            story.extend(self._create_top_performers_section(data))
            
            # Gráficos
            story.extend(self._create_chart_section(data, 'agents'))
            
            # Oportunidades de melhoria
            story.extend(self._create_improvement_opportunities(data))
            
            story.extend(self._create_footer())
            
            doc.build(story)
            return output_path
        
        except Exception as e:
            logger.error(f"Erro ao gerar PDF de agentes: {str(e)}")
            raise Exception(f"Falha na geração do PDF: {str(e)}")
    
    def generate_comprehensive_report(self, all_data: Dict[str, Any], output_path: str) -> str:
        """Gera relatório completo com todas as métricas"""
        try:
            doc = SimpleDocTemplate(output_path, pagesize=self.page_size)
            story = []
            
            # Capa
            story.extend(self._create_cover_page(all_data))
            story.append(PageBreak())
            
            # Índice
            story.extend(self._create_table_of_contents())
            story.append(PageBreak())
            
            # Resumo executivo
            story.extend(self._create_executive_summary(all_data))
            story.append(PageBreak())
            
            # Seção de distribuição
            if 'distribution' in all_data:
                story.extend(self._create_distribution_section(all_data['distribution']))
                story.append(PageBreak())
            
            # Seção de timing
            if 'timing' in all_data:
                story.extend(self._create_timing_section(all_data['timing']))
                story.append(PageBreak())
            
            # Seção de satisfação
            if 'satisfaction' in all_data:
                story.extend(self._create_satisfaction_section(all_data['satisfaction']))
                story.append(PageBreak())
            
            # Seção de agentes
            if 'agents' in all_data:
                story.extend(self._create_agents_section(all_data['agents']))
                story.append(PageBreak())
            
            # Conclusões e recomendações
            story.extend(self._create_conclusions_section(all_data))
            
            doc.build(story)
            return output_path
        
        except Exception as e:
            logger.error(f"Erro ao gerar relatório completo: {str(e)}")
            raise Exception(f"Falha na geração do PDF: {str(e)}")
    
    # Métodos auxiliares para criação de seções
    def _create_header(self, title: str) -> List:
        """Cria cabeçalho do relatório"""
        elements = []
        
        # Logo (se disponível)
        if os.path.exists(settings.pdf_logo_path):
            try:
                logo = Image(settings.pdf_logo_path, width=2*inch, height=1*inch)
                elements.append(logo)
                elements.append(Spacer(1, 20))
            except:
                pass
        
        # Título
        elements.append(Paragraph(title, self.styles['CustomTitle']))
        
        # Informações da empresa
        company_info = f"{settings.pdf_company_name}<br/>{settings.pdf_company_address}"
        elements.append(Paragraph(company_info, self.styles['Normal']))
        elements.append(Spacer(1, 30))
        
        return elements
    
    def _create_distribution_summary(self, data: Dict[str, Any]) -> List:
        """Cria resumo da distribuição"""
        elements = []
        elements.append(Paragraph("Resumo Executivo", self.styles['SectionHeader']))
        
        total_calls = data.get('total_calls', 0)
        answered_calls = data.get('answered_calls', 0)
        abandoned_calls = data.get('abandoned_calls', 0)
        
        summary_data = [
            ['Métrica', 'Valor'],
            ['Total de Chamadas', f'{total_calls:,}'],
            ['Chamadas Atendidas', f'{answered_calls:,}'],
            ['Chamadas Abandonadas', f'{abandoned_calls:,}'],
            ['Taxa de Atendimento', f'{(answered_calls/total_calls*100):.1f}%' if total_calls > 0 else '0%']
        ]
        
        table = Table(summary_data, colWidths=[3*inch, 2*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3498db')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        elements.append(table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def _create_distribution_table(self, data: Dict[str, Any]) -> List:
        """Cria tabela de distribuição por fila"""
        elements = []
        elements.append(Paragraph("Distribuição por Fila", self.styles['SectionHeader']))
        
        queues_data = data.get('distribution_by_queue', [])
        
        table_data = [['Fila', 'Nome', 'Chamadas', 'Percentual']]
        
        for queue in queues_data:
            table_data.append([
                str(queue.get('queue_id', 'N/A')),
                queue.get('queue_name', 'N/A'),
                f"{queue.get('calls', 0):,}",
                f"{queue.get('percentage', 0):.1f}%"
            ])
        
        table = Table(table_data, colWidths=[1*inch, 2*inch, 1.5*inch, 1.5*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#34495e')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        elements.append(table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def _create_chart_section(self, data: Dict[str, Any], chart_type: str) -> List:
        """Cria seção com gráfico"""
        elements = []
        elements.append(Paragraph("Visualizações", self.styles['SectionHeader']))
        
        try:
            # Gera gráfico usando Plotly e salva como imagem
            if chart_type == 'distribution':
                chart_html = chart_generator.create_distribution_chart(data)
            elif chart_type == 'timing':
                chart_html = chart_generator.create_timing_metrics_chart(data)
            elif chart_type == 'satisfaction':
                chart_html = chart_generator.create_satisfaction_chart(data)
            elif chart_type == 'agents':
                chart_html = chart_generator.create_agent_performance_chart(data)
            else:
                chart_html = None
            
            if chart_html:
                # Nota: Em produção, você converteria o HTML do gráfico para imagem
                # Por agora, adicionamos um placeholder
                elements.append(Paragraph("Gráfico gerado (visualizar na versão web)", self.styles['Normal']))
            
        except Exception as e:
            logger.error(f"Erro ao criar gráfico no PDF: {str(e)}")
            elements.append(Paragraph("Erro ao gerar gráfico", self.styles['Normal']))
        
        elements.append(Spacer(1, 20))
        return elements
    
    def _create_footer(self) -> List:
        """Cria rodapé do relatório"""
        elements = []
        elements.append(Spacer(1, 30))
        
        footer_text = f"Relatório gerado em {datetime.now().strftime('%d/%m/%Y às %H:%M')}"
        elements.append(Paragraph(footer_text, self.styles['Normal']))
        
        return elements
    
    def _create_timing_metrics_section(self, data: Dict[str, Any]) -> List:
        """Cria seção de métricas de timing"""
        elements = []
        elements.append(Paragraph("Métricas de Timing", self.styles['SectionHeader']))
        
        metrics = data.get('overall_metrics', {})
        
        metrics_data = [
            ['Métrica', 'Valor', 'Descrição'],
            ['ASA', f"{metrics.get('asa', 0):.1f}s", 'Tempo médio para atender'],
            ['AHT', f"{metrics.get('aht', 0):.1f}s", 'Tempo médio de atendimento'],
            ['ATT', f"{metrics.get('att', 0):.1f}s", 'Tempo médio de conversa'],
            ['ACW', f"{metrics.get('acw', 0):.1f}s", 'Tempo pós-chamada'],
            ['Service Level', f"{metrics.get('service_level', 0):.1f}%", '% chamadas atendidas no prazo'],
            ['Taxa de Abandono', f"{metrics.get('abandonment_rate', 0):.1f}%", '% chamadas abandonadas']
        ]
        
        table = Table(metrics_data, colWidths=[2*inch, 1.5*inch, 2.5*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2ecc71')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightgreen),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        elements.append(table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def _create_cover_page(self, data: Dict[str, Any]) -> List:
        """Cria página de capa"""
        elements = []
        
        # Espaço inicial
        elements.append(Spacer(1, 2*inch))
        
        # Título principal
        title = "Relatório Completo de Métricas de Pós-Venda"
        elements.append(Paragraph(title, self.styles['CustomTitle']))
        elements.append(Spacer(1, 1*inch))
        
        # Informações do relatório
        info_text = f"""
        <b>Empresa:</b> {settings.pdf_company_name}<br/>
        <b>Data de Geração:</b> {datetime.now().strftime('%d/%m/%Y')}<br/>
        <b>Período Analisado:</b> {data.get('period_start', 'N/A')} a {data.get('period_end', 'N/A')}<br/>
        <b>Versão:</b> 2.1.0
        """
        elements.append(Paragraph(info_text, self.styles['Normal']))
        
        return elements
    
    def _create_table_of_contents(self) -> List:
        """Cria índice do relatório"""
        elements = []
        elements.append(Paragraph("Índice", self.styles['CustomTitle']))
        elements.append(Spacer(1, 20))
        
        toc_items = [
            "1. Resumo Executivo",
            "2. Distribuição de Chamadas",
            "3. Métricas de Timing",
            "4. Pesquisa de Satisfação",
            "5. Performance de Agentes",
            "6. Conclusões e Recomendações"
        ]
        
        for item in toc_items:
            elements.append(Paragraph(item, self.styles['Normal']))
            elements.append(Spacer(1, 10))
        
        return elements
    
    def _create_executive_summary(self, data: Dict[str, Any]) -> List:
        """Cria resumo executivo"""
        elements = []
        elements.append(Paragraph("Resumo Executivo", self.styles['CustomTitle']))
        elements.append(Spacer(1, 20))
        
        summary_text = """
        Este relatório apresenta uma análise completa das métricas de pós-venda, 
        incluindo distribuição de chamadas, tempos de atendimento, satisfação do cliente 
        e performance individual dos agentes. Os dados foram coletados do sistema 3CX 
        e processados para fornecer insights acionáveis para melhoria contínua do atendimento.
        """
        
        elements.append(Paragraph(summary_text, self.styles['Normal']))
        elements.append(Spacer(1, 20))
        
        return elements
    
    # Métodos auxiliares adicionais (implementação simplificada)
    def _create_hourly_analysis(self, data: Dict[str, Any]) -> List:
        elements = []
        elements.append(Paragraph("Análise por Horário", self.styles['SectionHeader']))
        elements.append(Paragraph("Dados de distribuição por hora disponíveis na versão web.", self.styles['Normal']))
        elements.append(Spacer(1, 20))
        return elements
    
    def _create_timing_by_queue_table(self, data: Dict[str, Any]) -> List:
        elements = []
        elements.append(Paragraph("Métricas por Fila", self.styles['SectionHeader']))
        elements.append(Paragraph("Detalhamento das métricas por fila de atendimento.", self.styles['Normal']))
        elements.append(Spacer(1, 20))
        return elements
    
    def _create_sla_analysis(self, data: Dict[str, Any]) -> List:
        elements = []
        elements.append(Paragraph("Análise de SLA", self.styles['SectionHeader']))
        elements.append(Paragraph("Análise de compliance com os SLAs estabelecidos.", self.styles['Normal']))
        elements.append(Spacer(1, 20))
        return elements
    
    def _create_satisfaction_metrics_section(self, data: Dict[str, Any]) -> List:
        elements = []
        elements.append(Paragraph("Métricas de Satisfação", self.styles['SectionHeader']))
        
        metrics = data.get('metrics', {})
        nps = metrics.get('nps', 0)
        csat = metrics.get('csat', 0)
        
        satisfaction_text = f"""
        <b>NPS (Net Promoter Score):</b> {nps:.1f}<br/>
        <b>CSAT (Customer Satisfaction):</b> {csat:.1f}%<br/>
        <b>Total de Respostas:</b> {metrics.get('total_responses', 0)}
        """
        
        elements.append(Paragraph(satisfaction_text, self.styles['Normal']))
        elements.append(Spacer(1, 20))
        return elements
    
    def _create_nps_distribution_table(self, data: Dict[str, Any]) -> List:
        elements = []
        elements.append(Paragraph("Distribuição NPS", self.styles['SectionHeader']))
        elements.append(Paragraph("Distribuição das avaliações por categoria NPS.", self.styles['Normal']))
        elements.append(Spacer(1, 20))
        return elements
    
    def _create_comments_section(self, data: Dict[str, Any]) -> List:
        elements = []
        elements.append(Paragraph("Comentários dos Clientes", self.styles['SectionHeader']))
        
        comments = data.get('comments', [])
        for i, comment in enumerate(comments[:5]):  # Mostra apenas os primeiros 5
            elements.append(Paragraph(f"{i+1}. {comment}", self.styles['Normal']))
            elements.append(Spacer(1, 5))
        
        elements.append(Spacer(1, 20))
        return elements
    
    def _create_team_summary(self, data: Dict[str, Any]) -> List:
        elements = []
        elements.append(Paragraph("Resumo da Equipe", self.styles['SectionHeader']))
        
        team_avg = data.get('team_averages', {})
        summary_text = f"""
        <b>Média de Chamadas por Agente:</b> {team_avg.get('calls_handled', 0):.1f}<br/>
        <b>Taxa de Ocupação Média:</b> {team_avg.get('occupancy_rate', 0):.1f}%<br/>
        <b>Score de Produtividade Médio:</b> {team_avg.get('productivity_score', 0):.1f}
        """
        
        elements.append(Paragraph(summary_text, self.styles['Normal']))
        elements.append(Spacer(1, 20))
        return elements
    
    def _create_agent_performance_table(self, data: Dict[str, Any]) -> List:
        elements = []
        elements.append(Paragraph("Performance Individual", self.styles['SectionHeader']))
        elements.append(Paragraph("Tabela detalhada de performance disponível na versão web.", self.styles['Normal']))
        elements.append(Spacer(1, 20))
        return elements
    
    def _create_top_performers_section(self, data: Dict[str, Any]) -> List:
        elements = []
        elements.append(Paragraph("Top Performers", self.styles['SectionHeader']))
        
        top_performers = data.get('top_performers', [])
        for i, performer in enumerate(top_performers):
            elements.append(Paragraph(f"{i+1}. {performer}", self.styles['Normal']))
        
        elements.append(Spacer(1, 20))
        return elements
    
    def _create_improvement_opportunities(self, data: Dict[str, Any]) -> List:
        elements = []
        elements.append(Paragraph("Oportunidades de Melhoria", self.styles['SectionHeader']))
        
        opportunities = data.get('improvement_opportunities', [])
        for opportunity in opportunities:
            elements.append(Paragraph(f"• {opportunity}", self.styles['Normal']))
        
        elements.append(Spacer(1, 20))
        return elements
    
    def _create_distribution_section(self, data: Dict[str, Any]) -> List:
        elements = []
        elements.append(Paragraph("Distribuição de Chamadas", self.styles['CustomTitle']))
        elements.extend(self._create_distribution_summary(data))
        elements.extend(self._create_distribution_table(data))
        return elements
    
    def _create_timing_section(self, data: Dict[str, Any]) -> List:
        elements = []
        elements.append(Paragraph("Métricas de Timing", self.styles['CustomTitle']))
        elements.extend(self._create_timing_metrics_section(data))
        return elements
    
    def _create_satisfaction_section(self, data: Dict[str, Any]) -> List:
        elements = []
        elements.append(Paragraph("Pesquisa de Satisfação", self.styles['CustomTitle']))
        elements.extend(self._create_satisfaction_metrics_section(data))
        return elements
    
    def _create_agents_section(self, data: Dict[str, Any]) -> List:
        elements = []
        elements.append(Paragraph("Performance de Agentes", self.styles['CustomTitle']))
        elements.extend(self._create_team_summary(data))
        return elements
    
    def _create_conclusions_section(self, data: Dict[str, Any]) -> List:
        elements = []
        elements.append(Paragraph("Conclusões e Recomendações", self.styles['CustomTitle']))
        
        conclusions_text = """
        Com base na análise dos dados apresentados, recomenda-se:
        
        1. Monitoramento contínuo das métricas de Service Level
        2. Implementação de treinamentos focados em FCR
        3. Otimização da distribuição de chamadas entre filas
        4. Acompanhamento individual da performance dos agentes
        5. Melhoria contínua baseada no feedback dos clientes
        """
        
        elements.append(Paragraph(conclusions_text, self.styles['Normal']))
        return elements


# Instância global do gerador
pdf_generator = PDFGenerator()
