"""
Gerador de gráficos e visualizações usando Plotly
"""
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
import base64
import io
import logging

logger = logging.getLogger(__name__)


class ChartGenerator:
    """Gerador de gráficos para relatórios"""
    
    def __init__(self):
        self.color_palette = {
            'primary': '#3498db',
            'secondary': '#2ecc71',
            'warning': '#f39c12',
            'danger': '#e74c3c',
            'info': '#9b59b6',
            'success': '#27ae60',
            'dark': '#34495e'
        }
        self.chart_config = {
            'displayModeBar': False,
            'responsive': True
        }
    
    def create_distribution_chart(self, distribution_data: Dict[str, Any]) -> str:
        """Cria gráfico de distribuição de chamadas por fila"""
        try:
            queues_data = distribution_data.get('distribution_by_queue', [])
            
            if not queues_data:
                return self._create_no_data_chart("Distribuição por Fila")
            
            # Extrai dados
            queue_names = [f"Fila {q.get('queue_id', 'N/A')}" for q in queues_data]
            calls = [q.get('calls', 0) for q in queues_data]
            percentages = [q.get('percentage', 0) for q in queues_data]
            
            # Cria gráfico de pizza
            fig = go.Figure(data=[go.Pie(
                labels=queue_names,
                values=calls,
                textinfo='label+percent',
                textposition='auto',
                hovertemplate='<b>%{label}</b><br>' +
                             'Chamadas: %{value}<br>' +
                             'Percentual: %{percent}<br>' +
                             '<extra></extra>',
                marker=dict(
                    colors=[self.color_palette['primary'], 
                           self.color_palette['secondary'],
                           self.color_palette['warning'],
                           self.color_palette['info']]
                )
            )])
            
            fig.update_layout(
                title={
                    'text': 'Distribuição de Chamadas por Fila',
                    'x': 0.5,
                    'xanchor': 'center',
                    'font': {'size': 18, 'color': self.color_palette['dark']}
                },
                showlegend=True,
                height=400,
                margin=dict(t=60, b=40, l=40, r=40)
            )
            
            return fig.to_html(config=self.chart_config, div_id="distribution_chart")
        
        except Exception as e:
            logger.error(f"Erro ao criar gráfico de distribuição: {str(e)}")
            return self._create_error_chart("Erro ao gerar gráfico de distribuição")
    
    def create_timing_metrics_chart(self, timing_data: Dict[str, Any]) -> str:
        """Cria gráfico de métricas de timing"""
        try:
            metrics = timing_data.get('overall_metrics', {})
            
            # Dados para o gráfico
            metric_names = ['ASA (s)', 'AHT (s)', 'ATT (s)', 'ACW (s)']
            metric_values = [
                metrics.get('asa', 0),
                metrics.get('aht', 0),
                metrics.get('att', 0),
                metrics.get('acw', 0)
            ]
            
            # Cria gráfico de barras
            fig = go.Figure(data=[
                go.Bar(
                    x=metric_names,
                    y=metric_values,
                    marker_color=[
                        self.color_palette['primary'],
                        self.color_palette['secondary'],
                        self.color_palette['warning'],
                        self.color_palette['info']
                    ],
                    text=[f'{v:.1f}s' for v in metric_values],
                    textposition='auto',
                    hovertemplate='<b>%{x}</b><br>Valor: %{y:.1f}s<extra></extra>'
                )
            ])
            
            fig.update_layout(
                title={
                    'text': 'Métricas de Timing',
                    'x': 0.5,
                    'xanchor': 'center',
                    'font': {'size': 18, 'color': self.color_palette['dark']}
                },
                xaxis_title='Métricas',
                yaxis_title='Tempo (segundos)',
                height=400,
                margin=dict(t=60, b=40, l=40, r=40)
            )
            
            return fig.to_html(config=self.chart_config, div_id="timing_chart")
        
        except Exception as e:
            logger.error(f"Erro ao criar gráfico de timing: {str(e)}")
            return self._create_error_chart("Erro ao gerar gráfico de timing")
    
    def create_service_level_gauge(self, service_level: float) -> str:
        """Cria gauge de Service Level"""
        try:
            # Define cores baseadas no valor
            if service_level >= 90:
                color = self.color_palette['success']
            elif service_level >= 80:
                color = self.color_palette['warning']
            else:
                color = self.color_palette['danger']
            
            fig = go.Figure(go.Indicator(
                mode="gauge+number+delta",
                value=service_level,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Service Level (%)"},
                delta={'reference': 80, 'increasing': {'color': "green"}},
                gauge={
                    'axis': {'range': [None, 100]},
                    'bar': {'color': color},
                    'steps': [
                        {'range': [0, 50], 'color': "lightgray"},
                        {'range': [50, 80], 'color': "gray"},
                        {'range': [80, 100], 'color': "lightgreen"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 80
                    }
                }
            ))
            
            fig.update_layout(
                height=300,
                margin=dict(t=40, b=40, l=40, r=40)
            )
            
            return fig.to_html(config=self.chart_config, div_id="service_level_gauge")
        
        except Exception as e:
            logger.error(f"Erro ao criar gauge de service level: {str(e)}")
            return self._create_error_chart("Erro ao gerar gauge")
    
    def create_satisfaction_chart(self, satisfaction_data: Dict[str, Any]) -> str:
        """Cria gráfico de satisfação (NPS)"""
        try:
            metrics = satisfaction_data.get('metrics', {})
            
            promoters = metrics.get('promoters', 0)
            passives = metrics.get('passives', 0)
            detractors = metrics.get('detractors', 0)
            nps = metrics.get('nps', 0)
            
            # Gráfico de barras empilhadas
            fig = go.Figure(data=[
                go.Bar(
                    name='Detratores (0-6)',
                    x=['NPS Distribution'],
                    y=[detractors],
                    marker_color=self.color_palette['danger']
                ),
                go.Bar(
                    name='Neutros (7-8)',
                    x=['NPS Distribution'],
                    y=[passives],
                    marker_color=self.color_palette['warning']
                ),
                go.Bar(
                    name='Promotores (9-10)',
                    x=['NPS Distribution'],
                    y=[promoters],
                    marker_color=self.color_palette['success']
                )
            ])
            
            fig.update_layout(
                barmode='stack',
                title={
                    'text': f'Distribuição NPS - Score: {nps:.1f}',
                    'x': 0.5,
                    'xanchor': 'center',
                    'font': {'size': 18, 'color': self.color_palette['dark']}
                },
                xaxis_title='',
                yaxis_title='Número de Respostas',
                height=400,
                margin=dict(t=60, b=40, l=40, r=40)
            )
            
            return fig.to_html(config=self.chart_config, div_id="satisfaction_chart")
        
        except Exception as e:
            logger.error(f"Erro ao criar gráfico de satisfação: {str(e)}")
            return self._create_error_chart("Erro ao gerar gráfico de satisfação")
    
    def create_agent_performance_chart(self, agent_data: Dict[str, Any]) -> str:
        """Cria gráfico de performance de agentes"""
        try:
            agents = agent_data.get('agents', [])
            
            if not agents:
                return self._create_no_data_chart("Performance de Agentes")
            
            # Extrai dados dos top 10 agentes
            top_agents = sorted(agents, key=lambda x: x.get('productivity_score', 0), reverse=True)[:10]
            
            agent_names = [agent.get('agent_name', 'N/A') for agent in top_agents]
            productivity_scores = [agent.get('productivity_score', 0) for agent in top_agents]
            calls_handled = [agent.get('calls_handled', 0) for agent in top_agents]
            
            # Cria subplot com dois eixos Y
            fig = make_subplots(specs=[[{"secondary_y": True}]])
            
            # Adiciona barras de produtividade
            fig.add_trace(
                go.Bar(
                    x=agent_names,
                    y=productivity_scores,
                    name="Score de Produtividade",
                    marker_color=self.color_palette['primary'],
                    yaxis="y"
                ),
                secondary_y=False,
            )
            
            # Adiciona linha de chamadas atendidas
            fig.add_trace(
                go.Scatter(
                    x=agent_names,
                    y=calls_handled,
                    mode='lines+markers',
                    name="Chamadas Atendidas",
                    line=dict(color=self.color_palette['secondary'], width=3),
                    yaxis="y2"
                ),
                secondary_y=True,
            )
            
            # Atualiza layout
            fig.update_xaxes(title_text="Agentes")
            fig.update_yaxes(title_text="Score de Produtividade", secondary_y=False)
            fig.update_yaxes(title_text="Chamadas Atendidas", secondary_y=True)
            
            fig.update_layout(
                title={
                    'text': 'Performance dos Agentes (Top 10)',
                    'x': 0.5,
                    'xanchor': 'center',
                    'font': {'size': 18, 'color': self.color_palette['dark']}
                },
                height=500,
                margin=dict(t=60, b=100, l=40, r=40),
                xaxis={'tickangle': -45}
            )
            
            return fig.to_html(config=self.chart_config, div_id="agent_performance_chart")
        
        except Exception as e:
            logger.error(f"Erro ao criar gráfico de agentes: {str(e)}")
            return self._create_error_chart("Erro ao gerar gráfico de agentes")
    
    def create_hourly_distribution_chart(self, hourly_data: Dict[str, Any]) -> str:
        """Cria gráfico de distribuição por hora"""
        try:
            if not hourly_data:
                return self._create_no_data_chart("Distribuição por Hora")
            
            hours = list(hourly_data.keys())
            calls = [data.get('calls', 0) for data in hourly_data.values()]
            service_levels = [data.get('service_level', 0) for data in hourly_data.values()]
            
            # Cria subplot com dois eixos Y
            fig = make_subplots(specs=[[{"secondary_y": True}]])
            
            # Adiciona barras de chamadas
            fig.add_trace(
                go.Bar(
                    x=hours,
                    y=calls,
                    name="Chamadas",
                    marker_color=self.color_palette['primary'],
                    opacity=0.7
                ),
                secondary_y=False,
            )
            
            # Adiciona linha de service level
            fig.add_trace(
                go.Scatter(
                    x=hours,
                    y=service_levels,
                    mode='lines+markers',
                    name="Service Level (%)",
                    line=dict(color=self.color_palette['danger'], width=3)
                ),
                secondary_y=True,
            )
            
            # Atualiza layout
            fig.update_xaxes(title_text="Hora do Dia")
            fig.update_yaxes(title_text="Número de Chamadas", secondary_y=False)
            fig.update_yaxes(title_text="Service Level (%)", secondary_y=True)
            
            fig.update_layout(
                title={
                    'text': 'Distribuição de Chamadas por Hora',
                    'x': 0.5,
                    'xanchor': 'center',
                    'font': {'size': 18, 'color': self.color_palette['dark']}
                },
                height=400,
                margin=dict(t=60, b=40, l=40, r=40)
            )
            
            return fig.to_html(config=self.chart_config, div_id="hourly_chart")
        
        except Exception as e:
            logger.error(f"Erro ao criar gráfico por hora: {str(e)}")
            return self._create_error_chart("Erro ao gerar gráfico por hora")
    
    def create_comparative_chart(self, comparative_data: Dict[str, Any]) -> str:
        """Cria gráfico comparativo entre períodos"""
        try:
            comparison = comparative_data.get('comparison', {})
            variance = comparison.get('variance', {})
            
            if not variance:
                return self._create_no_data_chart("Comparativo entre Períodos")
            
            metrics = list(variance.keys())
            variances = list(variance.values())
            
            # Define cores baseadas na variação
            colors = []
            for var in variances:
                if var > 5:
                    colors.append(self.color_palette['success'])
                elif var < -5:
                    colors.append(self.color_palette['danger'])
                else:
                    colors.append(self.color_palette['warning'])
            
            fig = go.Figure(data=[
                go.Bar(
                    x=metrics,
                    y=variances,
                    marker_color=colors,
                    text=[f'{v:+.1f}%' for v in variances],
                    textposition='auto',
                    hovertemplate='<b>%{x}</b><br>Variação: %{y:+.1f}%<extra></extra>'
                )
            ])
            
            # Adiciona linha de referência em 0
            fig.add_hline(y=0, line_dash="dash", line_color="gray")
            
            fig.update_layout(
                title={
                    'text': 'Variação entre Períodos (%)',
                    'x': 0.5,
                    'xanchor': 'center',
                    'font': {'size': 18, 'color': self.color_palette['dark']}
                },
                xaxis_title='Métricas',
                yaxis_title='Variação (%)',
                height=400,
                margin=dict(t=60, b=100, l=40, r=40),
                xaxis={'tickangle': -45}
            )
            
            return fig.to_html(config=self.chart_config, div_id="comparative_chart")
        
        except Exception as e:
            logger.error(f"Erro ao criar gráfico comparativo: {str(e)}")
            return self._create_error_chart("Erro ao gerar gráfico comparativo")
    
    def create_dashboard_summary(self, summary_data: Dict[str, Any]) -> str:
        """Cria dashboard resumo com múltiplos gráficos"""
        try:
            # Cria subplot 2x2
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('Chamadas por Fila', 'Service Level', 'Satisfação NPS', 'Tendência Horária'),
                specs=[[{"type": "pie"}, {"type": "indicator"}],
                       [{"type": "bar"}, {"type": "scatter"}]]
            )
            
            # Gráfico 1: Pizza de distribuição
            distribution = summary_data.get('distribution', {})
            queues_data = distribution.get('distribution_by_queue', [])
            if queues_data:
                fig.add_trace(
                    go.Pie(
                        labels=[f"Fila {q.get('queue_id', 'N/A')}" for q in queues_data],
                        values=[q.get('calls', 0) for q in queues_data],
                        showlegend=False
                    ),
                    row=1, col=1
                )
            
            # Gráfico 2: Gauge de Service Level
            timing = summary_data.get('timing', {})
            service_level = timing.get('service_level', 0)
            fig.add_trace(
                go.Indicator(
                    mode="gauge+number",
                    value=service_level,
                    gauge={'axis': {'range': [0, 100]},
                           'bar': {'color': self.color_palette['primary']}},
                    domain={'x': [0, 1], 'y': [0, 1]}
                ),
                row=1, col=2
            )
            
            # Gráfico 3: NPS
            satisfaction = summary_data.get('satisfaction', {})
            nps_data = satisfaction.get('metrics', {})
            if nps_data:
                fig.add_trace(
                    go.Bar(
                        x=['Detratores', 'Neutros', 'Promotores'],
                        y=[nps_data.get('detractors', 0), 
                           nps_data.get('passives', 0), 
                           nps_data.get('promoters', 0)],
                        marker_color=[self.color_palette['danger'], 
                                     self.color_palette['warning'], 
                                     self.color_palette['success']],
                        showlegend=False
                    ),
                    row=2, col=1
                )
            
            # Gráfico 4: Tendência horária
            hourly = summary_data.get('hourly', {})
            if hourly:
                hours = list(hourly.keys())
                calls = [data.get('calls', 0) for data in hourly.values()]
                fig.add_trace(
                    go.Scatter(
                        x=hours,
                        y=calls,
                        mode='lines+markers',
                        line=dict(color=self.color_palette['secondary']),
                        showlegend=False
                    ),
                    row=2, col=2
                )
            
            fig.update_layout(
                title={
                    'text': 'Dashboard Resumo - Métricas de Pós-Venda',
                    'x': 0.5,
                    'xanchor': 'center',
                    'font': {'size': 20, 'color': self.color_palette['dark']}
                },
                height=600,
                margin=dict(t=80, b=40, l=40, r=40)
            )
            
            return fig.to_html(config=self.chart_config, div_id="dashboard_summary")
        
        except Exception as e:
            logger.error(f"Erro ao criar dashboard: {str(e)}")
            return self._create_error_chart("Erro ao gerar dashboard")
    
    def _create_no_data_chart(self, title: str) -> str:
        """Cria gráfico para quando não há dados"""
        fig = go.Figure()
        fig.add_annotation(
            text="Nenhum dado disponível para o período selecionado",
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            showarrow=False,
            font=dict(size=16, color=self.color_palette['dark'])
        )
        fig.update_layout(
            title=title,
            height=300,
            margin=dict(t=60, b=40, l=40, r=40)
        )
        return fig.to_html(config=self.chart_config)
    
    def _create_error_chart(self, error_message: str) -> str:
        """Cria gráfico para quando há erro"""
        fig = go.Figure()
        fig.add_annotation(
            text=error_message,
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            showarrow=False,
            font=dict(size=16, color=self.color_palette['danger'])
        )
        fig.update_layout(
            title="Erro na Geração do Gráfico",
            height=300,
            margin=dict(t=60, b=40, l=40, r=40)
        )
        return fig.to_html(config=self.chart_config)


# Instância global do gerador
chart_generator = ChartGenerator()
