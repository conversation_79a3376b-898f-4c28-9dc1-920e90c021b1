"""
Serviço de métricas do Chatwoot
"""
import logging
from typing import Dict, List, Any, Optional
from datetime import date, datetime, timedelta
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

from src.clients.chatwoot_client import chatwoot_client

logger = logging.getLogger(__name__)


class ChatwootMetricsService:
    """Serviço para processamento de métricas do Chatwoot"""
    
    def __init__(self):
        self.client = chatwoot_client
    
    async def get_satisfaction_report(
        self, 
        start_date: date, 
        end_date: date, 
        agent_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Gera relatório de satisfação"""
        try:
            data = await self.client.get_satisfaction_metrics(start_date, end_date, agent_id)
            
            if not data:
                return {
                    'data': [],
                    'summary': {
                        'total_agents': 0,
                        'total_ratings': 0,
                        'average_rating': 0,
                        'satisfaction_rate': 0
                    },
                    'charts': {}
                }
            
            # Calcular resumo
            total_ratings = sum(row['total_ratings'] for row in data)
            total_positive = sum(row['positive_ratings'] for row in data)
            avg_rating = sum(row['average_rating'] * row['total_ratings'] for row in data) / total_ratings if total_ratings > 0 else 0
            satisfaction_rate = (total_positive * 100 / total_ratings) if total_ratings > 0 else 0
            
            summary = {
                'total_agents': len(data),
                'total_ratings': total_ratings,
                'average_rating': round(avg_rating, 2),
                'satisfaction_rate': round(satisfaction_rate, 2)
            }
            
            # Gerar gráficos
            charts = await self._generate_satisfaction_charts(data)
            
            return {
                'data': data,
                'summary': summary,
                'charts': charts,
                'period': {'start_date': start_date, 'end_date': end_date}
            }
            
        except Exception as e:
            logger.error(f"Erro ao gerar relatório de satisfação: {e}")
            raise
    
    async def get_response_time_report(
        self, 
        start_date: date, 
        end_date: date, 
        agent_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Gera relatório de tempo de resposta"""
        try:
            data = await self.client.get_response_time_metrics(start_date, end_date, agent_id)
            
            if not data:
                return {
                    'data': [],
                    'summary': {
                        'total_agents': 0,
                        'total_conversations': 0,
                        'average_response_time': 0
                    },
                    'charts': {}
                }
            
            # Calcular resumo
            total_conversations = sum(row['total_conversations'] for row in data)
            avg_response_time = sum(
                row['avg_first_response_time_minutes'] * row['total_conversations'] 
                for row in data if row['avg_first_response_time_minutes']
            ) / total_conversations if total_conversations > 0 else 0
            
            summary = {
                'total_agents': len(data),
                'total_conversations': total_conversations,
                'average_response_time': round(avg_response_time, 2)
            }
            
            # Gerar gráficos
            charts = await self._generate_response_time_charts(data)
            
            return {
                'data': data,
                'summary': summary,
                'charts': charts,
                'period': {'start_date': start_date, 'end_date': end_date}
            }
            
        except Exception as e:
            logger.error(f"Erro ao gerar relatório de tempo de resposta: {e}")
            raise
    
    async def get_volume_report(
        self, 
        start_date: date, 
        end_date: date
    ) -> Dict[str, Any]:
        """Gera relatório de volume de conversas"""
        try:
            data = await self.client.get_conversation_volume(start_date, end_date)
            
            if not data:
                return {
                    'data': [],
                    'summary': {
                        'total_conversations': 0,
                        'daily_average': 0,
                        'peak_day': None
                    },
                    'charts': {}
                }
            
            # Calcular resumo
            total_conversations = sum(row['total_conversations'] for row in data)
            daily_average = total_conversations / len(data) if data else 0
            peak_day = max(data, key=lambda x: x['total_conversations']) if data else None
            
            summary = {
                'total_conversations': total_conversations,
                'daily_average': round(daily_average, 1),
                'peak_day': peak_day
            }
            
            # Gerar gráficos
            charts = await self._generate_volume_charts(data)
            
            return {
                'data': data,
                'summary': summary,
                'charts': charts,
                'period': {'start_date': start_date, 'end_date': end_date}
            }
            
        except Exception as e:
            logger.error(f"Erro ao gerar relatório de volume: {e}")
            raise
    
    async def get_dashboard_data(
        self, 
        start_date: date, 
        end_date: date
    ) -> Dict[str, Any]:
        """Gera dados para dashboard"""
        try:
            summary = await self.client.get_dashboard_summary(start_date, end_date)
            
            # Buscar dados para gráficos do dashboard
            volume_data = await self.client.get_conversation_volume(start_date, end_date)
            satisfaction_data = await self.client.get_satisfaction_metrics(start_date, end_date)
            
            # Gerar gráficos resumidos
            charts = {
                'volume_trend': await self._generate_volume_trend_chart(volume_data),
                'satisfaction_overview': await self._generate_satisfaction_overview_chart(satisfaction_data)
            }
            
            return {
                'summary': summary,
                'charts': charts
            }
            
        except Exception as e:
            logger.error(f"Erro ao gerar dados do dashboard: {e}")
            raise
    
    async def _generate_satisfaction_charts(self, data: List[Dict[str, Any]]) -> Dict[str, str]:
        """Gera gráficos de satisfação"""
        charts = {}
        
        if not data:
            return charts
        
        try:
            # Gráfico de barras - Avaliação média por agente
            fig_bar = go.Figure()
            fig_bar.add_trace(go.Bar(
                x=[row['agent_name'] for row in data],
                y=[row['average_rating'] for row in data],
                text=[f"{row['average_rating']:.1f}" for row in data],
                textposition='auto',
                marker_color='#28a745'
            ))
            fig_bar.update_layout(
                title='Avaliação Média por Agente',
                xaxis_title='Agente',
                yaxis_title='Avaliação Média',
                yaxis=dict(range=[0, 5])
            )
            charts['satisfaction_by_agent'] = fig_bar.to_html(include_plotlyjs='cdn')
            
            # Gráfico de pizza - Distribuição de avaliações
            total_positive = sum(row['positive_ratings'] for row in data)
            total_negative = sum(row['negative_ratings'] for row in data)
            total_neutral = sum(row['total_ratings'] - row['positive_ratings'] - row['negative_ratings'] for row in data)
            
            fig_pie = go.Figure(data=[go.Pie(
                labels=['Positivas (4-5)', 'Neutras (3)', 'Negativas (1-2)'],
                values=[total_positive, total_neutral, total_negative],
                hole=0.3,
                marker_colors=['#28a745', '#ffc107', '#dc3545']
            )])
            fig_pie.update_layout(title='Distribuição de Avaliações')
            charts['satisfaction_distribution'] = fig_pie.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            logger.error(f"Erro ao gerar gráficos de satisfação: {e}")
        
        return charts
    
    async def _generate_response_time_charts(self, data: List[Dict[str, Any]]) -> Dict[str, str]:
        """Gera gráficos de tempo de resposta"""
        charts = {}
        
        if not data:
            return charts
        
        try:
            # Gráfico de barras - Tempo médio de resposta por agente
            fig_bar = go.Figure()
            fig_bar.add_trace(go.Bar(
                x=[row['agent_name'] for row in data],
                y=[row['avg_first_response_time_minutes'] or 0 for row in data],
                text=[f"{row['avg_first_response_time_minutes']:.1f}min" if row['avg_first_response_time_minutes'] else "0min" for row in data],
                textposition='auto',
                marker_color='#007bff'
            ))
            fig_bar.update_layout(
                title='Tempo Médio de Primeira Resposta por Agente',
                xaxis_title='Agente',
                yaxis_title='Tempo (minutos)'
            )
            charts['response_time_by_agent'] = fig_bar.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            logger.error(f"Erro ao gerar gráficos de tempo de resposta: {e}")
        
        return charts
    
    async def _generate_volume_charts(self, data: List[Dict[str, Any]]) -> Dict[str, str]:
        """Gera gráficos de volume"""
        charts = {}
        
        if not data:
            return charts
        
        try:
            # Gráfico de linha - Volume diário
            dates = [row['date'] for row in reversed(data)]
            volumes = [row['total_conversations'] for row in reversed(data)]
            
            fig_line = go.Figure()
            fig_line.add_trace(go.Scatter(
                x=dates,
                y=volumes,
                mode='lines+markers',
                name='Conversas',
                line=dict(color='#007bff', width=3),
                marker=dict(size=8)
            ))
            fig_line.update_layout(
                title='Volume Diário de Conversas',
                xaxis_title='Data',
                yaxis_title='Número de Conversas'
            )
            charts['daily_volume'] = fig_line.to_html(include_plotlyjs='cdn')
            
            # Gráfico de barras empilhadas - Status das conversas
            fig_stacked = go.Figure()
            fig_stacked.add_trace(go.Bar(
                x=dates,
                y=[row['resolved_conversations'] for row in reversed(data)],
                name='Resolvidas',
                marker_color='#28a745'
            ))
            fig_stacked.add_trace(go.Bar(
                x=dates,
                y=[row['pending_conversations'] for row in reversed(data)],
                name='Pendentes',
                marker_color='#ffc107'
            ))
            fig_stacked.add_trace(go.Bar(
                x=dates,
                y=[row['open_conversations'] for row in reversed(data)],
                name='Abertas',
                marker_color='#dc3545'
            ))
            fig_stacked.update_layout(
                title='Status das Conversas por Dia',
                xaxis_title='Data',
                yaxis_title='Número de Conversas',
                barmode='stack'
            )
            charts['status_distribution'] = fig_stacked.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            logger.error(f"Erro ao gerar gráficos de volume: {e}")
        
        return charts
    
    async def _generate_volume_trend_chart(self, data: List[Dict[str, Any]]) -> str:
        """Gera gráfico de tendência de volume para dashboard"""
        if not data:
            return ""
        
        try:
            dates = [row['date'] for row in reversed(data[-7:])]  # Últimos 7 dias
            volumes = [row['total_conversations'] for row in reversed(data[-7:])]
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=dates,
                y=volumes,
                mode='lines+markers',
                line=dict(color='#007bff', width=2),
                marker=dict(size=6),
                fill='tonexty',
                fillcolor='rgba(0,123,255,0.1)'
            ))
            fig.update_layout(
                title='Tendência de Volume (7 dias)',
                height=300,
                margin=dict(l=20, r=20, t=40, b=20)
            )
            return fig.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            logger.error(f"Erro ao gerar gráfico de tendência: {e}")
            return ""
    
    async def _generate_satisfaction_overview_chart(self, data: List[Dict[str, Any]]) -> str:
        """Gera gráfico de overview de satisfação para dashboard"""
        if not data:
            return ""
        
        try:
            total_positive = sum(row['positive_ratings'] for row in data)
            total_negative = sum(row['negative_ratings'] for row in data)
            total_neutral = sum(row['total_ratings'] - row['positive_ratings'] - row['negative_ratings'] for row in data)
            
            fig = go.Figure(data=[go.Pie(
                labels=['Positivas', 'Neutras', 'Negativas'],
                values=[total_positive, total_neutral, total_negative],
                hole=0.4,
                marker_colors=['#28a745', '#ffc107', '#dc3545']
            )])
            fig.update_layout(
                title='Distribuição de Satisfação',
                height=300,
                margin=dict(l=20, r=20, t=40, b=20)
            )
            return fig.to_html(include_plotlyjs='cdn')
            
        except Exception as e:
            logger.error(f"Erro ao gerar gráfico de satisfação: {e}")
            return ""


# Instância global do serviço
chatwoot_metrics_service = ChatwootMetricsService()
