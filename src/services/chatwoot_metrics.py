"""
Serviço de métricas do Chatwoot - Sistema de Relatórios WhatsApp
"""
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import date, datetime, timedelta
import asyncio
from decimal import Decimal

from src.clients.chatwoot_client import chatwoot_client

logger = logging.getLogger(__name__)


class ChatwootMetricsService:
    """Serviço para processamento de métricas do Chatwoot"""

    def __init__(self):
        self.client = chatwoot_client
        self.account_id = 1  # ID padrão da conta
    
    async def get_dashboard_summary(
        self,
        start_date: date,
        end_date: date
    ) -> Dict[str, Any]:
        """Retorna resumo geral para o dashboard"""
        try:
            # Executar todas as consultas em paralelo
            tasks = [
                self._get_conversations_summary(start_date, end_date),
                self._get_satisfaction_summary(start_date, end_date),
                self._get_response_time_summary(start_date, end_date),
                self._get_agents_summary(start_date, end_date)
            ]

            conversations, satisfaction, response_time, agents = await asyncio.gather(*tasks)

            return {
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'conversations': conversations,
                'satisfaction': satisfaction,
                'response_time': response_time,
                'agents': agents,
                'last_updated': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Erro ao gerar resumo do dashboard: {e}")
            return self._get_empty_dashboard()

    async def get_satisfaction_metrics(
        self,
        start_date: date,
        end_date: date,
        agent_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Retorna métricas detalhadas de satisfação"""
        try:
            # Buscar dados de satisfação
            satisfaction_data = await self._get_satisfaction_data(start_date, end_date, agent_id)

            if not satisfaction_data:
                return self._get_empty_satisfaction_metrics()

            # Processar métricas
            metrics = self._process_satisfaction_metrics(satisfaction_data)

            # Buscar distribuição por nota
            distribution = await self._get_satisfaction_distribution(start_date, end_date, agent_id)

            # Buscar tendência temporal
            trend = await self._get_satisfaction_trend(start_date, end_date, agent_id)

            return {
                'summary': metrics,
                'by_agent': satisfaction_data,
                'distribution': distribution,
                'trend': trend,
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                }
            }

        except Exception as e:
            logger.error(f"Erro ao buscar métricas de satisfação: {e}")
            return self._get_empty_satisfaction_metrics()

    async def get_response_time_metrics(
        self,
        start_date: date,
        end_date: date,
        agent_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Retorna métricas de tempo de resposta"""
        try:
            # Buscar dados de tempo de resposta
            response_data = await self._get_response_time_data(start_date, end_date, agent_id)

            if not response_data:
                return self._get_empty_response_time_metrics()

            # Processar métricas
            metrics = self._process_response_time_metrics(response_data)

            # Buscar distribuição por faixas de tempo
            distribution = await self._get_response_time_distribution(start_date, end_date, agent_id)

            # Buscar análise por hora do dia
            hourly_analysis = await self._get_hourly_response_analysis(start_date, end_date, agent_id)

            return {
                'summary': metrics,
                'by_agent': response_data,
                'distribution': distribution,
                'hourly_analysis': hourly_analysis,
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                }
            }

        except Exception as e:
            logger.error(f"Erro ao buscar métricas de tempo de resposta: {e}")
            return self._get_empty_response_time_metrics()

    async def get_volume_metrics(
        self,
        start_date: date,
        end_date: date,
        agent_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Retorna métricas de volume de conversas"""
        try:
            # Buscar dados de volume
            volume_data = await self._get_volume_data(start_date, end_date, agent_id)

            if not volume_data:
                return self._get_empty_volume_metrics()

            # Buscar volume por dia
            daily_volume = await self._get_daily_volume(start_date, end_date, agent_id)

            # Buscar volume por hora
            hourly_volume = await self._get_hourly_volume(start_date, end_date, agent_id)

            # Buscar estatísticas de mensagens
            message_stats = await self._get_message_statistics(start_date, end_date, agent_id)

            return {
                'summary': volume_data,
                'daily_volume': daily_volume,
                'hourly_volume': hourly_volume,
                'message_stats': message_stats,
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                }
            }

        except Exception as e:
            logger.error(f"Erro ao buscar métricas de volume: {e}")
            return self._get_empty_volume_metrics()

    async def get_performance_metrics(
        self,
        start_date: date,
        end_date: date
    ) -> Dict[str, Any]:
        """Retorna métricas de performance dos agentes"""
        try:
            # Buscar performance completa dos agentes
            performance_data = await self._get_agent_performance_data(start_date, end_date)

            if not performance_data:
                return self._get_empty_performance_metrics()

            # Calcular scores de performance
            scored_agents = self._calculate_performance_scores(performance_data)

            # Buscar top performers
            top_performers = sorted(scored_agents, key=lambda x: x['performance_score'], reverse=True)[:3]

            return {
                'agents': scored_agents,
                'top_performers': top_performers,
                'summary': {
                    'total_agents': len(scored_agents),
                    'avg_performance_score': sum(a['performance_score'] for a in scored_agents) / len(scored_agents) if scored_agents else 0,
                    'best_performer': top_performers[0] if top_performers else None
                },
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                }
            }

        except Exception as e:
            logger.error(f"Erro ao buscar métricas de performance: {e}")
            return self._get_empty_performance_metrics()

    async def get_agents_list(self) -> List[Dict[str, Any]]:
        """Retorna lista de agentes disponíveis"""
        try:
            query = """
                SELECT id, name, email
                FROM users
                ORDER BY name
            """

            result = await self.client.execute_query(query, [])

            return [
                {
                    'id': row['id'],
                    'name': row['name'],
                    'email': row['email']
                }
                for row in result
            ]

        except Exception as e:
            logger.error(f"Erro ao buscar lista de agentes: {e}")
            return []

    # ========== MÉTODOS AUXILIARES PRIVADOS ==========

    async def _get_conversations_summary(self, start_date: date, end_date: date) -> Dict[str, Any]:
        """Busca resumo de conversas"""
        query = """
            SELECT
                COUNT(*) as total_conversations,
                COUNT(CASE WHEN status = 1 THEN 1 END) as resolved_conversations,
                COUNT(CASE WHEN status = 0 THEN 1 END) as open_conversations,
                COUNT(CASE WHEN status = 2 THEN 1 END) as pending_conversations,
                COUNT(DISTINCT assignee_id) as active_agents,
                COUNT(DISTINCT contact_id) as unique_contacts
            FROM conversations
            WHERE created_at BETWEEN $1 AND $2
                AND account_id = $3
        """

        result = await self.client.execute_query(query, [start_date, end_date, self.account_id])

        if result:
            row = result[0]
            total = int(row['total_conversations'])
            resolved = int(row['resolved_conversations'])

            return {
                'total_conversations': total,
                'resolved_conversations': resolved,
                'open_conversations': int(row['open_conversations']),
                'pending_conversations': int(row['pending_conversations']),
                'resolution_rate': round((resolved * 100 / total), 2) if total > 0 else 0,
                'active_agents': int(row['active_agents']),
                'unique_contacts': int(row['unique_contacts'])
            }

        return {
            'total_conversations': 0,
            'resolved_conversations': 0,
            'open_conversations': 0,
            'pending_conversations': 0,
            'resolution_rate': 0,
            'active_agents': 0,
            'unique_contacts': 0
        }

    async def _get_satisfaction_summary(self, start_date: date, end_date: date) -> Dict[str, Any]:
        """Busca resumo de satisfação"""
        query = """
            SELECT
                COUNT(*) as total_ratings,
                AVG(rating) as average_rating,
                COUNT(CASE WHEN rating >= 4 THEN 1 END) as positive_ratings,
                COUNT(CASE WHEN rating <= 2 THEN 1 END) as negative_ratings
            FROM csat_survey_responses
            WHERE created_at BETWEEN $1 AND $2
                AND account_id = $3
        """

        result = await self.client.execute_query(query, [start_date, end_date, self.account_id])

        if result and result[0]['total_ratings']:
            row = result[0]
            total = int(row['total_ratings'])
            positive = int(row['positive_ratings'])

            return {
                'total_ratings': total,
                'average_rating': round(float(row['average_rating']), 2),
                'positive_ratings': positive,
                'negative_ratings': int(row['negative_ratings']),
                'satisfaction_rate': round((positive * 100 / total), 2) if total > 0 else 0
            }

        return {
            'total_ratings': 0,
            'average_rating': 0,
            'positive_ratings': 0,
            'negative_ratings': 0,
            'satisfaction_rate': 0
        }

    async def _get_response_time_summary(self, start_date: date, end_date: date) -> Dict[str, Any]:
        """Busca resumo de tempo de resposta"""
        query = """
            SELECT
                COUNT(*) as total_conversations,
                AVG(EXTRACT(EPOCH FROM (first_reply_created_at - created_at)) / 60) as avg_response_minutes,
                MIN(EXTRACT(EPOCH FROM (first_reply_created_at - created_at)) / 60) as min_response_minutes,
                MAX(EXTRACT(EPOCH FROM (first_reply_created_at - created_at)) / 60) as max_response_minutes
            FROM conversations
            WHERE first_reply_created_at IS NOT NULL
                AND created_at BETWEEN $1 AND $2
                AND account_id = $3
        """

        result = await self.client.execute_query(query, [start_date, end_date, self.account_id])

        if result and result[0]['total_conversations']:
            row = result[0]

            return {
                'total_conversations': int(row['total_conversations']),
                'avg_response_minutes': round(float(row['avg_response_minutes']), 2),
                'min_response_minutes': round(float(row['min_response_minutes']), 2),
                'max_response_minutes': round(float(row['max_response_minutes']), 2)
            }

        return {
            'total_conversations': 0,
            'avg_response_minutes': 0,
            'min_response_minutes': 0,
            'max_response_minutes': 0
        }

    async def _get_agents_summary(self, start_date: date, end_date: date) -> Dict[str, Any]:
        """Busca resumo de agentes"""
        query = """
            SELECT
                COUNT(DISTINCT u.id) as total_agents,
                COUNT(DISTINCT c.assignee_id) as active_agents
            FROM users u
            LEFT JOIN conversations c ON u.id = c.assignee_id
                AND c.created_at BETWEEN $1 AND $2
        """

        result = await self.client.execute_query(query, [start_date, end_date])

        if result:
            row = result[0]
            return {
                'total_agents': int(row['total_agents']),
                'active_agents': int(row['active_agents'])
            }

        return {
            'total_agents': 0,
            'active_agents': 0
        }

    # Métodos para retornar dados vazios quando não há informações
    def _get_empty_dashboard(self) -> Dict[str, Any]:
        """Retorna dashboard vazio"""
        return {
            'period': {'start_date': '', 'end_date': ''},
            'conversations': {
                'total_conversations': 0,
                'resolved_conversations': 0,
                'open_conversations': 0,
                'pending_conversations': 0,
                'resolution_rate': 0,
                'active_agents': 0,
                'unique_contacts': 0
            },
            'satisfaction': {
                'total_ratings': 0,
                'average_rating': 0,
                'positive_ratings': 0,
                'negative_ratings': 0,
                'satisfaction_rate': 0
            },
            'response_time': {
                'total_conversations': 0,
                'avg_response_minutes': 0,
                'min_response_minutes': 0,
                'max_response_minutes': 0
            },
            'agents': {
                'total_agents': 0,
                'active_agents': 0
            },
            'last_updated': datetime.now().isoformat()
        }

    def _get_empty_satisfaction_metrics(self) -> Dict[str, Any]:
        """Retorna métricas de satisfação vazias"""
        return {
            'summary': {
                'total_ratings': 0,
                'average_rating': 0,
                'satisfaction_rate': 0,
                'nps_score': 0
            },
            'by_agent': [],
            'distribution': [],
            'trend': [],
            'period': {'start_date': '', 'end_date': ''}
        }

    def _get_empty_response_time_metrics(self) -> Dict[str, Any]:
        """Retorna métricas de tempo de resposta vazias"""
        return {
            'summary': {
                'avg_response_minutes': 0,
                'median_response_minutes': 0,
                'sla_compliance': 0
            },
            'by_agent': [],
            'distribution': [],
            'hourly_analysis': [],
            'period': {'start_date': '', 'end_date': ''}
        }

    def _get_empty_volume_metrics(self) -> Dict[str, Any]:
        """Retorna métricas de volume vazias"""
        return {
            'summary': {
                'total_conversations': 0,
                'total_messages': 0,
                'unique_contacts': 0,
                'daily_average': 0
            },
            'daily_volume': [],
            'hourly_volume': [],
            'message_stats': {},
            'period': {'start_date': '', 'end_date': ''}
        }

    def _get_empty_performance_metrics(self) -> Dict[str, Any]:
        """Retorna métricas de performance vazias"""
        return {
            'agents': [],
            'top_performers': [],
            'summary': {
                'total_agents': 0,
                'avg_performance_score': 0,
                'best_performer': None
            },
            'period': {'start_date': '', 'end_date': ''}
        }

    # Métodos auxiliares para buscar dados específicos (implementação simplificada)
    async def _get_satisfaction_data(self, start_date: date, end_date: date, agent_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """Busca dados de satisfação por agente"""
        # Implementação será adicionada quando houver dados CSAT
        return []

    async def _get_response_time_data(self, start_date: date, end_date: date, agent_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """Busca dados de tempo de resposta por agente"""
        query = """
            SELECT
                u.name as agent_name,
                u.id as agent_id,
                COUNT(c.id) as total_conversations,
                AVG(EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at)) / 60) as avg_response_minutes
            FROM conversations c
            JOIN users u ON c.assignee_id = u.id
            WHERE c.first_reply_created_at IS NOT NULL
                AND c.created_at BETWEEN $1 AND $2
                AND c.account_id = $3
        """

        params = [start_date, end_date, self.account_id]

        if agent_id:
            query += " AND c.assignee_id = $4"
            params.append(agent_id)

        query += " GROUP BY u.id, u.name ORDER BY avg_response_minutes ASC"

        result = await self.client.execute_query(query, params)

        return [
            {
                'agent_name': row['agent_name'],
                'agent_id': row['agent_id'],
                'total_conversations': int(row['total_conversations']),
                'avg_response_minutes': round(float(row['avg_response_minutes']), 2) if row['avg_response_minutes'] else 0
            }
            for row in result
        ]


# Instância global do serviço
chatwoot_metrics_service = ChatwootMetricsService()



