"""
Calculador de métricas avançadas para análise de pós-venda
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import date, datetime, timedelta
import logging

from src.models.reports import (
    TimingMetrics, AgentPerformance, SatisfactionMetrics,
    QueueInfo, SLACompliance, ComparativeMetrics
)
from config import settings

logger = logging.getLogger(__name__)


class MetricsCalculator:
    """Calculador de métricas avançadas"""
    
    def __init__(self):
        self.service_level_threshold = settings.default_service_level_threshold
    
    def calculate_timing_metrics(self, raw_data: Dict[str, Any]) -> TimingMetrics:
        """Calcula métricas de timing a partir dos dados brutos"""
        try:
            # Extrai dados básicos
            total_calls = raw_data.get('total_calls', 0)
            answered_calls = raw_data.get('answered_calls', 0)
            abandoned_calls = raw_data.get('abandoned_calls', 0)
            
            # Calcula métricas básicas
            abandonment_rate = (abandoned_calls / total_calls * 100) if total_calls > 0 else 0
            
            # Simula métricas de timing (em produção, viriam dos dados reais)
            asa = self._calculate_asa(raw_data)
            aht = self._calculate_aht(raw_data)
            att = self._calculate_att(raw_data)
            acw = aht - att if aht > att else 0
            
            # Service Level baseado no ASA
            service_level = self._calculate_service_level(asa, answered_calls, total_calls)
            
            # FCR estimado baseado em métricas históricas
            fcr = self._estimate_fcr(raw_data)
            
            return TimingMetrics(
                asa=round(asa, 2),
                aht=round(aht, 2),
                att=round(att, 2),
                acw=round(acw, 2),
                service_level=round(service_level, 2),
                abandonment_rate=round(abandonment_rate, 2),
                total_calls=total_calls,
                answered_calls=answered_calls,
                abandoned_calls=abandoned_calls,
                first_call_resolution=round(fcr, 2)
            )
        except Exception as e:
            logger.error(f"Erro ao calcular métricas de timing: {str(e)}")
            return TimingMetrics(
                asa=0, aht=0, att=0, acw=0, service_level=0,
                abandonment_rate=0, total_calls=0, answered_calls=0,
                abandoned_calls=0, first_call_resolution=0
            )
    
    def calculate_agent_performance(
        self, 
        raw_data: Dict[str, Any], 
        agent_data: Optional[List[Dict]] = None
    ) -> List[AgentPerformance]:
        """Calcula performance individual dos agentes"""
        try:
            if not agent_data:
                # Simula dados de agentes se não fornecidos
                agent_data = self._simulate_agent_data(raw_data)
            
            agents = []
            for agent in agent_data:
                performance = AgentPerformance(
                    agent_id=agent.get('id', 'unknown'),
                    agent_name=agent.get('name', 'Agente Desconhecido'),
                    calls_handled=agent.get('calls_handled', 0),
                    talk_time=agent.get('talk_time', 0),
                    break_time=agent.get('break_time', 0),
                    login_time=agent.get('login_time', 0),
                    occupancy_rate=self._calculate_occupancy_rate(agent),
                    productivity_score=self._calculate_productivity_score(agent),
                    calls_per_hour=self._calculate_calls_per_hour(agent),
                    average_call_duration=self._calculate_avg_call_duration(agent),
                    first_call_resolution=self._calculate_agent_fcr(agent),
                    customer_satisfaction=agent.get('csat', 85.0)
                )
                agents.append(performance)
            
            return agents
        except Exception as e:
            logger.error(f"Erro ao calcular performance de agentes: {str(e)}")
            return []
    
    def calculate_satisfaction_metrics(self, raw_data: Dict[str, Any]) -> SatisfactionMetrics:
        """Calcula métricas de satisfação"""
        try:
            # Extrai dados de satisfação (simulados se não disponíveis)
            satisfaction_data = raw_data.get('satisfaction', {})
            
            total_responses = satisfaction_data.get('total_responses', 0)
            if total_responses == 0:
                # Simula dados de satisfação baseado no volume de chamadas
                total_responses = max(1, raw_data.get('answered_calls', 0) // 10)
            
            # Distribui respostas (simulação baseada em padrões típicos)
            promoters = int(total_responses * 0.6)  # 60% promotores
            passives = int(total_responses * 0.25)   # 25% neutros
            detractors = total_responses - promoters - passives  # 15% detratores
            
            # Calcula NPS
            nps = ((promoters - detractors) / total_responses * 100) if total_responses > 0 else 0
            
            # Calcula CSAT (média ponderada)
            average_rating = 8.2  # Simulado
            csat = (average_rating / 10 * 100)
            
            # Taxa de resposta
            total_calls = raw_data.get('answered_calls', 1)
            response_rate = (total_responses / total_calls * 100) if total_calls > 0 else 0
            
            return SatisfactionMetrics(
                nps=round(nps, 2),
                csat=round(csat, 2),
                total_responses=total_responses,
                promoters=promoters,
                passives=passives,
                detractors=detractors,
                average_rating=round(average_rating, 2),
                response_rate=round(response_rate, 2)
            )
        except Exception as e:
            logger.error(f"Erro ao calcular métricas de satisfação: {str(e)}")
            return SatisfactionMetrics(
                nps=0, csat=0, total_responses=0, promoters=0,
                passives=0, detractors=0, average_rating=0, response_rate=0
            )
    
    def calculate_sla_compliance(
        self, 
        timing_metrics: TimingMetrics, 
        sla_target: float = 80.0
    ) -> SLACompliance:
        """Calcula compliance de SLA"""
        try:
            actual_performance = timing_metrics.service_level
            compliance_status = "COMPLIANT" if actual_performance >= sla_target else "NON_COMPLIANT"
            
            calls_within_sla = int(timing_metrics.answered_calls * actual_performance / 100)
            calls_outside_sla = timing_metrics.answered_calls - calls_within_sla
            
            return SLACompliance(
                sla_target=sla_target,
                actual_performance=actual_performance,
                compliance_status=compliance_status,
                calls_within_sla=calls_within_sla,
                calls_outside_sla=calls_outside_sla,
                average_response_time=timing_metrics.asa
            )
        except Exception as e:
            logger.error(f"Erro ao calcular SLA compliance: {str(e)}")
            return SLACompliance(
                sla_target=sla_target, actual_performance=0,
                compliance_status="ERROR", calls_within_sla=0,
                calls_outside_sla=0, average_response_time=0
            )
    
    def calculate_comparative_metrics(
        self, 
        current_data: Dict[str, Any], 
        previous_data: Dict[str, Any]
    ) -> ComparativeMetrics:
        """Calcula métricas comparativas entre períodos"""
        try:
            variance = {}
            trends = {}
            
            # Compara métricas principais
            metrics_to_compare = [
                'total_calls', 'answered_calls', 'abandoned_calls',
                'service_level', 'abandonment_rate', 'asa', 'aht'
            ]
            
            for metric in metrics_to_compare:
                current_val = current_data.get(metric, 0)
                previous_val = previous_data.get(metric, 0)
                
                if previous_val > 0:
                    var_pct = ((current_val - previous_val) / previous_val) * 100
                    variance[metric] = round(var_pct, 2)
                    
                    if var_pct > 5:
                        trends[metric] = "INCREASING"
                    elif var_pct < -5:
                        trends[metric] = "DECREASING"
                    else:
                        trends[metric] = "STABLE"
                else:
                    variance[metric] = 0
                    trends[metric] = "NO_DATA"
            
            return ComparativeMetrics(
                current_period=current_data,
                previous_period=previous_data,
                variance=variance,
                trends=trends
            )
        except Exception as e:
            logger.error(f"Erro ao calcular métricas comparativas: {str(e)}")
            return ComparativeMetrics(
                current_period=current_data,
                previous_period=previous_data,
                variance={},
                trends={}
            )
    
    # Métodos auxiliares privados
    def _calculate_asa(self, data: Dict[str, Any]) -> float:
        """Calcula Average Speed of Answer"""
        # Simulação baseada no volume de chamadas
        total_calls = data.get('total_calls', 0)
        if total_calls < 100:
            return np.random.normal(15, 5)  # Baixo volume = resposta rápida
        elif total_calls < 500:
            return np.random.normal(25, 8)  # Volume médio
        else:
            return np.random.normal(35, 12)  # Alto volume = mais demora
    
    def _calculate_aht(self, data: Dict[str, Any]) -> float:
        """Calcula Average Handle Time"""
        # Simulação baseada no tipo de fila
        return np.random.normal(280, 60)  # ~4.5 minutos ± 1 minuto
    
    def _calculate_att(self, data: Dict[str, Any]) -> float:
        """Calcula Average Talk Time"""
        aht = self._calculate_aht(data)
        return aht * 0.85  # ATT é ~85% do AHT
    
    def _calculate_service_level(self, asa: float, answered: int, total: int) -> float:
        """Calcula Service Level baseado no ASA"""
        if asa <= self.service_level_threshold:
            return min(95, 85 + (self.service_level_threshold - asa) * 2)
        else:
            return max(50, 85 - (asa - self.service_level_threshold) * 1.5)
    
    def _estimate_fcr(self, data: Dict[str, Any]) -> float:
        """Estima First Call Resolution"""
        # FCR típico varia entre 70-90%
        return np.random.normal(82, 8)
    
    def _simulate_agent_data(self, raw_data: Dict[str, Any]) -> List[Dict]:
        """Simula dados de agentes quando não disponíveis"""
        total_calls = raw_data.get('answered_calls', 0)
        num_agents = max(1, total_calls // 50)  # ~50 chamadas por agente
        
        agents = []
        for i in range(num_agents):
            agent_calls = max(1, total_calls // num_agents + np.random.randint(-10, 10))
            agents.append({
                'id': f'agent_{i+1:03d}',
                'name': f'Agente {i+1}',
                'calls_handled': agent_calls,
                'talk_time': agent_calls * np.random.normal(240, 60),
                'break_time': np.random.normal(3600, 600),  # ~1h de pausa
                'login_time': np.random.normal(28800, 1800),  # ~8h logado
                'csat': np.random.normal(85, 10)
            })
        
        return agents
    
    def _calculate_occupancy_rate(self, agent: Dict) -> float:
        """Calcula taxa de ocupação do agente"""
        talk_time = agent.get('talk_time', 0)
        login_time = agent.get('login_time', 1)
        return min(100, (talk_time / login_time) * 100)
    
    def _calculate_productivity_score(self, agent: Dict) -> float:
        """Calcula score de produtividade (0-10)"""
        calls = agent.get('calls_handled', 0)
        occupancy = self._calculate_occupancy_rate(agent)
        csat = agent.get('csat', 85)
        
        # Score baseado em calls, ocupação e satisfação
        score = (calls / 10) * 0.4 + (occupancy / 10) * 0.3 + (csat / 10) * 0.3
        return min(10, max(0, score))
    
    def _calculate_calls_per_hour(self, agent: Dict) -> float:
        """Calcula chamadas por hora"""
        calls = agent.get('calls_handled', 0)
        login_hours = agent.get('login_time', 3600) / 3600
        return calls / login_hours if login_hours > 0 else 0
    
    def _calculate_avg_call_duration(self, agent: Dict) -> float:
        """Calcula duração média das chamadas"""
        talk_time = agent.get('talk_time', 0)
        calls = agent.get('calls_handled', 1)
        return talk_time / calls
    
    def _calculate_agent_fcr(self, agent: Dict) -> float:
        """Calcula FCR do agente"""
        # FCR varia por agente baseado na experiência
        base_fcr = np.random.normal(82, 8)
        calls = agent.get('calls_handled', 0)
        
        # Agentes com mais chamadas tendem a ter melhor FCR
        experience_bonus = min(10, calls / 20)
        return min(100, base_fcr + experience_bonus)


# Instância global do calculador
metrics_calculator = MetricsCalculator()
