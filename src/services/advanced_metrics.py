"""
Sistema de Métricas Avançadas para Dados 3CX
Processa e calcula métricas mais intuitivas e completas
"""
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import statistics
import math

logger = logging.getLogger(__name__)


class AdvancedMetricsCalculator:
    """Calculador de métricas avançadas para dados 3CX"""
    
    def __init__(self):
        self.service_level_threshold = 20  # segundos
        self.excellent_threshold = 90
        self.good_threshold = 75
        self.regular_threshold = 60
    
    def calculate_comprehensive_metrics(self, distribution_data: Dict, satisfaction_data: Dict) -> Dict[str, Any]:
        """Calcula métricas abrangentes combinando distribuição e satisfação"""
        try:
            metrics = {
                "timing_metrics": self._calculate_timing_metrics(distribution_data),
                "satisfaction_metrics": self._calculate_satisfaction_metrics(satisfaction_data),
                "efficiency_metrics": self._calculate_efficiency_metrics(distribution_data),
                "quality_metrics": self._calculate_quality_metrics(distribution_data, satisfaction_data),
                "agent_performance": self._analyze_agent_performance(satisfaction_data.get('Pesquisa Satisfação', {}).get('Pesquisa por Agente', {})) if satisfaction_data else {},
                "operational_insights": self._generate_operational_insights(distribution_data, satisfaction_data),
                "performance_classification": self._classify_overall_performance(distribution_data, satisfaction_data)
            }
            
            return metrics
        except Exception as e:
            logger.error(f"Erro ao calcular métricas abrangentes: {str(e)}")
            return {
                "timing_metrics": {},
                "satisfaction_metrics": {},
                "efficiency_metrics": {},
                "quality_metrics": {},
                "agent_performance": {},
                "operational_insights": ["📊 Erro ao processar dados ou nenhum dado disponível"],
                "performance_classification": {"level": "Sem dados", "score": 0}
            }
    
    def _calculate_timing_metrics(self, data: Dict) -> Dict[str, Any]:
        """Calcula métricas de timing mais detalhadas"""
        if not data or 'relatorio de distribuição' not in data:
            return {}
        
        dist_data = data['relatorio de distribuição']
        filas_data = dist_data.get('chamadas_por_filas', [])
        
        timing_metrics = {
            "asa_seconds": 0,
            "asa_formatted": "00:00:00",
            "aht_seconds": 0,
            "aht_formatted": "00:00:00",
            "service_level_percentage": 0,
            "abandonment_rate": 0,
            "answer_rate": 0,
            "peak_wait_time": "00:00:00",
            "min_wait_time": "00:00:00",
            "wait_time_variance": 0,
            "efficiency_score": 0
        }
        
        if filas_data:
            total_received = sum(int(f.get('Recebidas', 0)) for f in filas_data)
            total_answered = sum(int(f.get('Atendidas', 0)) for f in filas_data)
            total_abandoned = sum(int(f.get('Não-Atendidas', 0)) for f in filas_data)
            
            # Calcula médias ponderadas
            wait_times = []
            durations = []
            service_levels = []
            
            for fila in filas_data:
                received = int(fila.get('Recebidas', 0))
                if received > 0:
                    # Converte tempo de espera para segundos
                    wait_avg = self._time_to_seconds(fila.get('Espera Média', '00:00:00'))
                    wait_times.extend([wait_avg] * received)
                    
                    # Converte duração para segundos
                    duration_avg = self._time_to_seconds(fila.get('Duração Média', '00:00:00'))
                    durations.extend([duration_avg] * received)
                    
                    # Service level
                    sl = float(fila.get('Nível de serviço', 0))
                    service_levels.append(sl)
            
            if wait_times:
                timing_metrics["asa_seconds"] = statistics.mean(wait_times)
                timing_metrics["asa_formatted"] = self._seconds_to_time(timing_metrics["asa_seconds"])
                timing_metrics["wait_time_variance"] = statistics.variance(wait_times) if len(wait_times) > 1 else 0
            
            if durations:
                timing_metrics["aht_seconds"] = statistics.mean(durations)
                timing_metrics["aht_formatted"] = self._seconds_to_time(timing_metrics["aht_seconds"])
            
            if service_levels:
                timing_metrics["service_level_percentage"] = statistics.mean(service_levels)
            
            if total_received > 0:
                timing_metrics["abandonment_rate"] = (total_abandoned / total_received) * 100
                timing_metrics["answer_rate"] = (total_answered / total_received) * 100
            
            # Picos de espera (apenas filas com chamadas)
            max_waits = [self._time_to_seconds(f.get('Espera Máx', '00:00:00')) for f in filas_data if int(f.get('Recebidas', 0)) > 0]
            min_waits = [self._time_to_seconds(f.get('Espera Mín', '00:00:00')) for f in filas_data if int(f.get('Recebidas', 0)) > 0]

            if max_waits:
                timing_metrics["peak_wait_time"] = self._seconds_to_time(max(max_waits))
            if min_waits:
                timing_metrics["min_wait_time"] = self._seconds_to_time(min(min_waits))
            
            # Score de eficiência (0-100)
            timing_metrics["efficiency_score"] = self._calculate_efficiency_score(timing_metrics)
        
        return timing_metrics
    
    def _calculate_satisfaction_metrics(self, data: Dict) -> Dict[str, Any]:
        """Calcula métricas de satisfação mais detalhadas"""
        if not data or 'Pesquisa Satisfação' not in data:
            return {}
        
        sat_data = data['Pesquisa Satisfação']
        
        satisfaction_metrics = {
            "overall_satisfaction": 0,
            "participation_rate": 0,
            "nps_score": 0,
            "csat_score": 0,
            "satisfaction_by_type": {},
            "satisfaction_trend": "stable",
            "critical_agents": [],
            "top_performers": [],
            "improvement_areas": []
        }
        
        # Análise do sumário
        if 'sumario' in sat_data and 'Pesquisas Efetuadas' in sat_data['sumario']:
            pesquisas_efetuadas = sat_data['sumario']['Pesquisas Efetuadas']
            if pesquisas_efetuadas and len(pesquisas_efetuadas) > 0:
                summary = pesquisas_efetuadas[0]
                total_calls = summary.get('Sem Avaliacao', 0) + summary.get('Avaliadas', 0)
                evaluated = summary.get('Avaliadas', 0)

                if total_calls > 0:
                    satisfaction_metrics["participation_rate"] = (evaluated / total_calls) * 100
        
        # Análise por tipo de avaliação
        if 'Pesquisa por fila' in sat_data:
            fila_data = sat_data['Pesquisa por fila'].get('Pesquisa Amvox', {})
            
            # Av-1: Avalia Atendente
            if 'Av-1-Avalia Atendente' in fila_data:
                av1_list = fila_data['Av-1-Avalia Atendente']
                if av1_list and len(av1_list) > 0:
                    av1_data = av1_list[0]
                    av1_satisfaction = self._calculate_satisfaction_percentage(av1_data, 'binary')
                    satisfaction_metrics["satisfaction_by_type"]["atendente"] = av1_satisfaction

            # Av-2: Avalia Chamada
            if 'Av-2-Avalia Chamada' in fila_data:
                av2_list = fila_data['Av-2-Avalia Chamada']
                if av2_list and len(av2_list) > 0:
                    av2_data = av2_list[0]
                    av2_satisfaction = self._calculate_satisfaction_percentage(av2_data, 'yes_no')
                    satisfaction_metrics["satisfaction_by_type"]["chamada"] = av2_satisfaction

            # Av-3: Avalia Empresa
            if 'Av-3-Avalia Empresa' in fila_data:
                av3_list = fila_data['Av-3-Avalia Empresa']
                if av3_list and len(av3_list) > 0:
                    av3_data = av3_list[0]
                    av3_satisfaction = self._calculate_satisfaction_percentage(av3_data, 'rating')
                    satisfaction_metrics["satisfaction_by_type"]["empresa"] = av3_satisfaction
        
        # Calcula satisfação geral
        type_satisfactions = list(satisfaction_metrics["satisfaction_by_type"].values())
        if type_satisfactions:
            satisfaction_metrics["overall_satisfaction"] = statistics.mean(type_satisfactions)
        
        # Calcula NPS e CSAT
        satisfaction_metrics["nps_score"] = self._calculate_nps(satisfaction_metrics["overall_satisfaction"])
        satisfaction_metrics["csat_score"] = satisfaction_metrics["overall_satisfaction"]
        
        # Análise de agentes
        if 'Pesquisa por Agente' in sat_data:
            agent_analysis = self._analyze_agent_performance(sat_data['Pesquisa por Agente'])
            satisfaction_metrics.update(agent_analysis)
        
        return satisfaction_metrics
    
    def _calculate_efficiency_metrics(self, data: Dict) -> Dict[str, Any]:
        """Calcula métricas de eficiência operacional"""
        if not data or 'relatorio de distribuição' not in data:
            return {}
        
        dist_data = data['relatorio de distribuição']
        
        efficiency_metrics = {
            "resource_utilization": 0,
            "peak_hour_efficiency": 0,
            "queue_balance": 0,
            "capacity_utilization": 0,
            "operational_efficiency": 0
        }
        
        # Análise por hora
        if 'Chamadas por hora' in dist_data:
            hourly_data = dist_data['Chamadas por hora']
            if hourly_data:
                hourly_volumes = [int(h.get('Chamadas', 0)) for h in hourly_data]
                if hourly_volumes:
                    max_volume = max(hourly_volumes)
                    avg_volume = statistics.mean(hourly_volumes)
                    efficiency_metrics["peak_hour_efficiency"] = (avg_volume / max_volume) * 100 if max_volume > 0 else 0
        
        # Análise por fila
        if 'chamadas_por_filas' in dist_data:
            filas_data = dist_data['chamadas_por_filas']
            if filas_data:
                queue_volumes = [int(f.get('Recebidas', 0)) for f in filas_data]
                if queue_volumes:
                    # Balanceamento entre filas
                    volume_variance = statistics.variance(queue_volumes) if len(queue_volumes) > 1 else 0
                    max_volume = max(queue_volumes)
                    efficiency_metrics["queue_balance"] = max(0, 100 - (volume_variance / max_volume * 100)) if max_volume > 0 else 100
                
                # Utilização de capacidade
                answer_rates = [float(f.get('Taxa de Atendidas', 0)) for f in filas_data]
                if answer_rates:
                    efficiency_metrics["capacity_utilization"] = statistics.mean(answer_rates)
        
        # Score geral de eficiência
        efficiency_scores = [
            efficiency_metrics["peak_hour_efficiency"],
            efficiency_metrics["queue_balance"],
            efficiency_metrics["capacity_utilization"]
        ]
        valid_scores = [s for s in efficiency_scores if s > 0]
        if valid_scores:
            efficiency_metrics["operational_efficiency"] = statistics.mean(valid_scores)
        
        return efficiency_metrics
    
    def _calculate_quality_metrics(self, distribution_data: Dict, satisfaction_data: Dict) -> Dict[str, Any]:
        """Calcula métricas de qualidade combinadas"""
        quality_metrics = {
            "service_quality_index": 0,
            "customer_experience_score": 0,
            "first_call_resolution_estimate": 0,
            "quality_consistency": 0
        }
        
        # Combina métricas de timing e satisfação para calcular qualidade
        timing = self._calculate_timing_metrics(distribution_data)
        satisfaction = self._calculate_satisfaction_metrics(satisfaction_data)
        
        if timing and satisfaction:
            # Índice de qualidade de serviço (0-100)
            service_level = timing.get("service_level_percentage", 0)
            overall_satisfaction = satisfaction.get("overall_satisfaction", 0)
            
            quality_metrics["service_quality_index"] = (service_level * 0.6 + overall_satisfaction * 0.4)
            
            # Score de experiência do cliente
            asa_score = max(0, 100 - (timing.get("asa_seconds", 0) / 60 * 10))  # Penaliza espera longa
            quality_metrics["customer_experience_score"] = (asa_score * 0.3 + overall_satisfaction * 0.7)
            
            # Estimativa de FCR baseada em satisfação e eficiência
            participation_rate = satisfaction.get("participation_rate", 0)
            quality_metrics["first_call_resolution_estimate"] = min(100, overall_satisfaction * 0.8 + participation_rate * 0.2)
        
        return quality_metrics
    
    def _time_to_seconds(self, time_str: str) -> float:
        """Converte string de tempo HH:MM:SS para segundos"""
        try:
            parts = time_str.split(':')
            if len(parts) == 3:
                hours, minutes, seconds = map(int, parts)
                return hours * 3600 + minutes * 60 + seconds
            return 0
        except:
            return 0
    
    def _seconds_to_time(self, seconds: float) -> str:
        """Converte segundos para string HH:MM:SS"""
        try:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            secs = int(seconds % 60)
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        except:
            return "00:00:00"
    
    def _calculate_satisfaction_percentage(self, data: Dict, eval_type: str) -> float:
        """Calcula percentual de satisfação baseado no tipo de avaliação"""
        try:
            avaliadas = data.get('Avaliadas', 0)
            if avaliadas == 0:
                return 0
            
            if eval_type == 'binary':  # Satisfeito/Insatisfeito
                satisfeitos = data.get('Satisfeito', 0)
                return (satisfeitos / avaliadas) * 100
            
            elif eval_type == 'yes_no':  # Sim/Não
                sim = data.get('Sim', 0)
                return (sim / avaliadas) * 100
            
            elif eval_type == 'rating':  # Notas 1-5
                nota4 = data.get('Nota 4', 0)
                nota5 = data.get('Nota 5', 0)
                return ((nota4 + nota5) / avaliadas) * 100
            
            return 0
        except:
            return 0
    
    def _calculate_nps(self, satisfaction_percentage: float) -> float:
        """Calcula NPS estimado baseado na satisfação"""
        # Conversão aproximada de satisfação para NPS
        if satisfaction_percentage >= 90:
            return satisfaction_percentage - 40  # NPS alto
        elif satisfaction_percentage >= 70:
            return satisfaction_percentage - 50  # NPS médio
        else:
            return satisfaction_percentage - 60  # NPS baixo
    
    def _calculate_efficiency_score(self, timing_metrics: Dict) -> float:
        """Calcula score de eficiência baseado nas métricas de timing"""
        try:
            service_level = timing_metrics.get("service_level_percentage", 0)
            answer_rate = timing_metrics.get("answer_rate", 0)
            asa_seconds = timing_metrics.get("asa_seconds", 0)
            
            # Score baseado em service level (40%), answer rate (40%) e ASA (20%)
            asa_score = max(0, 100 - (asa_seconds / 60 * 20))  # Penaliza ASA alto
            
            efficiency = (service_level * 0.4 + answer_rate * 0.4 + asa_score * 0.2)
            return min(100, max(0, efficiency))
        except:
            return 0

    def _analyze_agent_performance(self, agent_data: Dict) -> Dict[str, Any]:
        """Analisa performance individual dos agentes"""
        analysis = {
            "critical_agents": [],
            "top_performers": [],
            "improvement_areas": []
        }

        try:
            if 'Pesquisa Amvox' in agent_data and 'Av-1-Avalia Atendente' in agent_data['Pesquisa Amvox']:
                agents = agent_data['Pesquisa Amvox']['Av-1-Avalia Atendente']

                agent_performances = []

                # Verifica se agents é um dicionário ou lista
                if isinstance(agents, dict):
                    # Analisa cada agente
                    for agent_name, agent_info in agents.items():
                        if isinstance(agent_info, dict):
                            satisfeitos = agent_info.get('Satisfeitos', 0)
                            insatisfeitos = agent_info.get('Insatisfeitos', 0)
                            total = satisfeitos + insatisfeitos

                            if total > 0:
                                satisfaction_rate = (satisfeitos / total) * 100
                                agent_performances.append({
                                    'name': agent_name.replace('_', ' '),
                                    'satisfaction_rate': satisfaction_rate,
                                    'total_evaluations': total,
                                    'satisfied': satisfeitos,
                                    'unsatisfied': insatisfeitos
                                })
                elif isinstance(agents, list):
                    # Se for lista, processa como array
                    for agent_info in agents:
                        if isinstance(agent_info, dict):
                            agent_name = agent_info.get('Agente', 'Agente_Desconhecido')
                            satisfeitos = agent_info.get('Satisfeitos', 0)
                            insatisfeitos = agent_info.get('Insatisfeitos', 0)
                            total = satisfeitos + insatisfeitos

                            if total > 0:
                                satisfaction_rate = (satisfeitos / total) * 100
                                agent_performances.append({
                                    'name': agent_name.replace('_', ' '),
                                    'satisfaction_rate': satisfaction_rate,
                                    'total_evaluations': total,
                                    'satisfied': satisfeitos,
                                    'unsatisfied': insatisfeitos
                                })

                # Ordena por performance
                agent_performances.sort(key=lambda x: x['satisfaction_rate'], reverse=True)

                # Identifica top performers (top 30% ou satisfação >= 90%)
                if agent_performances:
                    top_count = max(1, len(agent_performances) // 3)
                    analysis["top_performers"] = [
                        agent['name'] for agent in agent_performances[:top_count]
                        if agent['satisfaction_rate'] >= 85
                    ]

                    # Identifica agentes críticos (satisfação < 70%)
                    analysis["critical_agents"] = [
                        {
                            'name': agent['name'],
                            'satisfaction_rate': agent['satisfaction_rate'],
                            'total_evaluations': agent['total_evaluations']
                        }
                        for agent in agent_performances
                        if agent['satisfaction_rate'] < 70 and agent['total_evaluations'] >= 3
                    ]

                    # Identifica áreas de melhoria
                    avg_satisfaction = statistics.mean([a['satisfaction_rate'] for a in agent_performances])
                    low_performers = [a for a in agent_performances if a['satisfaction_rate'] < avg_satisfaction * 0.8]

                    if low_performers:
                        analysis["improvement_areas"].append(f"{len(low_performers)} agentes abaixo da média da equipe")

                    if avg_satisfaction < 80:
                        analysis["improvement_areas"].append("Satisfação geral da equipe abaixo do ideal")

                    participation_rates = [a['total_evaluations'] for a in agent_performances]
                    if participation_rates and statistics.mean(participation_rates) < 5:
                        analysis["improvement_areas"].append("Baixa participação nas pesquisas de satisfação")

        except Exception as e:
            logger.error(f"Erro na análise de agentes: {str(e)}")

        return analysis

    def _generate_operational_insights(self, distribution_data: Dict, satisfaction_data: Dict) -> List[str]:
        """Gera insights operacionais baseados nos dados"""
        insights = []

        try:
            # Análise de distribuição
            if distribution_data and 'relatorio de distribuição' in distribution_data:
                dist_data = distribution_data['relatorio de distribuição']

                # Análise por hora
                if 'Chamadas por hora' in dist_data:
                    hourly_data = dist_data['Chamadas por hora']
                    if hourly_data:
                        # Filtra apenas horários com chamadas recebidas
                        hourly_volumes = [(h.get('Hora', ''), int(h.get('Recebidas', 0))) for h in hourly_data if int(h.get('Recebidas', 0)) > 0]
                        hourly_volumes.sort(key=lambda x: x[1], reverse=True)

                        if hourly_volumes:
                            peak_hour, peak_volume = hourly_volumes[0]
                            insights.append(f"📈 Horário de maior volume: {peak_hour} ({peak_volume} chamadas)")

                            # Identifica horários críticos (volume muito baixo ou muito alto)
                            if len(hourly_volumes) > 1:
                                avg_volume = statistics.mean([v[1] for v in hourly_volumes])
                                critical_hours = [h for h, v in hourly_volumes if v > avg_volume * 1.5]
                                if critical_hours:
                                    insights.append(f"⚠️ Horários críticos identificados: {', '.join(critical_hours[:3])}")
                        else:
                            insights.append("📊 Nenhuma chamada recebida no período analisado")

                # Análise por fila
                if 'chamadas_por_filas' in dist_data:
                    filas_data = dist_data['chamadas_por_filas']
                    for fila in filas_data:
                        fila_name = fila.get('Filas', '')
                        recebidas = int(fila.get('Recebidas', 0))

                        # Só analisa filas que receberam chamadas
                        if recebidas > 0:
                            service_level = float(fila.get('Nível de serviço', 0))
                            answer_rate = float(fila.get('Taxa de Atendidas', 0))
                            abandonment_rate = float(fila.get('Taxa de Não-Atendidas', 0))

                            # Análise de Service Level
                            if service_level >= 80:
                                insights.append(f"✅ {fila_name}: Service Level adequado ({service_level}%)")
                            elif service_level >= 60:
                                insights.append(f"⚠️ {fila_name}: Service Level precisa melhorar ({service_level}%)")
                            else:
                                insights.append(f"🚨 {fila_name}: Service Level crítico ({service_level}%)")

                            # Análise de Taxa de Abandono
                            if abandonment_rate > 10:
                                insights.append(f"📞 {fila_name}: Taxa de abandono alta ({abandonment_rate:.1f}%)")
                            elif abandonment_rate > 0:
                                insights.append(f"⚠️ {fila_name}: Taxa de abandono moderada ({abandonment_rate:.1f}%)")
                            else:
                                insights.append(f"✅ {fila_name}: Taxa de abandono baixa, dentro do padrão aceitável")
                        else:
                            insights.append(f"📊 {fila_name}: Sem chamadas recebidas no período")

            # Análise de satisfação
            if satisfaction_data and 'Pesquisa Satisfação' in satisfaction_data:
                sat_data = satisfaction_data['Pesquisa Satisfação']

                if 'sumario' in sat_data and 'Pesquisas Efetuadas' in sat_data['sumario']:
                    pesquisas_efetuadas = sat_data['sumario']['Pesquisas Efetuadas']
                    if pesquisas_efetuadas and len(pesquisas_efetuadas) > 0:
                        summary = pesquisas_efetuadas[0]
                        participation_rate = float(summary.get('% Avaliadas', 0))

                        if participation_rate >= 50:
                            insights.append(f"📊 Boa participação nas pesquisas: {participation_rate}%")
                        elif participation_rate >= 30:
                            insights.append(f"⚠️ Participação moderada nas pesquisas: {participation_rate}%")
                        else:
                            insights.append(f"📉 Baixa participação nas pesquisas: {participation_rate}%")

                # Análise por tipo de avaliação
                if 'Pesquisa por fila' in sat_data:
                    fila_data = sat_data['Pesquisa por fila'].get('Pesquisa Amvox', {})

                    for eval_type, eval_name in [
                        ('Av-1-Avalia Atendente', 'Avaliação do Atendente'),
                        ('Av-2-Avalia Chamada', 'Avaliação da Chamada'),
                        ('Av-3-Avalia Empresa', 'Avaliação da Empresa')
                    ]:
                        if eval_type in fila_data:
                            eval_list = fila_data[eval_type]
                            if eval_list and len(eval_list) > 0:
                                eval_data = eval_list[0]
                                satisfaction_pct = self._calculate_satisfaction_percentage(
                                    eval_data,
                                    'binary' if 'Satisfeito' in eval_data else
                                    'yes_no' if 'Sim' in eval_data else 'rating'
                                )

                                if satisfaction_pct >= 85:
                                    insights.append(f"🌟 {eval_name}: Excelente ({satisfaction_pct:.1f}%)")
                                elif satisfaction_pct >= 70:
                                    insights.append(f"👍 {eval_name}: Boa ({satisfaction_pct:.1f}%)")
                                elif satisfaction_pct >= 50:
                                    insights.append(f"⚠️ {eval_name}: Regular ({satisfaction_pct:.1f}%)")
                                else:
                                    insights.append(f"🚨 {eval_name}: Crítica ({satisfaction_pct:.1f}%)")

        except Exception as e:
            logger.error(f"Erro ao gerar insights operacionais: {str(e)}")

        # Se não há insights, adiciona mensagem informativa
        if not insights:
            insights.append("📊 Nenhum dado disponível para o período e filas selecionadas")

        return insights[:10]  # Limita a 10 insights mais relevantes

    def _classify_overall_performance(self, distribution_data: Dict, satisfaction_data: Dict) -> Dict[str, Any]:
        """Classifica a performance geral do sistema"""
        classification = {
            "level": "Regular",
            "score": 0,
            "strengths": [],
            "weaknesses": [],
            "priority_actions": []
        }

        try:
            timing_metrics = self._calculate_timing_metrics(distribution_data)
            satisfaction_metrics = self._calculate_satisfaction_metrics(satisfaction_data)

            scores = []

            # Avalia timing
            if timing_metrics:
                service_level = timing_metrics.get("service_level_percentage", 0)
                answer_rate = timing_metrics.get("answer_rate", 0)

                if service_level >= 80:
                    classification["strengths"].append("Service Level adequado")
                    scores.append(85)
                elif service_level >= 60:
                    classification["weaknesses"].append("Service Level precisa melhorar")
                    scores.append(65)
                else:
                    classification["weaknesses"].append("Service Level crítico")
                    classification["priority_actions"].append("Melhorar tempo de resposta urgentemente")
                    scores.append(40)

                if answer_rate >= 95:
                    classification["strengths"].append("Excelente taxa de atendimento")
                    scores.append(90)
                elif answer_rate >= 90:
                    classification["strengths"].append("Boa taxa de atendimento")
                    scores.append(80)
                else:
                    classification["weaknesses"].append("Taxa de abandono alta")
                    classification["priority_actions"].append("Reduzir taxa de abandono")
                    scores.append(60)

            # Avalia satisfação
            if satisfaction_metrics:
                overall_satisfaction = satisfaction_metrics.get("overall_satisfaction", 0)
                participation_rate = satisfaction_metrics.get("participation_rate", 0)

                if overall_satisfaction >= 85:
                    classification["strengths"].append("Alta satisfação dos clientes")
                    scores.append(90)
                elif overall_satisfaction >= 70:
                    classification["strengths"].append("Satisfação adequada dos clientes")
                    scores.append(75)
                else:
                    classification["weaknesses"].append("Satisfação dos clientes baixa")
                    classification["priority_actions"].append("Implementar plano de melhoria da satisfação")
                    scores.append(50)

                if participation_rate >= 40:
                    classification["strengths"].append("Boa participação nas pesquisas")
                elif participation_rate < 20:
                    classification["weaknesses"].append("Baixa participação nas pesquisas")
                    classification["priority_actions"].append("Incentivar participação nas pesquisas")

            # Calcula score geral
            if scores:
                classification["score"] = statistics.mean(scores)

                if classification["score"] >= 85:
                    classification["level"] = "Excelente"
                elif classification["score"] >= 75:
                    classification["level"] = "Bom"
                elif classification["score"] >= 60:
                    classification["level"] = "Regular"
                else:
                    classification["level"] = "Crítico"

        except Exception as e:
            logger.error(f"Erro na classificação de performance: {str(e)}")

        return classification


# Instância global do calculador
advanced_metrics = AdvancedMetricsCalculator()
