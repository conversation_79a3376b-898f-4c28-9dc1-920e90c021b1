"""
Versão simplificada do Sistema de Relatórios 3CX
"""
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from datetime import date, datetime, timedelta
import httpx
import asyncio
import os
import logging
import io
import base64
from typing import Optional
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.graphics.shapes import Drawing
from reportlab.graphics.charts.piecharts import Pie
from reportlab.graphics.charts.barcharts import VerticalBarChart
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Criação da aplicação FastAPI
app = FastAPI(
    title="Sistema de Relatórios 3CX - Amvox",
    description="Sistema completo de análise de métricas de pós-venda",
    version="2.1.0"
)

# Configuração de templates
os.makedirs("templates", exist_ok=True)
os.makedirs("static", exist_ok=True)
templates = Jinja2Templates(directory="templates")

# Configurações básicas
THREECX_API_URL = "http://ticmobilerb.ddns.net/gdacip/apijson.php"
THREECX_USER = "amvox"
THREECX_PASSWORD = "super_7894"
AVAILABLE_QUEUES = [802, 803]


# Funções para geração de PDF
def create_chart_image(chart_type, data, title):
    """Cria uma imagem de gráfico usando matplotlib"""
    plt.figure(figsize=(8, 6))
    plt.style.use('default')

    if chart_type == 'pie':
        labels = list(data.keys())
        values = list(data.values())
        colors_list = ['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6']
        plt.pie(values, labels=labels, autopct='%1.1f%%', colors=colors_list[:len(labels)])
        plt.title(title, fontsize=14, fontweight='bold')

    elif chart_type == 'bar':
        labels = list(data.keys())
        values = list(data.values())
        plt.bar(labels, values, color='#3498db')
        plt.title(title, fontsize=14, fontweight='bold')
        plt.xticks(rotation=45, ha='right')
        plt.ylabel('Quantidade')

    elif chart_type == 'line':
        x_values = list(data.keys())
        y_values = list(data.values())
        plt.plot(x_values, y_values, marker='o', linewidth=2, color='#3498db')
        plt.title(title, fontsize=14, fontweight='bold')
        plt.xticks(rotation=45, ha='right')
        plt.ylabel('Valores')
        plt.grid(True, alpha=0.3)

    plt.tight_layout()

    # Salva em buffer
    img_buffer = io.BytesIO()
    plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
    img_buffer.seek(0)
    plt.close()

    return img_buffer


def generate_pdf_report(report_data, report_type, period_info):
    """Gera relatório em PDF"""
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []

    # Estilo personalizado para título
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        textColor=colors.HexColor('#3498db'),
        alignment=1  # Center
    )

    # Cabeçalho
    title = f"Relatório {report_type.title()} - AMVOX"
    story.append(Paragraph(title, title_style))

    # Informações do período
    period_text = f"Período: {period_info.get('start', 'N/A')} a {period_info.get('end', 'N/A')}"
    story.append(Paragraph(period_text, styles['Normal']))
    story.append(Spacer(1, 20))

    # Data de geração
    generation_date = f"Gerado em: {datetime.now().strftime('%d/%m/%Y às %H:%M')}"
    story.append(Paragraph(generation_date, styles['Normal']))
    story.append(Spacer(1, 30))

    if report_type == 'distribution':
        story.extend(generate_distribution_pdf_content(report_data, styles))
    elif report_type == 'satisfaction':
        story.extend(generate_satisfaction_pdf_content(report_data, styles))
    elif report_type == 'agents':
        story.extend(generate_agents_pdf_content(report_data, styles))

    doc.build(story)
    buffer.seek(0)
    return buffer


def generate_distribution_pdf_content(data, styles):
    """Gera conteúdo PDF para relatório de distribuição"""
    content = []

    if not data or 'data' not in data:
        content.append(Paragraph("Dados não disponíveis", styles['Normal']))
        return content

    report_data = data['data']

    # Título da seção
    content.append(Paragraph("Relatório de Distribuição de Chamadas", styles['Heading2']))
    content.append(Spacer(1, 12))

    # Resumo geral
    if 'relatorio de distribuição' in report_data:
        dist_data = report_data['relatorio de distribuição']

        if 'sumario' in dist_data:
            summary = dist_data['sumario']
            content.append(Paragraph("Resumo Geral", styles['Heading3']))

            # Informações do relatório
            if 'Informações do relatório' in summary:
                info = summary['Informações do relatório']
                content.append(Paragraph(f"<b>Filas:</b> {info.get('Filas', 'N/A')}", styles['Normal']))
                content.append(Paragraph(f"<b>Período:</b> {info.get('Data de início', 'N/A')} a {info.get('Data de fim', 'N/A')}", styles['Normal']))
                content.append(Spacer(1, 12))

            # Tabela de resumo
            summary_data = [['Métrica', 'Valor']]
            if 'Total de chamadas' in summary:
                total_calls = summary['Total de chamadas']
                summary_data.append(['Chamadas Conectadas', str(total_calls.get('Número de chamadas conectadas', 0))])
                summary_data.append(['Chamadas Atendidas', str(total_calls.get('Número de chamadas atendidas', 0))])
                summary_data.append(['Chamadas Não-Atendidas (PA)', str(total_calls.get('Número de chamadas não-atendidas pela PA', 0))])
                summary_data.append(['Chamadas Não-Atendidas (Fila)', str(total_calls.get('Número de chamadas não-atendidas na fila', 0))])
                summary_data.append(['Taxa de Não Atendidas', f"{total_calls.get('Taxa de Nao Atendidas', '0.00')}%"])
                summary_data.append(['Taxa de Abandono', f"{total_calls.get('Taxa de Abandono', '0.00')}%"])

            table = Table(summary_data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3498db')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            content.append(table)
            content.append(Spacer(1, 20))

        # Dados por fila
        if 'chamadas_por_filas' in dist_data:
            content.append(Paragraph("Distribuição por Fila", styles['Heading3']))

            filas_data = [['Fila', 'Recebidas', 'Atendidas', 'Não Atendidas', 'Service Level', 'Taxa Atendidas', 'Duração Média', 'Espera Média']]
            for fila in dist_data['chamadas_por_filas']:
                filas_data.append([
                    str(fila.get('Filas', 'N/A')),
                    str(fila.get('Recebidas', 0)),
                    str(fila.get('Atendidas', 0)),
                    str(fila.get('Não-Atendidas', 0)),
                    f"{fila.get('Nível de serviço', 0)}%",
                    f"{fila.get('Taxa de Atendidas', 0)}%",
                    str(fila.get('Duração Média', '00:00:00')),
                    str(fila.get('Espera Média', '00:00:00'))
                ])

            table = Table(filas_data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2ecc71')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 9),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            content.append(table)
            content.append(Spacer(1, 20))

        # Chamadas por hora (resumo dos horários de pico)
        if 'Chamadas por hora' in dist_data:
            content.append(Paragraph("Horários de Pico", styles['Heading3']))

            # Filtra apenas horários com chamadas
            horarios_com_chamadas = [h for h in dist_data['Chamadas por hora'] if h.get('Recebidas', 0) > 0]

            if horarios_com_chamadas:
                hora_data = [['Horário', 'Recebidas', 'Atendidas', 'Service Level', 'Espera Média']]
                for hora in horarios_com_chamadas:
                    hora_data.append([
                        str(hora.get('Hora', 'N/A')),
                        str(hora.get('Recebidas', 0)),
                        str(hora.get('Atendidas', 0)),
                        f"{hora.get('Nível de serviço', 0)}%",
                        str(hora.get('Espera Média', '00:00:00'))
                    ])

                table = Table(hora_data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#f39c12')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                content.append(table)
            else:
                content.append(Paragraph("Nenhuma chamada registrada no período.", styles['Normal']))

    # Adiciona análise automática
    content.append(Spacer(1, 30))
    analysis = analyze_distribution_metrics(data)
    analysis_content = format_analysis_for_pdf(analysis, "Análise de Performance - Distribuição")
    content.extend(analysis_content)

    return content


def generate_satisfaction_pdf_content(data, styles):
    """Gera conteúdo PDF para relatório de satisfação"""
    content = []

    if not data or 'data' not in data:
        content.append(Paragraph("Dados não disponíveis", styles['Normal']))
        return content

    report_data = data['data']

    # Título da seção
    content.append(Paragraph("Relatório de Pesquisa de Satisfação", styles['Heading2']))
    content.append(Spacer(1, 12))

    if 'Pesquisa Satisfação' in report_data:
        satisfaction_data = report_data['Pesquisa Satisfação']

        # Resumo das pesquisas
        if 'sumario' in satisfaction_data:
            summary = satisfaction_data['sumario']
            content.append(Paragraph("Resumo Geral das Pesquisas", styles['Heading3']))

            # Informações do relatório
            if 'Informações do relatório' in summary:
                info = summary['Informações do relatório']
                content.append(Paragraph(f"<b>Filas:</b> {info.get('Filas', 'N/A')}", styles['Normal']))
                content.append(Paragraph(f"<b>Período:</b> {info.get('Data de início', 'N/A')} a {info.get('Data de fim', 'N/A')}", styles['Normal']))
                content.append(Spacer(1, 12))

            if 'Pesquisas Efetuadas' in summary:
                pesquisas = summary['Pesquisas Efetuadas'][0]
                summary_data = [['Métrica', 'Valor', 'Percentual']]
                total_pesquisas = pesquisas.get('Sem Avaliacao', 0) + pesquisas.get('Avaliadas', 0)
                summary_data.append(['Total de Pesquisas', str(total_pesquisas), '100%'])
                summary_data.append(['Sem Avaliação', str(pesquisas.get('Sem Avaliacao', 0)), f"{pesquisas.get('% Sem Avaliação', 0)}%"])
                summary_data.append(['Avaliadas', str(pesquisas.get('Avaliadas', 0)), f"{pesquisas.get('% Avaliadas', 0)}%"])
                summary_data.append(['Av-1 (Atendente)', str(pesquisas.get('Av-1', 0)), f"{pesquisas.get('% meta Av-1', 0)}%"])
                summary_data.append(['Av-2 (Chamada)', str(pesquisas.get('Av-2', 0)), f"{pesquisas.get('% meta Av-2', 0)}%"])
                summary_data.append(['Av-3 (Empresa)', str(pesquisas.get('Av-3', 0)), f"{pesquisas.get('% meta Av-3', 0)}%"])

                table = Table(summary_data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#e74c3c')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 11),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                content.append(table)
                content.append(Spacer(1, 20))

        # Avaliação por agente
        if 'Pesquisa por Agente' in satisfaction_data:
            agent_data = satisfaction_data['Pesquisa por Agente']
            if 'Pesquisa Amvox' in agent_data and 'Av-1-Avalia Atendente' in agent_data['Pesquisa Amvox']:
                content.append(Paragraph("Avaliação dos Atendentes (Av-1)", styles['Heading3']))

                agents = agent_data['Pesquisa Amvox']['Av-1-Avalia Atendente']
                agents_data = [['Agente', 'Sem Avaliação', 'Avaliadas', '% Avaliadas', 'Satisfeitos', 'Insatisfeitos', '% Meta']]

                for agent in agents:
                    if isinstance(agent, dict):
                        agents_data.append([
                            agent.get('Agente', 'N/A'),
                            str(agent.get('Sem Avaliacao', 0)),
                            str(agent.get('Avaliadas', 0)),
                            f"{agent.get('% Avaliadas', 0)}%",
                            str(agent.get('Satisfeito', 0)),
                            str(agent.get('Insatisfeito', 0)),
                            f"{agent.get('% Meta(2.0)', 0)}%"
                        ])

                table = Table(agents_data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#f39c12')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 9),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                content.append(table)
                content.append(Spacer(1, 20))

            # Avaliação da Chamada (Av-2)
            if 'Pesquisa Amvox' in agent_data and 'Av-2-Avalia Chamada' in agent_data['Pesquisa Amvox']:
                content.append(Paragraph("Avaliação da Chamada (Av-2)", styles['Heading3']))

                agents = agent_data['Pesquisa Amvox']['Av-2-Avalia Chamada']
                agents_data = [['Agente', 'Sem Avaliação', 'Avaliadas', '% Avaliadas', 'Sim', 'Não', '% Meta']]

                for agent in agents:
                    if isinstance(agent, dict):
                        agents_data.append([
                            agent.get('Agente', 'N/A'),
                            str(agent.get('Sem Avaliacao', 0)),
                            str(agent.get('Avaliadas', 0)),
                            f"{agent.get('% Avaliadas', 0)}%",
                            str(agent.get('Sim', 0)),
                            str(agent.get('Não', 0)),
                            f"{agent.get('% Meta(2.0)', 0)}%"
                        ])

                table = Table(agents_data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2ecc71')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 9),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                content.append(table)
                content.append(Spacer(1, 20))

            # Avaliação da Empresa (Av-3)
            if 'Pesquisa Amvox' in agent_data and 'Av-3-Avalia Empresa' in agent_data['Pesquisa Amvox']:
                content.append(Paragraph("Avaliação da Empresa (Av-3)", styles['Heading3']))

                agents = agent_data['Pesquisa Amvox']['Av-3-Avalia Empresa']
                agents_data = [['Agente', 'Sem Avaliação', 'Avaliadas', '% Avaliadas', 'Nota 1', 'Nota 2', 'Nota 3', 'Nota 4', 'Nota 5', '% Meta']]

                for agent in agents:
                    if isinstance(agent, dict):
                        agents_data.append([
                            agent.get('Agente', 'N/A'),
                            str(agent.get('Sem Avaliacao', 0)),
                            str(agent.get('Avaliadas', 0)),
                            f"{agent.get('% Avaliadas', 0)}%",
                            str(agent.get('Nota 1', 0)),
                            str(agent.get('Nota 2', 0)),
                            str(agent.get('Nota 3', 0)),
                            str(agent.get('Nota 4', 0)),
                            str(agent.get('Nota 5', 0)),
                            f"{agent.get('% Meta(5.0)', 0)}%"
                        ])

                table = Table(agents_data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#9b59b6')),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                content.append(table)

    # Adiciona análise automática
    content.append(Spacer(1, 30))
    analysis = analyze_satisfaction_metrics(data)
    analysis_content = format_analysis_for_pdf(analysis, "Análise de Performance - Satisfação")
    content.extend(analysis_content)

    return content


def generate_agents_pdf_content(data, styles):
    """Gera conteúdo PDF para relatório de agentes"""
    content = []

    if not data or 'data' not in data:
        content.append(Paragraph("Dados não disponíveis", styles['Normal']))
        return content

    # Título da seção
    content.append(Paragraph("Relatório de Performance dos Agentes", styles['Heading2']))
    content.append(Spacer(1, 12))

    agents_data = data['data']

    # Verifica se os dados são uma lista (estrutura real da API)
    if isinstance(agents_data, list):
        content.append(Paragraph("Resumo de Performance dos Agentes", styles['Heading3']))

        agents_table = [['Agente', 'Sem Avaliação', 'Avaliadas', '% Avaliadas', 'Satisfeitos', 'Insatisfeitos', '% Satisfação', 'Status']]

        for agent in agents_data:
            if isinstance(agent, dict):
                satisfeitos = agent.get('Satisfeito', 0)
                insatisfeitos = agent.get('Insatisfeito', 0)
                total_avaliacoes = agent.get('Avaliadas', 0)
                sem_avaliacao = agent.get('Sem Avaliacao', 0)
                percent_avaliadas = agent.get('% Avaliadas', '0.00')

                # Calcula percentual de satisfação
                if total_avaliacoes > 0:
                    percent_satisfacao = (satisfeitos / total_avaliacoes * 100)
                else:
                    percent_satisfacao = 0

                # Define status baseado na satisfação
                if percent_satisfacao >= 90:
                    status = "Excelente"
                elif percent_satisfacao >= 70:
                    status = "Bom"
                elif percent_satisfacao >= 50:
                    status = "Regular"
                else:
                    status = "Precisa Melhorar"

                agents_table.append([
                    agent.get('Agente', 'N/A'),
                    str(sem_avaliacao),
                    str(total_avaliacoes),
                    f"{percent_avaliadas}%",
                    str(satisfeitos),
                    str(insatisfeitos),
                    f"{percent_satisfacao:.1f}%",
                    status
                ])

        table = Table(agents_table)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#9b59b6')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        content.append(table)
        content.append(Spacer(1, 20))

        # Adiciona análise de performance
        content.append(Paragraph("Análise de Performance", styles['Heading3']))

        # Calcula estatísticas
        total_agentes = len(agents_data)
        agentes_excelentes = sum(1 for agent in agents_data if (agent.get('Satisfeito', 0) / max(agent.get('Avaliadas', 1), 1) * 100) >= 90)
        agentes_bons = sum(1 for agent in agents_data if 70 <= (agent.get('Satisfeito', 0) / max(agent.get('Avaliadas', 1), 1) * 100) < 90)

        analysis_data = [['Métrica', 'Valor']]
        analysis_data.append(['Total de Agentes', str(total_agentes)])
        analysis_data.append(['Agentes com Performance Excelente (≥90%)', str(agentes_excelentes)])
        analysis_data.append(['Agentes com Performance Boa (70-89%)', str(agentes_bons)])
        analysis_data.append(['Taxa de Agentes com Performance Satisfatória', f"{((agentes_excelentes + agentes_bons) / max(total_agentes, 1) * 100):.1f}%"])

        table = Table(analysis_data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#34495e')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        content.append(table)

    elif isinstance(agents_data, dict):
        # Estrutura alternativa (caso seja um dicionário)
        content.append(Paragraph("Dados dos Agentes (Estrutura Alternativa)", styles['Heading3']))

        agents_table = [['Agente', 'Avaliações', '% Satisfação', 'Status']]

        for agent_name, agent_info in agents_data.items():
            if isinstance(agent_info, dict):
                satisfeitos = agent_info.get('Satisfeitos', 0)
                insatisfeitos = agent_info.get('Insatisfeitos', 0)
                total = satisfeitos + insatisfeitos
                percent = (satisfeitos / total * 100) if total > 0 else 0

                status = "Excelente" if percent >= 90 else "Bom" if percent >= 70 else "Regular" if percent >= 50 else "Precisa Melhorar"

                agents_table.append([
                    agent_name,
                    str(total),
                    f"{percent:.1f}%",
                    status
                ])

        table = Table(agents_table)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#9b59b6')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        content.append(table)

    # Adiciona análise automática para agentes
    content.append(Spacer(1, 30))

    # Análise geral da equipe
    if isinstance(agents_data, list) and agents_data:
        # Calcula métricas da equipe
        total_agentes = len(agents_data)
        agentes_com_dados = [a for a in agents_data if a.get('Avaliadas', 0) > 0]

        if agentes_com_dados:
            satisfacao_media = sum((a.get('Satisfeito', 0) / max(a.get('Avaliadas', 1), 1)) * 100
                                 for a in agentes_com_dados) / len(agentes_com_dados)

            team_analysis = {
                "insights": [
                    f"📊 Total de agentes analisados: {total_agentes}",
                    f"📈 Satisfação média da equipe: {satisfacao_media:.1f}%",
                ],
                "suggestions": [],
                "performance_level": "Bom" if satisfacao_media >= 70 else "Regular" if satisfacao_media >= 50 else "Crítico"
            }

            # Identifica agentes destaque e que precisam suporte
            agentes_destaque = [a.get('Agente', 'N/A') for a in agentes_com_dados
                              if (a.get('Satisfeito', 0) / max(a.get('Avaliadas', 1), 1)) * 100 >= 90]
            agentes_suporte = [a.get('Agente', 'N/A') for a in agentes_com_dados
                             if (a.get('Satisfeito', 0) / max(a.get('Avaliadas', 1), 1)) * 100 < 50]

            if agentes_destaque:
                team_analysis["insights"].append(f"🌟 Agentes destaque: {', '.join(agentes_destaque)}")

            if agentes_suporte:
                team_analysis["insights"].append(f"⚠️ Agentes que precisam suporte: {', '.join(agentes_suporte)}")
                team_analysis["suggestions"].append(f"Coaching individual para: {', '.join(agentes_suporte)}")

            if satisfacao_media < 70:
                team_analysis["suggestions"].append("Implementar programa de treinamento para toda a equipe")
                team_analysis["suggestions"].append("Revisar processos de atendimento")

            analysis_content = format_analysis_for_pdf(team_analysis, "Análise de Performance - Equipe")
            content.extend(analysis_content)

    return content


# Funções de análise automática de métricas
def analyze_distribution_metrics(data):
    """Analisa métricas de distribuição e gera insights"""
    if not data or 'data' not in data:
        return {"analysis": "Dados insuficientes para análise", "suggestions": []}

    report_data = data['data']
    analysis = {"insights": [], "suggestions": [], "performance_level": ""}

    if 'relatorio de distribuição' in report_data:
        dist_data = report_data['relatorio de distribuição']

        # Análise do resumo geral
        if 'sumario' in dist_data and 'Total de chamadas' in dist_data['sumario']:
            total_calls = dist_data['sumario']['Total de chamadas']
            connected = total_calls.get('Número de chamadas conectadas', 0)
            answered = total_calls.get('Número de chamadas atendidas', 0)
            abandon_rate = float(total_calls.get('Taxa de Abandono', '0.00'))

            # Análise da taxa de abandono
            if abandon_rate == 0:
                analysis["insights"].append("✅ Excelente: Taxa de abandono zerada - todas as chamadas foram atendidas.")
                analysis["performance_level"] = "Excelente"
            elif abandon_rate <= 3:
                analysis["insights"].append("✅ Bom: Taxa de abandono baixa, dentro do padrão aceitável.")
                analysis["performance_level"] = "Bom"
            elif abandon_rate <= 8:
                analysis["insights"].append("⚠️ Atenção: Taxa de abandono moderada, requer monitoramento.")
                analysis["performance_level"] = "Regular"
                analysis["suggestions"].append("Considere aumentar o número de agentes nos horários de pico")
            else:
                analysis["insights"].append("🚨 Crítico: Taxa de abandono alta, ação imediata necessária.")
                analysis["performance_level"] = "Crítico"
                analysis["suggestions"].append("URGENTE: Revisar dimensionamento da equipe")
                analysis["suggestions"].append("Implementar callback automático para reduzir abandono")

        # Análise por fila
        if 'chamadas_por_filas' in dist_data:
            for fila in dist_data['chamadas_por_filas']:
                fila_name = fila.get('Filas', 'Fila')
                service_level = float(fila.get('Nível de serviço', 0))
                recebidas = fila.get('Recebidas', 0)
                atendidas = fila.get('Atendidas', 0)

                if recebidas > 0:
                    if service_level >= 80:
                        analysis["insights"].append(f"✅ {fila_name}: Service Level excelente ({service_level}%)")
                    elif service_level >= 60:
                        analysis["insights"].append(f"⚠️ {fila_name}: Service Level adequado ({service_level}%)")
                        analysis["suggestions"].append(f"Otimizar atendimento na {fila_name} para atingir 80%+")
                    else:
                        analysis["insights"].append(f"🚨 {fila_name}: Service Level crítico ({service_level}%)")
                        analysis["suggestions"].append(f"PRIORIDADE: Melhorar urgentemente o atendimento na {fila_name}")

        # Análise de horários de pico
        if 'Chamadas por hora' in dist_data:
            horarios_pico = []
            for hora in dist_data['Chamadas por hora']:
                if hora.get('Recebidas', 0) > 0:
                    horarios_pico.append({
                        'hora': hora.get('Hora', ''),
                        'recebidas': hora.get('Recebidas', 0),
                        'service_level': float(hora.get('Nível de serviço', 0))
                    })

            if horarios_pico:
                # Encontra horário com mais chamadas
                pico_max = max(horarios_pico, key=lambda x: x['recebidas'])
                analysis["insights"].append(f"📈 Horário de maior volume: {pico_max['hora']} ({pico_max['recebidas']} chamadas)")

                # Encontra horários com service level baixo
                horarios_criticos = [h for h in horarios_pico if h['service_level'] < 60]
                if horarios_criticos:
                    horas_criticas = [h['hora'] for h in horarios_criticos]
                    analysis["suggestions"].append(f"Reforçar equipe nos horários: {', '.join(horas_criticas)}")

    return analysis


def analyze_satisfaction_metrics(data):
    """Analisa métricas de satisfação e gera insights"""
    if not data or 'data' not in data:
        return {"analysis": "Dados insuficientes para análise", "suggestions": []}

    report_data = data['data']
    analysis = {"insights": [], "suggestions": [], "performance_level": ""}

    if 'Pesquisa Satisfação' in report_data:
        satisfaction_data = report_data['Pesquisa Satisfação']

        # Análise do resumo geral
        if 'sumario' in satisfaction_data and 'Pesquisas Efetuadas' in satisfaction_data['sumario']:
            pesquisas = satisfaction_data['sumario']['Pesquisas Efetuadas'][0]
            total = pesquisas.get('Sem Avaliacao', 0) + pesquisas.get('Avaliadas', 0)
            avaliadas = pesquisas.get('Avaliadas', 0)
            percent_avaliadas = float(pesquisas.get('% Avaliadas', 0))

            # Análise da taxa de participação
            if percent_avaliadas >= 50:
                analysis["insights"].append(f"✅ Boa participação: {percent_avaliadas}% das pesquisas foram respondidas")
                analysis["performance_level"] = "Bom"
            elif percent_avaliadas >= 30:
                analysis["insights"].append(f"⚠️ Participação moderada: {percent_avaliadas}% de respostas")
                analysis["performance_level"] = "Regular"
                analysis["suggestions"].append("Implementar incentivos para aumentar participação nas pesquisas")
            else:
                analysis["insights"].append(f"🚨 Baixa participação: apenas {percent_avaliadas}% responderam")
                analysis["performance_level"] = "Crítico"
                analysis["suggestions"].append("URGENTE: Revisar processo de pesquisa de satisfação")
                analysis["suggestions"].append("Simplificar questionário e melhorar abordagem")

            # Análise das metas por tipo
            meta_av1 = float(pesquisas.get('% meta Av-1', 0))
            meta_av2 = float(pesquisas.get('% meta Av-2', 0))
            meta_av3 = float(pesquisas.get('% meta Av-3', 0))

            if meta_av1 >= 70:
                analysis["insights"].append(f"✅ Av-1 (Atendente): Meta atingida ({meta_av1}%)")
            else:
                analysis["insights"].append(f"⚠️ Av-1 (Atendente): Abaixo da meta ({meta_av1}%)")
                analysis["suggestions"].append("Treinamento focado em atendimento ao cliente")

            if meta_av2 >= 70:
                analysis["insights"].append(f"✅ Av-2 (Chamada): Meta atingida ({meta_av2}%)")
            else:
                analysis["insights"].append(f"⚠️ Av-2 (Chamada): Abaixo da meta ({meta_av2}%)")
                analysis["suggestions"].append("Melhorar qualidade técnica das chamadas")

            if meta_av3 >= 70:
                analysis["insights"].append(f"✅ Av-3 (Empresa): Meta atingida ({meta_av3}%)")
            else:
                analysis["insights"].append(f"⚠️ Av-3 (Empresa): Abaixo da meta ({meta_av3}%)")
                analysis["suggestions"].append("Revisar processos e políticas da empresa")

        # Análise por agente
        if 'Pesquisa por Agente' in satisfaction_data:
            agent_data = satisfaction_data['Pesquisa por Agente']
            if 'Pesquisa Amvox' in agent_data and 'Av-1-Avalia Atendente' in agent_data['Pesquisa Amvox']:
                agents = agent_data['Pesquisa Amvox']['Av-1-Avalia Atendente']

                agentes_excelentes = []
                agentes_criticos = []

                for agent in agents:
                    if isinstance(agent, dict):
                        nome = agent.get('Agente', 'N/A')
                        satisfeitos = agent.get('Satisfeito', 0)
                        total_agent = agent.get('Avaliadas', 0)

                        if total_agent > 0:
                            percent = (satisfeitos / total_agent) * 100
                            if percent >= 90:
                                agentes_excelentes.append(nome)
                            elif percent < 50:
                                agentes_criticos.append(nome)

                if agentes_excelentes:
                    analysis["insights"].append(f"🌟 Agentes destaque: {', '.join(agentes_excelentes)}")

                if agentes_criticos:
                    analysis["insights"].append(f"🚨 Agentes precisam suporte: {', '.join(agentes_criticos)}")
                    analysis["suggestions"].append(f"Coaching individual para: {', '.join(agentes_criticos)}")

    return analysis


def analyze_agent_metrics(agent_data, agent_name):
    """Analisa métricas de um agente específico"""
    if not agent_data or 'metrics' not in agent_data:
        return {"insights": ["Dados insuficientes para análise"], "suggestions": ["Aguardar mais dados"], "performance_level": "Indefinido"}

    metrics = agent_data['metrics']
    analysis = {"insights": [], "suggestions": [], "performance_level": ""}

    # Análise geral do agente
    total_avaliacoes = metrics.get('total_avaliacoes', 0)
    percentual_satisfacao = metrics.get('percentual_satisfacao', 0)

    if total_avaliacoes == 0:
        analysis["insights"].append("ℹ️ Agente sem avaliações no período analisado")
        analysis["suggestions"].append("Aguardar mais dados para análise completa")
        analysis["performance_level"] = "Sem Dados"
        return analysis

    # Classificação de performance
    if percentual_satisfacao >= 90:
        analysis["performance_level"] = "Excelente"
        analysis["insights"].append(f"🌟 Performance excelente: {percentual_satisfacao:.1f}% de satisfação")
        analysis["insights"].append("Agente modelo para a equipe")
        analysis["suggestions"].append("Considerar como mentor para outros agentes")
        analysis["suggestions"].append("Compartilhar melhores práticas com a equipe")
    elif percentual_satisfacao >= 70:
        analysis["performance_level"] = "Bom"
        analysis["insights"].append(f"✅ Boa performance: {percentual_satisfacao:.1f}% de satisfação")
        analysis["insights"].append("Performance dentro do padrão esperado")
        analysis["suggestions"].append("Manter o bom trabalho e buscar excelência")
        analysis["suggestions"].append("Participar de treinamentos avançados")
    elif percentual_satisfacao >= 50:
        analysis["performance_level"] = "Regular"
        analysis["insights"].append(f"⚠️ Performance regular: {percentual_satisfacao:.1f}% de satisfação")
        analysis["insights"].append("Necessita melhorias para atingir padrão ideal")
        analysis["suggestions"].append("Treinamento focado em pontos de melhoria")
        analysis["suggestions"].append("Acompanhamento mais próximo do supervisor")
        analysis["suggestions"].append("Definir metas específicas de melhoria")
    else:
        analysis["performance_level"] = "Crítico"
        analysis["insights"].append(f"🚨 Performance crítica: {percentual_satisfacao:.1f}% de satisfação")
        analysis["insights"].append("Requer ação imediata para melhoria")
        analysis["suggestions"].append("URGENTE: Plano de desenvolvimento individual")
        analysis["suggestions"].append("Coaching intensivo e revisão de processos")
        analysis["suggestions"].append("Acompanhamento diário até melhoria")

    # Análise por tipo de avaliação
    avaliacoes_por_tipo = metrics.get('avaliacoes_por_tipo', {})

    if 'atendente' in avaliacoes_por_tipo:
        atendente = avaliacoes_por_tipo['atendente']
        perc_atendente = atendente.get('percentual', 0)
        if perc_atendente >= 90:
            analysis["insights"].append(f"✅ Excelente avaliação como atendente: {perc_atendente:.1f}%")
        elif perc_atendente >= 70:
            analysis["insights"].append(f"👍 Boa avaliação como atendente: {perc_atendente:.1f}%")
        else:
            analysis["insights"].append(f"⚠️ Avaliação como atendente precisa melhorar: {perc_atendente:.1f}%")
            analysis["suggestions"].append("Foco em habilidades de comunicação e empatia")

    if 'chamada' in avaliacoes_por_tipo:
        chamada = avaliacoes_por_tipo['chamada']
        perc_chamada = chamada.get('percentual', 0)
        if perc_chamada >= 90:
            analysis["insights"].append(f"✅ Excelente qualidade de chamada: {perc_chamada:.1f}%")
        elif perc_chamada >= 70:
            analysis["insights"].append(f"👍 Boa qualidade de chamada: {perc_chamada:.1f}%")
        else:
            analysis["insights"].append(f"⚠️ Qualidade de chamada precisa melhorar: {perc_chamada:.1f}%")
            analysis["suggestions"].append("Melhorar conhecimento técnico e resolução de problemas")

    if 'empresa' in avaliacoes_por_tipo:
        empresa = avaliacoes_por_tipo['empresa']
        perc_empresa = empresa.get('percentual', 0)
        if perc_empresa >= 90:
            analysis["insights"].append(f"✅ Excelente representação da empresa: {perc_empresa:.1f}%")
        elif perc_empresa >= 70:
            analysis["insights"].append(f"👍 Boa representação da empresa: {perc_empresa:.1f}%")
        else:
            analysis["insights"].append(f"⚠️ Representação da empresa precisa melhorar: {perc_empresa:.1f}%")
            analysis["suggestions"].append("Alinhar com políticas e valores da empresa")

    # Análise de volume de avaliações
    if total_avaliacoes < 5:
        analysis["insights"].append("ℹ️ Baixo volume de avaliações - aguardar mais dados")
    elif total_avaliacoes >= 20:
        analysis["insights"].append("📊 Alto volume de avaliações - dados estatisticamente relevantes")
    else:
        analysis["insights"].append(f"📊 Volume moderado de avaliações: {total_avaliacoes} no período")

    return analysis


def generate_executive_summary(distribution_data, satisfaction_data, period_info):
    """Gera resumo executivo baseado em todas as métricas"""
    summary = {
        "period": period_info,
        "overall_performance": "",
        "key_metrics": {},
        "main_insights": [],
        "priority_actions": [],
        "recommendations": []
    }

    # Análise das métricas de distribuição
    dist_analysis = analyze_distribution_metrics(distribution_data) if distribution_data else None
    sat_analysis = analyze_satisfaction_metrics(satisfaction_data) if satisfaction_data else None

    # Determina performance geral
    performance_levels = []
    if dist_analysis and dist_analysis.get('performance_level'):
        performance_levels.append(dist_analysis['performance_level'])
    if sat_analysis and sat_analysis.get('performance_level'):
        performance_levels.append(sat_analysis['performance_level'])

    if 'Crítico' in performance_levels:
        summary["overall_performance"] = "Crítico"
    elif 'Regular' in performance_levels:
        summary["overall_performance"] = "Regular"
    elif 'Bom' in performance_levels:
        summary["overall_performance"] = "Bom"
    else:
        summary["overall_performance"] = "Excelente"

    # Extrai métricas principais
    if distribution_data and 'data' in distribution_data:
        dist_data = distribution_data['data']
        if 'relatorio de distribuição' in dist_data:
            sumario = dist_data['relatorio de distribuição'].get('sumario', {})
            if 'Total de chamadas' in sumario:
                total_calls = sumario['Total de chamadas']
                summary["key_metrics"]["total_chamadas"] = total_calls.get('Número de chamadas conectadas', 0)
                summary["key_metrics"]["taxa_abandono"] = total_calls.get('Taxa de Abandono', '0.00')

            if 'chamadas_por_filas' in dist_data['relatorio de distribuição']:
                filas = dist_data['relatorio de distribuição']['chamadas_por_filas']
                service_levels = [float(f.get('Nível de serviço', 0)) for f in filas if f.get('Recebidas', 0) > 0]
                if service_levels:
                    summary["key_metrics"]["service_level_medio"] = round(sum(service_levels) / len(service_levels), 1)

    if satisfaction_data and 'data' in satisfaction_data:
        sat_data = satisfaction_data['data']
        if 'Pesquisa Satisfação' in sat_data:
            sumario = sat_data['Pesquisa Satisfação'].get('sumario', {})
            if 'Pesquisas Efetuadas' in sumario:
                pesquisas = sumario['Pesquisas Efetuadas'][0]
                summary["key_metrics"]["taxa_participacao"] = pesquisas.get('% Avaliadas', 0)
                summary["key_metrics"]["satisfacao_atendente"] = pesquisas.get('% meta Av-1', 0)
                summary["key_metrics"]["satisfacao_chamada"] = pesquisas.get('% meta Av-2', 0)
                summary["key_metrics"]["satisfacao_empresa"] = pesquisas.get('% meta Av-3', 0)

    # Consolida insights principais
    if dist_analysis:
        summary["main_insights"].extend(dist_analysis.get('insights', []))
        summary["recommendations"].extend(dist_analysis.get('suggestions', []))

    if sat_analysis:
        summary["main_insights"].extend(sat_analysis.get('insights', []))
        summary["recommendations"].extend(sat_analysis.get('suggestions', []))

    # Gera ações prioritárias baseadas na performance
    if summary["overall_performance"] == "Crítico":
        summary["priority_actions"] = [
            "Reunião de emergência com gestores",
            "Análise detalhada dos processos críticos",
            "Implementação de plano de ação imediato"
        ]
    elif summary["overall_performance"] == "Regular":
        summary["priority_actions"] = [
            "Revisar dimensionamento da equipe",
            "Implementar treinamentos específicos",
            "Monitoramento semanal das métricas"
        ]
    else:
        summary["priority_actions"] = [
            "Manter padrão de qualidade atual",
            "Identificar melhores práticas para replicar",
            "Monitoramento mensal das métricas"
        ]

    return summary


def format_analysis_for_pdf(analysis, title):
    """Formata análise para inclusão em PDF"""
    content = []
    styles = getSampleStyleSheet()

    # Título da análise
    content.append(Paragraph(title, styles['Heading3']))
    content.append(Spacer(1, 12))

    # Performance Level
    if 'performance_level' in analysis and analysis['performance_level']:
        level = analysis['performance_level']
        color_map = {
            'Excelente': colors.HexColor('#2ecc71'),
            'Bom': colors.HexColor('#3498db'),
            'Regular': colors.HexColor('#f39c12'),
            'Crítico': colors.HexColor('#e74c3c')
        }

        level_style = ParagraphStyle(
            'PerformanceLevel',
            parent=styles['Normal'],
            fontSize=14,
            textColor=color_map.get(level, colors.black),
            fontName='Helvetica-Bold'
        )
        content.append(Paragraph(f"Nível de Performance: {level}", level_style))
        content.append(Spacer(1, 12))

    # Insights
    if 'insights' in analysis and analysis['insights']:
        content.append(Paragraph("Principais Insights:", styles['Heading4']))
        for insight in analysis['insights']:
            content.append(Paragraph(f"• {insight}", styles['Normal']))
        content.append(Spacer(1, 12))

    # Sugestões
    if 'suggestions' in analysis and analysis['suggestions']:
        content.append(Paragraph("Recomendações:", styles['Heading4']))
        for suggestion in analysis['suggestions']:
            content.append(Paragraph(f"• {suggestion}", styles['Normal']))
        content.append(Spacer(1, 12))

    return content


@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """Página principal do dashboard"""
    return HTMLResponse(content="""
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Amvox Analytics - Sistema de Relatórios 3CX</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
            :root {
                /* Light Theme */
                --bg-primary: #ffffff;
                --bg-secondary: #f8fafc;
                --bg-tertiary: #f1f5f9;
                --text-primary: #1e293b;
                --text-secondary: #64748b;
                --text-muted: #94a3b8;
                --border-color: #e2e8f0;
                --accent-primary: #1d4ed8;
                --accent-secondary: #3b82f6;
                --success: #10b981;
                --warning: #f59e0b;
                --danger: #ef4444;
                --info: #06b6d4;
                --sidebar-bg: #1e293b;
                --sidebar-text: #cbd5e1;
                --sidebar-active: #3b82f6;
                --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
                --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
                --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            }

            [data-theme="dark"] {
                /* Dark Theme */
                --bg-primary: #0f172a;
                --bg-secondary: #1e293b;
                --bg-tertiary: #334155;
                --text-primary: #f8fafc;
                --text-secondary: #cbd5e1;
                --text-muted: #94a3b8;
                --border-color: #334155;
                --accent-primary: #3b82f6;
                --accent-secondary: #60a5fa;
                --success: #22c55e;
                --warning: #fbbf24;
                --danger: #f87171;
                --info: #22d3ee;
                --sidebar-bg: #020617;
                --sidebar-text: #94a3b8;
                --sidebar-active: #3b82f6;
                --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
                --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
                --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                background-color: var(--bg-secondary);
                color: var(--text-primary);
                transition: all 0.3s ease;
                overflow-x: hidden;
            }

            /* Header Navigation */
            .main-header {
                background: var(--bg-primary);
                border-bottom: 1px solid var(--border-color);
                box-shadow: var(--shadow-sm);
                position: sticky;
                top: 0;
                z-index: 1000;
            }

            .header-container {
                max-width: 1400px;
                margin: 0 auto;
                padding: 0 2rem;
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 70px;
            }

            .header-nav {
                flex: 1;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .nav-menu {
                display: flex;
                align-items: center;
                gap: 1.5rem;
                list-style: none;
                margin: 0;
                padding: 0;
            }

            .nav-menu-item {
                position: relative;
            }

            .nav-menu-link {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.75rem 1rem;
                color: var(--text-secondary);
                text-decoration: none;
                border-radius: 8px;
                font-weight: 500;
                transition: all 0.2s ease;
            }

            .nav-menu-link:hover {
                background: var(--bg-tertiary);
                color: var(--text-primary);
            }

            .nav-menu-link.active {
                background: var(--accent-primary);
                color: white;
            }

            .header-actions {
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            /* Main Content */
            .main-content {
                min-height: calc(100vh - 70px);
            }

            .theme-toggle {
                background: var(--bg-tertiary);
                border: none;
                color: var(--text-secondary);
                width: 40px;
                height: 40px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .theme-toggle:hover {
                background: var(--accent-primary);
                color: white;
            }

            .current-time {
                color: var(--text-secondary);
                font-size: 0.875rem;
                font-weight: 500;
                padding: 0.5rem 1rem;
                background: var(--bg-tertiary);
                border-radius: 8px;
            }

            /* Content Area */
            .content-area {
                padding: 2rem;
            }

            /* Cards */
            .card {
                background: var(--bg-primary);
                border: 1px solid var(--border-color);
                border-radius: 12px;
                box-shadow: var(--shadow-sm);
                transition: all 0.3s ease;
                margin-bottom: 1.5rem;
            }

            .card:hover {
                box-shadow: var(--shadow-md);
                transform: translateY(-2px);
            }

            .card-header {
                background: transparent;
                border-bottom: 1px solid var(--border-color);
                padding: 1.5rem;
                font-weight: 600;
                color: var(--text-primary);
                border-radius: 12px 12px 0 0 !important;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .card-body {
                padding: 1.5rem;
            }

            /* Metric Cards */
            .metric-card {
                background: var(--bg-primary);
                border: 1px solid var(--border-color);
                border-radius: 12px;
                padding: 1.5rem;
                text-align: center;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .metric-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
            }

            .metric-card:hover {
                transform: translateY(-4px);
                box-shadow: var(--shadow-lg);
            }

            .metric-value {
                font-size: 2.5rem;
                font-weight: 700;
                color: var(--accent-primary);
                margin-bottom: 0.5rem;
                line-height: 1;
            }

            .metric-label {
                color: var(--text-secondary);
                font-size: 0.875rem;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                margin-bottom: 0.25rem;
            }

            .metric-subtitle {
                color: var(--text-muted);
                font-size: 0.75rem;
            }

            /* Status Indicators */
            .status-indicator {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.375rem 0.75rem;
                border-radius: 20px;
                font-size: 0.875rem;
                font-weight: 500;
            }

            .status-success {
                background: rgba(16, 185, 129, 0.1);
                color: var(--success);
            }

            .status-warning {
                background: rgba(245, 158, 11, 0.1);
                color: var(--warning);
            }

            .status-danger {
                background: rgba(239, 68, 68, 0.1);
                color: var(--danger);
            }

            .status-info {
                background: rgba(6, 182, 212, 0.1);
                color: var(--info);
            }

            /* Tables */
            .table {
                color: var(--text-primary);
                margin-bottom: 0;
            }

            .table th {
                background: var(--bg-tertiary);
                color: var(--text-primary);
                font-weight: 600;
                border-bottom: 1px solid var(--border-color);
                padding: 1rem 0.75rem;
            }

            .table td {
                border-bottom: 1px solid var(--border-color);
                padding: 0.75rem;
                vertical-align: middle;
            }

            .table-responsive {
                border-radius: 8px;
                border: 1px solid var(--border-color);
                max-height: 400px;
                overflow-y: auto;
            }

            /* Buttons */
            .btn {
                border-radius: 8px;
                font-weight: 500;
                padding: 0.625rem 1.25rem;
                transition: all 0.2s ease;
                border: none;
            }

            .btn-primary {
                background: var(--accent-primary);
                color: white;
            }

            .btn-primary:hover {
                background: var(--accent-secondary);
                transform: translateY(-1px);
                box-shadow: var(--shadow-md);
            }

            .btn-outline-primary {
                border: 1px solid var(--accent-primary);
                color: var(--accent-primary);
                background: transparent;
            }

            .btn-outline-primary:hover {
                background: var(--accent-primary);
                color: white;
            }

            /* Loading States */
            .loading {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 2rem;
                color: var(--text-muted);
                gap: 0.5rem;
            }

            .loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(4px);
                z-index: 9999;
                display: none;
                align-items: center;
                justify-content: center;
            }

            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 3px solid var(--border-color);
                border-top: 3px solid var(--accent-primary);
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            /* Development Page Styles */
            .development-page {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                min-height: 60vh;
                text-align: center;
                padding: 3rem 2rem;
            }

            .development-icon {
                font-size: 4rem;
                color: var(--accent-primary);
                margin-bottom: 2rem;
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% { transform: scale(1); opacity: 1; }
                50% { transform: scale(1.1); opacity: 0.7; }
                100% { transform: scale(1); opacity: 1; }
            }

            .development-title {
                font-size: 2.5rem;
                font-weight: 700;
                color: var(--text-primary);
                margin-bottom: 1rem;
            }

            .development-subtitle {
                font-size: 1.25rem;
                color: var(--text-secondary);
                margin-bottom: 2rem;
                max-width: 600px;
            }

            .development-features {
                background: var(--bg-primary);
                border: 1px solid var(--border-color);
                border-radius: 12px;
                padding: 2rem;
                max-width: 500px;
                width: 100%;
                margin-top: 2rem;
            }

            .development-features h4 {
                color: var(--text-primary);
                margin-bottom: 1rem;
                font-size: 1.1rem;
            }

            .development-features ul {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .development-features li {
                padding: 0.5rem 0;
                color: var(--text-secondary);
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .development-features li i {
                color: var(--success);
                width: 16px;
            }

            /* Simplified Development Page Styles */
            .development-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                min-height: 60vh;
                text-align: center;
                padding: 3rem 2rem;
            }

            .development-message {
                background: var(--bg-primary);
                border: 1px solid var(--border-color);
                border-radius: 12px;
                padding: 2rem;
                margin: 2rem 0;
                max-width: 400px;
                width: 100%;
            }

            .development-message i {
                font-size: 2rem;
                color: var(--accent-primary);
                margin-bottom: 1rem;
                display: block;
            }

            .development-message p {
                font-size: 1.1rem;
                color: var(--text-primary);
                margin-bottom: 0.5rem;
                font-weight: 500;
            }

            .development-message small {
                font-size: 0.9rem;
                color: var(--text-secondary);
            }

            /* Charts */
            .chart-container {
                height: 300px;
                position: relative;
            }

            /* Agent Cards */
            .agent-card {
                background: var(--bg-primary);
                border: 1px solid var(--border-color);
                border-radius: 12px;
                padding: 1.25rem;
                transition: all 0.3s ease;
                cursor: pointer;
                position: relative;
                overflow: hidden;
            }

            .agent-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 4px;
                height: 100%;
                background: var(--accent-primary);
            }

            .agent-card:hover {
                transform: translateY(-2px);
                box-shadow: var(--shadow-md);
            }

            .agent-name {
                font-weight: 600;
                color: var(--text-primary);
                margin-bottom: 0.75rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .agent-stats {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
                margin-bottom: 1rem;
            }

            .agent-stat {
                text-align: center;
            }

            .agent-stat-value {
                font-size: 1.5rem;
                font-weight: 600;
                color: var(--accent-primary);
                line-height: 1;
            }

            .agent-stat-label {
                font-size: 0.75rem;
                color: var(--text-muted);
                text-transform: uppercase;
                letter-spacing: 0.5px;
                margin-top: 0.25rem;
            }

            /* Progress Bars */
            .progress {
                height: 8px;
                background: var(--bg-tertiary);
                border-radius: 4px;
                overflow: hidden;
            }

            .progress-bar {
                background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
                transition: width 0.6s ease;
            }

            /* Custom Progress Bars for Distribution Table */
            .progress-custom {
                height: 25px !important;
                background: var(--bg-tertiary);
                border-radius: 6px;
                overflow: hidden;
                box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
            }

            .progress-custom .progress-bar {
                font-size: 13px;
                font-weight: 600;
                line-height: 25px;
                text-align: center;
                color: white;
                text-shadow: 0 1px 2px rgba(0,0,0,0.3);
                transition: width 0.8s ease;
            }

            /* Badge styles for time information */
            .badge {
                font-weight: 500;
                letter-spacing: 0.3px;
            }

            .badge .fas {
                font-size: 0.8em;
            }

            /* Satisfaction Table Styles */
            .satisfaction-icon {
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                background: var(--bg-tertiary);
            }

            #satisfactionTable .badge {
                font-size: 0.85rem;
                padding: 0.5rem 0.75rem;
                min-width: 45px;
            }

            #satisfactionTable .progress {
                margin: 0.25rem 0;
                background: var(--bg-tertiary);
            }

            #satisfactionTable .progress-bar {
                font-weight: 600;
                text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            }

            #satisfactionTable td {
                vertical-align: middle;
                padding: 1.25rem 1rem;
            }

            #satisfactionTable th {
                padding: 1rem;
                font-size: 0.95rem;
                font-weight: 600;
            }

            #satisfactionTable .text-muted {
                font-size: 0.8rem;
            }

            #satisfactionTable .satisfaction-icon {
                width: 32px;
                height: 32px;
                font-size: 1.1rem;
            }

            #satisfactionTable .badge.fs-6 {
                font-size: 1rem !important;
                padding: 0.6rem 1rem;
                min-width: 55px;
            }

            #satisfactionTable .progress {
                height: 24px !important;
                margin: 0.5rem 0;
            }

            #satisfactionTable .progress-bar {
                font-size: 0.9rem;
                line-height: 24px;
            }

            /* Satisfaction table centered content */
            #satisfactionTable .d-flex.justify-content-center {
                text-align: center;
            }

            #satisfactionTable .satisfaction-icon {
                flex-shrink: 0;
            }

            /* Enhanced Agent Progress Bars */
            .agent-progress-bar {
                height: 24px !important;
                background-color: #e9ecef;
                border-radius: 12px;
                overflow: hidden;
                position: relative;
            }

            .agent-progress-bar .progress-bar {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                transition: width 0.8s ease-in-out;
                position: relative;
            }

            .progress-percentage {
                color: white;
                font-size: 0.85rem;
                font-weight: 600;
                text-shadow: 0 1px 2px rgba(0,0,0,0.3);
                position: absolute;
                z-index: 1;
            }

            /* Enhanced Agent Cards - Alternative Layout */
            .agent-card-enhanced {
                background: var(--bg-primary);
                border: 1px solid var(--border-color);
                border-radius: 12px;
                transition: all 0.3s ease;
                cursor: pointer;
                position: relative;
                overflow: hidden;
            }

            .agent-card-enhanced::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 4px;
                height: 100%;
                background: var(--accent-primary);
            }

            .agent-card-enhanced:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                border-color: var(--accent-primary);
            }

            .satisfaction-section {
                background: var(--bg-secondary);
                padding: 1rem;
                border-radius: 8px;
                margin: 0.5rem 0;
            }

            .progress-enhanced-agent {
                height: 28px !important;
                background-color: rgba(0,0,0,0.1);
                border-radius: 14px;
                overflow: hidden;
                position: relative;
                margin-bottom: 0.5rem;
            }

            .progress-enhanced-agent .progress-bar {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                transition: width 1s ease-in-out;
                position: relative;
                border-radius: 14px;
            }

            .progress-text-agent {
                color: white;
                font-size: 0.9rem;
                font-weight: 700;
                text-shadow: 0 1px 3px rgba(0,0,0,0.4);
                position: absolute;
                z-index: 2;
            }

            .progress-labels-agent {
                display: flex;
                justify-content: space-between;
                font-size: 0.7rem;
            }

            .agent-card-enhanced .badge {
                font-size: 0.8rem;
                padding: 0.4rem 0.8rem;
            }

            .explanation-box {
                background: rgba(23, 162, 184, 0.1);
                border: 1px solid rgba(23, 162, 184, 0.2);
                border-radius: 6px;
                padding: 0.5rem;
                margin-bottom: 0.5rem;
            }

            .explanation-box small {
                font-size: 0.75rem;
                line-height: 1.3;
            }

            /* Enhanced Modal Styles */
            .metric-card-enhanced {
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                padding: 1rem;
                height: 100%;
                display: flex;
                align-items: center;
                transition: all 0.3s ease;
            }

            .metric-card-enhanced:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            }

            .metric-icon {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 1rem;
                color: white;
                font-size: 1.2rem;
            }

            .metric-content {
                flex: 1;
            }

            .metric-value {
                font-size: 1.8rem;
                font-weight: 700;
                margin: 0;
                color: #2c3e50;
            }

            .metric-label {
                font-size: 0.9rem;
                font-weight: 600;
                margin: 0;
                color: #495057;
            }

            .performance-circle {
                width: 120px;
                height: 120px;
                border-radius: 50%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                margin: 0 auto;
                position: relative;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            .performance-circle.excellent {
                background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            }

            .performance-circle.good {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            .performance-circle.average {
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            }

            .performance-circle.poor {
                background: linear-gradient(135deg, #fc4a1a 0%, #f7b733 100%);
            }

            .performance-percentage {
                font-size: 1.8rem;
                font-weight: 700;
                color: white;
                text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }

            .performance-label {
                font-size: 0.8rem;
                color: rgba(255,255,255,0.9);
                text-transform: uppercase;
                letter-spacing: 1px;
            }

            .evaluation-type-card {
                border: 1px solid #e9ecef;
                border-radius: 12px;
                overflow: hidden;
                height: 100%;
                transition: all 0.3s ease;
            }

            .evaluation-type-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            }

            .card-header-custom {
                padding: 1rem;
                border-bottom: none;
            }

            .card-body-custom {
                padding: 1rem;
                background: white;
            }

            .metric-row-small {
                display: flex;
                justify-content: space-between;
                gap: 0.5rem;
            }

            .metric-item-small {
                text-align: center;
                flex: 1;
                padding: 0.5rem;
                background: #f8f9fa;
                border-radius: 6px;
            }

            .metric-label-small {
                display: block;
                font-size: 0.7rem;
                color: #6c757d;
                margin-bottom: 0.25rem;
            }

            .metric-value-small {
                display: block;
                font-size: 1.1rem;
                font-weight: 600;
                color: #2c3e50;
            }

            .progress-enhanced-modal {
                height: 20px;
                border-radius: 10px;
                overflow: hidden;
            }

            .progress-text-modal {
                color: white;
                font-size: 0.75rem;
                font-weight: 600;
                text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            }

            .bg-gradient-primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            .bg-gradient-secondary {
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            }

            .bg-gradient-dark {
                background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            }

            .classification-badge {
                display: flex;
                align-items: center;
                padding: 1rem;
                border-radius: 12px;
                background: white;
                border: 2px solid #e9ecef;
            }

            .classification-badge.exceptional {
                border-color: #ffd700;
                background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
            }

            .classification-badge.excellent {
                border-color: #28a745;
                background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            }

            .classification-badge.very-good {
                border-color: #17a2b8;
                background: linear-gradient(135deg, #e6f3f7 0%, #d1ecf1 100%);
            }

            .classification-badge.good {
                border-color: #ffc107;
                background: linear-gradient(135deg, #fff8e1 0%, #fff3cd 100%);
            }

            .classification-badge.needs-improvement {
                border-color: #dc3545;
                background: linear-gradient(135deg, #fdf2f2 0%, #f8d7da 100%);
            }

            .classification-icon {
                font-size: 2rem;
                margin-right: 1rem;
                color: #495057;
            }

            .classification-text h4 {
                margin: 0;
                color: #2c3e50;
                font-weight: 700;
            }

            .classification-text p {
                margin: 0;
                color: #6c757d;
                font-size: 0.9rem;
            }

            .highlight-item {
                display: flex;
                align-items: center;
                padding: 0.5rem;
                margin-bottom: 0.5rem;
                border-radius: 6px;
                font-size: 0.9rem;
            }

            .highlight-item.positive {
                background: #d4edda;
                color: #155724;
            }

            .highlight-item.neutral {
                background: #d1ecf1;
                color: #0c5460;
            }

            .highlight-item.warning {
                background: #fff3cd;
                color: #856404;
            }

            .insight-item {
                display: flex;
                align-items: flex-start;
                padding: 1rem;
                margin-bottom: 1rem;
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                transition: all 0.3s ease;
            }

            .insight-item:hover {
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }

            .insight-icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 1rem;
                color: white;
                flex-shrink: 0;
            }

            .insight-icon.success {
                background: #28a745;
            }

            .insight-icon.warning {
                background: #ffc107;
            }

            .insight-icon.info {
                background: #17a2b8;
            }

            .insight-content strong {
                color: #2c3e50;
                font-size: 0.95rem;
            }

            .insight-content p {
                color: #6c757d;
                font-size: 0.85rem;
                margin-top: 0.25rem;
            }

            .recommendation-item {
                display: flex;
                align-items: flex-start;
                padding: 1rem;
                margin-bottom: 1rem;
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                transition: all 0.3s ease;
            }

            .recommendation-item:hover {
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }

            .recommendation-priority {
                padding: 0.25rem 0.5rem;
                border-radius: 12px;
                font-size: 0.7rem;
                font-weight: 600;
                text-transform: uppercase;
                margin-right: 1rem;
                flex-shrink: 0;
                margin-top: 0.25rem;
            }

            .recommendation-priority.high {
                background: #dc3545;
                color: white;
            }

            .recommendation-priority.medium {
                background: #ffc107;
                color: #212529;
            }

            .recommendation-priority.low {
                background: #6c757d;
                color: white;
            }

            .recommendation-content strong {
                color: #2c3e50;
                font-size: 0.95rem;
            }

            .recommendation-content p {
                color: #6c757d;
                font-size: 0.85rem;
                margin-top: 0.25rem;
            }

            /* Responsive Design */
            @media (max-width: 992px) {
                .header-container {
                    padding: 0 1rem;
                }

                .nav-menu {
                    gap: 1rem;
                }

                .content-area {
                    padding: 1rem;
                }

                .metric-value {
                    font-size: 2rem;
                }
            }

            @media (max-width: 1024px) {
                .nav-menu {
                    gap: 0.75rem;
                }

                .nav-menu-link {
                    padding: 0.5rem 0.75rem;
                    font-size: 0.9rem;
                }
            }

            @media (max-width: 768px) {
                .header-container {
                    height: 60px;
                    justify-content: center;
                }

                .nav-menu {
                    display: none;
                }

                .header-actions {
                    position: absolute;
                    right: 1rem;
                }

                .chart-container {
                    height: 250px;
                }

                .agent-stats {
                    grid-template-columns: 1fr;
                    gap: 0.5rem;
                }

                .metric-card {
                    padding: 1rem;
                }

                .metric-value {
                    font-size: 1.75rem;
                }

                .development-title {
                    font-size: 2rem;
                }

                .development-subtitle {
                    font-size: 1.1rem;
                }

                .development-icon {
                    font-size: 3rem;
                }
            }

            @media (max-width: 576px) {
                .header-container {
                    padding: 0 0.75rem;
                }

                .content-area {
                    padding: 0.75rem;
                }

                .card-header,
                .card-body {
                    padding: 1rem;
                }

                .chart-container {
                    height: 200px;
                }

                .metric-value {
                    font-size: 1.5rem;
                }

                .current-time {
                    display: none;
                }
            }
        </style>
    </head>
    <body>
        <!-- Main Header -->
        <header class="main-header">
            <div class="header-container">
                <nav class="header-nav">
                    <ul class="nav-menu">
                        <li class="nav-menu-item">
                            <a href="#" class="nav-menu-link active" data-section="telefonia">
                                <i class="fas fa-phone"></i>
                                <span>Telefonia</span>
                            </a>
                        </li>
                        <li class="nav-menu-item">
                            <a href="#" class="nav-menu-link" data-section="whatsapp">
                                <i class="fab fa-whatsapp"></i>
                                <span>WhatsApp</span>
                            </a>
                        </li>
                        <li class="nav-menu-item">
                            <a href="#" class="nav-menu-link" data-section="consolidado">
                                <i class="fas fa-chart-pie"></i>
                                <span>Consolidado</span>
                            </a>
                        </li>
                    </ul>
                </nav>

                <div class="header-actions">
                    <button class="theme-toggle" id="themeToggle" title="Alternar tema">
                        <i class="fas fa-moon"></i>
                    </button>
                    <div class="current-time" id="current-time"></div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-content" id="mainContent">

            <!-- Content Area -->
            <div class="content-area">
                <!-- Period Controls -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-calendar-alt"></i>
                        Período de Análise
                    </div>
                    <div class="card-body">
                        <div class="row align-items-end g-3">
                            <div class="col-md-4">
                                <label for="startDate" class="form-label">Data Inicial</label>
                                <input type="date" class="form-control" id="startDate">
                            </div>
                            <div class="col-md-4">
                                <label for="endDate" class="form-label">Data Final</label>
                                <input type="date" class="form-control" id="endDate">
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-primary w-100" onclick="loadAllData()">
                                    <i class="fas fa-sync-alt me-2"></i>
                                    Atualizar Dados
                                </button>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Os dados são automaticamente coletados de todas as filas (802 | RECEPTIVO AMVOX e 803 | ATIVO AMVOX)
                                </small>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary" onclick="setQuickPeriod('today')">Hoje</button>
                                    <button type="button" class="btn btn-outline-primary" onclick="setQuickPeriod('yesterday')">Ontem</button>
                                    <button type="button" class="btn btn-outline-primary" onclick="setQuickPeriod('week')">Esta Semana</button>
                                    <button type="button" class="btn btn-outline-primary" onclick="setQuickPeriod('month')">Este Mês</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Metrics -->
                <div class="row g-3 mb-4">
                    <div class="col-6 col-lg-3">
                        <div class="metric-card">
                            <div class="metric-value" id="total-calls">-</div>
                            <div class="metric-label">Total de Chamadas</div>
                            <div class="metric-subtitle" id="total-calls-period">Período atual</div>
                        </div>
                    </div>
                    <div class="col-6 col-lg-3">
                        <div class="metric-card">
                            <div class="metric-value" id="service-level" style="color: var(--success)">-</div>
                            <div class="metric-label">Service Level</div>
                            <div class="metric-subtitle" id="service-level-period">Período atual</div>
                        </div>
                    </div>
                    <div class="col-6 col-lg-3">
                        <div class="metric-card">
                            <div class="metric-value" id="avg-wait-time" style="color: var(--warning)">-</div>
                            <div class="metric-label">Tempo Médio Espera</div>
                            <div class="metric-subtitle" id="avg-wait-period">Período atual</div>
                        </div>
                    </div>
                    <div class="col-6 col-lg-3">
                        <div class="metric-card">
                            <div class="metric-value" id="satisfaction-score" style="color: var(--info)">-</div>
                            <div class="metric-label">Taxa Satisfação</div>
                            <div class="metric-subtitle" id="satisfaction-period">Período atual</div>
                        </div>
                    </div>
                </div>

                <!-- Additional Metrics Row -->
                <div class="row g-3 mb-4">
                    <div class="col-6 col-lg-3">
                        <div class="metric-card">
                            <div class="metric-value" id="answered-calls" style="color: var(--success)">-</div>
                            <div class="metric-label">Chamadas Atendidas</div>
                            <div class="metric-subtitle">Total do período</div>
                        </div>
                    </div>
                    <div class="col-6 col-lg-3">
                        <div class="metric-card">
                            <div class="metric-value" id="abandonment-rate" style="color: var(--danger)">-</div>
                            <div class="metric-label">Taxa de Abandono</div>
                            <div class="metric-subtitle">Percentual</div>
                        </div>
                    </div>
                    <div class="col-6 col-lg-3">
                        <div class="metric-card">
                            <div class="metric-value" id="avg-handle-time" style="color: var(--primary)">-</div>
                            <div class="metric-label">Tempo Médio Atendimento</div>
                            <div class="metric-subtitle">AHT</div>
                        </div>
                    </div>
                    <div class="col-6 col-lg-3">
                        <div class="metric-card">
                            <div class="metric-value" id="active-queues" style="color: var(--accent-primary)">-</div>
                            <div class="metric-label">Filas Ativas</div>
                            <div class="metric-subtitle">Com chamadas</div>
                        </div>
                    </div>
                </div>

                <!-- Telefonia Specific Content -->
                <div id="telefonia-content">
                    <!-- Charts and Tables Section -->
                    <div class="row g-3">
                        <!-- Satisfaction Chart -->
                        <div class="col-lg-12">
                            <div class="card">
                                <div class="card-header">
                                    <i class="fas fa-chart-bar"></i>
                                    Pesquisa de Satisfação
                                </div>
                                <div class="card-body">
                                    <div class="chart-container">
                                        <canvas id="satisfactionChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Distribution Table -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-table"></i>
                        Detalhes da Distribuição
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table" id="distributionTable">
                                <thead>
                                    <tr>
                                        <th>Fila</th>
                                        <th>Recebidas</th>
                                        <th>Atendidas</th>
                                        <th>Não Atendidas</th>
                                        <th>Service Level</th>
                                        <th>Taxa Atendidas</th>
                                        <th>Duração Média</th>
                                        <th>Espera Média</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="8" class="text-center">
                                            <div class="loading">
                                                <div class="loading-spinner"></div>
                                                <span>Carregando dados...</span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                                    <!-- Versão mobile -->
                                    <div class="d-md-none">
                                        <div class="row">
                                            <div class="col-6">
                                                <button type="button" class="btn btn-outline-danger w-100 btn-sm" onclick="downloadPDF('distribution')">
                                                    <i class="fas fa-file-pdf"></i><br>
                                                    <small>Distribuição</small>
                                                </button>
                                            </div>
                                            <div class="col-6">
                                                <button type="button" class="btn btn-outline-danger w-100 btn-sm" onclick="downloadPDF('satisfaction')">
                                                    <i class="fas fa-file-pdf"></i><br>
                                                    <small>Satisfação</small>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>





            <!-- Pesquisa de Satisfação Detalhada -->
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-smile me-2"></i>
                    Pesquisa de Satisfação - Análise Detalhada
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <div class="table-responsive">
                                <table class="table table-hover" id="satisfactionTable">
                                    <thead class="table-dark">
                                        <tr>
                                            <th style="width: 25%;" class="text-center">
                                                <i class="fas fa-star me-2"></i>Tipo de Avaliação
                                            </th>
                                            <th style="width: 15%;" class="text-center">
                                                <i class="fas fa-chart-bar me-1"></i>Total
                                            </th>
                                            <th style="width: 15%;" class="text-center">
                                                <i class="fas fa-thumbs-up me-1"></i>Positivas
                                            </th>
                                            <th style="width: 15%;" class="text-center">
                                                <i class="fas fa-thumbs-down me-1"></i>Negativas
                                            </th>
                                            <th style="width: 15%;" class="text-center">
                                                <i class="fas fa-percentage me-1"></i>Taxa
                                            </th>
                                            <th style="width: 15%;" class="text-center">
                                                <i class="fas fa-trophy me-1"></i>Score
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="6" class="text-center text-muted">
                                                <div class="loading">
                                                    <i class="fas fa-spinner fa-spin"></i> Carregando dados...
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

                <!-- Hourly Distribution Chart -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-clock"></i>
                        Distribuição de Chamadas por Hora
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="hourlyChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Agents Performance -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-users"></i>
                        Performance Individual dos Agentes
                    </div>
                    <div class="card-body">
                        <div id="agents-grid" class="row g-3">
                            <div class="col-12 text-center">
                                <div class="loading">
                                    <div class="loading-spinner"></div>
                                    <span>Carregando dados dos agentes...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Análises Automáticas com IA -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="fas fa-brain me-2"></i>
                        Análises Automáticas com IA
                        <button class="btn btn-sm btn-outline-primary float-end" onclick="refreshAnalysis()">
                            <i class="fas fa-sync-alt me-1"></i>
                            Atualizar
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="analysis-content">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                                <p>Carregando análises automáticas...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports Section -->
                <div class="row g-3">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-file-pdf"></i>
                                Relatórios PDF
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary" onclick="downloadPDF('distribution')">
                                        <i class="fas fa-download me-2"></i>
                                        Relatório de Distribuição
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="downloadPDF('satisfaction')">
                                        <i class="fas fa-download me-2"></i>
                                        Relatório de Satisfação
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="downloadPDF('agents')">
                                        <i class="fas fa-download me-2"></i>
                                        Relatório de Agentes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="text-center">
                <div class="loading-spinner mb-3"></div>
                <h5 style="color: var(--text-primary);">Carregando dados...</h5>
                <p style="color: var(--text-secondary);">Aguarde enquanto processamos as informações</p>
            </div>
        </div>

            <!-- Detalhes das Chamadas -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-list me-2"></i>
                            Detalhes das Chamadas Recentes
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="callDetailsTable">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Data/Hora</th>
                                            <th>Agente</th>
                                            <th>Número</th>
                                            <th>Evento</th>
                                            <th>Tempo Espera</th>
                                            <th>Duração</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td colspan="6" class="text-center text-muted">
                                                <div class="loading">
                                                    <i class="fas fa-spinner fa-spin"></i> Carregando detalhes...
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
                </div>
                <!-- End of Telefonia Content -->

            <!-- Status do Sistema -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-info-circle me-2"></i>
                            Status do Sistema
                        </div>
                        <div class="card-body">
                            <div id="system-status">
                                <div class="alert alert-info">
                                    <i class="fas fa-spinner fa-spin me-2"></i>
                                    Verificando status do sistema...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="text-center">
                <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">Carregando...</span>
                </div>
                <div class="mt-3">
                    <h5>Carregando dados...</h5>
                    <p class="text-muted">Aguarde um momento</p>
                </div>
            </div>
        </div>

        <!-- Modal para Detalhes do Agente -->
        <div class="modal fade" id="agentModal" tabindex="-1" aria-labelledby="agentModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-scrollable">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="agentModalLabel">
                            <i class="fas fa-user me-2"></i>
                            Detalhes do Agente
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div id="agentModalContent">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin fa-2x"></i>
                                <p class="mt-2">Carregando dados do agente...</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-danger" onclick="downloadAgentPDF()">
                            <i class="fas fa-file-pdf me-2"></i>
                            Baixar PDF
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                    </div>
                </div>
            </div>
        </div>

        </div> <!-- Fechamento da content-area -->
        </div> <!-- Fechamento da main-content -->

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            // Theme Management
            const themeToggle = document.getElementById('themeToggle');

            // Initialize theme
            const currentTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', currentTheme);
            updateThemeIcon(currentTheme);

            // Theme toggle functionality
            themeToggle.addEventListener('click', () => {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                updateThemeIcon(newTheme);
            });

            function updateThemeIcon(theme) {
                const icon = themeToggle.querySelector('i');
                icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }

            // Navigation functionality - aguarda DOM carregar
            document.addEventListener('DOMContentLoaded', function() {
                console.log('🚀 DOM carregado, configurando navegação...');

                const navLinks = document.querySelectorAll('.nav-menu-link');
                console.log(`📍 Encontrados ${navLinks.length} links de navegação`);

                navLinks.forEach((link, index) => {
                    console.log(`📍 Link ${index}: ${link.dataset.section}`);

                    link.addEventListener('click', (e) => {
                        e.preventDefault();

                        console.log('🖱️ Clique no menu:', link.dataset.section);

                        // Remove active class from all links
                        navLinks.forEach(l => l.classList.remove('active'));

                        // Add active class to clicked link
                        link.classList.add('active');

                        // Handle section navigation
                        const section = link.dataset.section;
                        handleSectionNavigation(section);
                    });
                });
            });

            // Handle section navigation
            function handleSectionNavigation(section) {
                console.log(`🧭 Navegando para seção: ${section}`);
                const contentArea = document.querySelector('.content-area');

                switch(section) {
                    case 'telefonia':
                        console.log('📞 Mostrando seção Telefonia');
                        // Show telefonia dashboard (current functionality)
                        showTelefoniaSection();
                        break;
                    case 'whatsapp':
                        console.log('💬 Redirecionando para Dashboard Chatwoot');
                        // Redirect to Chatwoot dashboard
                        window.location.href = '/chatwoot';
                        break;
                    case 'consolidado':
                        console.log('📊 Mostrando seção Consolidado');
                        // Show Consolidado development page (simplified)
                        showSimpleDevelopmentPage('Consolidado', 'fas fa-chart-pie');
                        break;
                    default:
                        console.log(`❓ Seção desconhecida: ${section}`);
                }
            }

            function showTelefoniaSection() {
                console.log('📞 Mostrando seção de Telefonia...');

                // Show the main telefonia content
                const contentArea = document.querySelector('.content-area');
                const telefoniaContent = document.getElementById('telefonia-content');
                const devPage = document.getElementById('development-page');

                // Always show the content area for telefonia
                if (contentArea) {
                    console.log('👁️ Mostrando content-area...');
                    contentArea.style.display = 'block';
                    contentArea.style.visibility = 'visible';
                }

                // Show telefonia specific content
                if (telefoniaContent) {
                    console.log('👁️ Mostrando telefonia-content...');
                    telefoniaContent.style.display = 'block';
                }

                // Restore all tables and cards
                const allTables = document.querySelectorAll('.table, .card, .table-responsive');
                console.log(`👁️ Restaurando ${allTables.length} tabelas/cards...`);
                allTables.forEach(element => {
                    element.style.display = '';
                });

                // Restore specific sections
                const specificSections = document.querySelectorAll('#satisfactionTable, #agents-grid');
                console.log(`👁️ Restaurando ${specificSections.length} seções específicas...`);
                specificSections.forEach(element => {
                    element.style.display = '';
                });

                // Hide development page if visible
                if (devPage) {
                    console.log('🔒 Escondendo página de desenvolvimento...');
                    devPage.style.display = 'none';
                }

                // Load data for telefonia dashboard
                loadAllData();
            }

            function showDevelopmentPage(sectionName, iconClass, features) {
                const contentArea = document.querySelector('.content-area');
                if (!contentArea) return;

                // Hide the main content
                contentArea.style.display = 'none';

                // Create or update development page
                let devPage = document.getElementById('development-page');
                if (!devPage) {
                    devPage = document.createElement('div');
                    devPage.id = 'development-page';
                    devPage.className = 'development-page';
                    contentArea.parentNode.insertBefore(devPage, contentArea.nextSibling);
                }

                const featuresHtml = features.map(feature =>
                    `<li><i class="fas fa-check"></i> ${feature}</li>`
                ).join('');

                devPage.innerHTML = `
                    <div class="development-icon">
                        <i class="${iconClass}"></i>
                    </div>
                    <h1 class="development-title">${sectionName}</h1>
                    <p class="development-subtitle">
                        Esta seção está em desenvolvimento e estará disponível em breve.
                    </p>
                    <div class="development-features">
                        <h4>Funcionalidades Planejadas:</h4>
                        <ul>
                            ${featuresHtml}
                        </ul>
                    </div>
                `;

                devPage.style.display = 'block';
            }

            // Função simplificada para páginas de desenvolvimento
            function showSimpleDevelopmentPage(sectionName, iconClass) {
                console.log(`🔄 Chamando showSimpleDevelopmentPage para: ${sectionName}`);

                const mainContent = document.getElementById('mainContent');
                const contentArea = document.querySelector('.content-area');

                console.log('📍 mainContent encontrado:', !!mainContent);
                console.log('📍 contentArea encontrada:', !!contentArea);

                if (!mainContent) {
                    console.error('❌ mainContent não encontrado!');
                    return;
                }

                // Hide the entire content area (including ALL tables)
                if (contentArea) {
                    console.log('🔒 Escondendo content-area completamente...');
                    contentArea.style.display = 'none';
                    contentArea.style.visibility = 'hidden';
                }

                // Force hide all tables and cards (extra safety)
                const allTables = document.querySelectorAll('.table, .card, .table-responsive');
                console.log(`🔒 Escondendo ${allTables.length} tabelas/cards adicionais...`);
                allTables.forEach(element => {
                    element.style.display = 'none';
                });

                // Force hide specific sections
                const specificSections = document.querySelectorAll('#satisfactionTable, #agents-grid, .card');
                console.log(`🔒 Escondendo ${specificSections.length} seções específicas...`);
                specificSections.forEach(element => {
                    element.style.display = 'none';
                });

                // Create or update development page
                let devPage = document.getElementById('development-page');
                if (!devPage) {
                    console.log('🆕 Criando nova página de desenvolvimento...');
                    devPage = document.createElement('div');
                    devPage.id = 'development-page';
                    devPage.className = 'development-page';
                    mainContent.appendChild(devPage);
                } else {
                    console.log('♻️ Reutilizando página de desenvolvimento existente...');
                }

                console.log('📝 Atualizando conteúdo da página de desenvolvimento...');
                devPage.innerHTML = `
                    <div class="development-container">
                        <div class="development-icon">
                            <i class="${iconClass}"></i>
                        </div>
                        <h1 class="development-title">${sectionName}</h1>
                        <div class="development-message">
                            <i class="fas fa-tools"></i>
                            <p>Esta seção está em desenvolvimento.</p>
                            <small class="text-muted">Em breve, novas funcionalidades estarão disponíveis.</small>
                        </div>
                        <button class="btn btn-primary mt-3" onclick="handleSectionNavigation('telefonia')">
                            <i class="fas fa-arrow-left me-2"></i>
                            Voltar para Telefonia
                        </button>
                    </div>
                `;

                console.log('👁️ Mostrando página de desenvolvimento...');
                devPage.style.display = 'block';
            }

            // Update current time
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleTimeString('pt-BR', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
                document.getElementById('current-time').textContent = timeString;
            }

            updateTime();
            setInterval(updateTime, 60000); // Update every minute

            // Variáveis globais para armazenar dados
            let distributionData = null;
            let satisfactionData = null;
            let satisfactionChart = null;
            let hourlyChart = null;

            // Atualiza relógio
            function updateTime() {
                const now = new Date();
                document.getElementById('current-time').textContent = now.toLocaleString('pt-BR');
            }
            updateTime();
            setInterval(updateTime, 1000);

            // Carrega métricas do dashboard (otimizado para todas as filas)
            function loadMetrics() {
                if (distributionData) {
                    const summary = distributionData.data['relatorio de distribuição'].sumario;
                    const filas = distributionData.data['relatorio de distribuição'].chamadas_por_filas;

                    // Total de chamadas (consolidado)
                    const totalCalls = summary['Total de chamadas']['Número de chamadas conectadas'];

                    // Calcula Service Level médio ponderado de todas as filas ativas
                    const filasAtivas = filas.filter(f => parseInt(f.Recebidas) > 0);
                    let serviceLevelMedio = 0;
                    let tempoEsperaMedio = 0;

                    if (filasAtivas.length > 0) {
                        const totalRecebidas = filasAtivas.reduce((sum, f) => sum + parseInt(f.Recebidas), 0);

                        // Service Level ponderado pelo volume de chamadas
                        serviceLevelMedio = filasAtivas.reduce((sum, f) => {
                            const peso = parseInt(f.Recebidas) / totalRecebidas;
                            return sum + (parseFloat(f['Nível de serviço']) * peso);
                        }, 0);

                        // Tempo de espera médio ponderado
                        const temposEmSegundos = filasAtivas.map(f => {
                            const tempo = f['Espera Média'];
                            const [h, m, s] = tempo.split(':').map(Number);
                            return h * 3600 + m * 60 + s;
                        });

                        const tempoMedioSegundos = filasAtivas.reduce((sum, f, index) => {
                            const peso = parseInt(f.Recebidas) / totalRecebidas;
                            return sum + (temposEmSegundos[index] * peso);
                        }, 0);

                        const horas = Math.floor(tempoMedioSegundos / 3600);
                        const minutos = Math.floor((tempoMedioSegundos % 3600) / 60);
                        const segundos = Math.floor(tempoMedioSegundos % 60);
                        tempoEsperaMedio = `${horas.toString().padStart(2, '0')}:${minutos.toString().padStart(2, '0')}:${segundos.toString().padStart(2, '0')}`;
                    } else {
                        // Fallback para primeira fila se nenhuma estiver ativa
                        serviceLevelMedio = parseFloat(filas[0]['Nível de serviço']);
                        tempoEsperaMedio = filas[0]['Espera Média'];
                    }

                    // Métricas principais
                    document.getElementById('total-calls').textContent = totalCalls.toLocaleString();
                    document.getElementById('service-level').textContent = serviceLevelMedio.toFixed(1) + '%';
                    document.getElementById('avg-wait-time').textContent = tempoEsperaMedio;

                    // Métricas adicionais
                    const totalAtendidas = summary['Total de chamadas']['Número de chamadas atendidas'];
                    const taxaAbandono = parseFloat(summary['Total de chamadas']['Taxa de Abandono']);
                    const numFilasAtivas = filasAtivas.length;

                    document.getElementById('answered-calls').textContent = totalAtendidas.toLocaleString();
                    document.getElementById('abandonment-rate').textContent = taxaAbandono.toFixed(1) + '%';
                    document.getElementById('active-queues').textContent = numFilasAtivas;

                    // Calcula AHT médio ponderado
                    if (filasAtivas.length > 0) {
                        const totalRecebidas = filasAtivas.reduce((sum, f) => sum + parseInt(f.Recebidas), 0);
                        const temposAHTSegundos = filasAtivas.map(f => {
                            const tempo = f['Duração Média'];
                            const [h, m, s] = tempo.split(':').map(Number);
                            return h * 3600 + m * 60 + s;
                        });

                        const ahtMedioSegundos = filasAtivas.reduce((sum, f, index) => {
                            const peso = parseInt(f.Recebidas) / totalRecebidas;
                            return sum + (temposAHTSegundos[index] * peso);
                        }, 0);

                        const horasAHT = Math.floor(ahtMedioSegundos / 3600);
                        const minutosAHT = Math.floor((ahtMedioSegundos % 3600) / 60);
                        const segundosAHT = Math.floor(ahtMedioSegundos % 60);
                        const ahtFormatado = `${horasAHT.toString().padStart(2, '0')}:${minutosAHT.toString().padStart(2, '0')}:${segundosAHT.toString().padStart(2, '0')}`;

                        document.getElementById('avg-handle-time').textContent = ahtFormatado;
                    } else {
                        document.getElementById('avg-handle-time').textContent = '00:00:00';
                    }

                    // Atualiza informações do período
                    const period = distributionData.period;
                    if (period) {
                        const startFormatted = new Date(period.start).toLocaleDateString('pt-BR');
                        const endFormatted = new Date(period.end).toLocaleDateString('pt-BR');
                        const periodText = startFormatted === endFormatted ? startFormatted : `${startFormatted} a ${endFormatted}`;

                        document.getElementById('total-calls-period').textContent = periodText;
                        document.getElementById('service-level-period').textContent = periodText;
                        document.getElementById('avg-wait-period').textContent = periodText;
                    }
                } else {
                    // Dados simulados se não houver dados reais
                    document.getElementById('total-calls').textContent = (Math.floor(Math.random() * 1000) + 500).toLocaleString();
                    document.getElementById('service-level').textContent = (Math.random() * 20 + 80).toFixed(1) + '%';
                    document.getElementById('avg-wait-time').textContent = (Math.random() * 30 + 15).toFixed(1) + 's';
                }

                if (satisfactionData) {
                    // Calcula score de satisfação consolidado de todas as filas
                    const porFila = satisfactionData.data['Pesquisa Satisfação']['Pesquisa por fila']['Pesquisa Amvox'];

                    // Verifica se há dados de avaliação
                    let avgScore = 0;
                    let totalAvaliacoes = 0;

                    if (porFila['Av-1-Avalia Atendente'] && porFila['Av-1-Avalia Atendente'].length > 0) {
                        const av1Data = porFila['Av-1-Avalia Atendente'][0];
                        const av2Data = porFila['Av-2-Avalia Chamada'] && porFila['Av-2-Avalia Chamada'].length > 0 ? porFila['Av-2-Avalia Chamada'][0] : null;
                        const av3Data = porFila['Av-3-Avalia Empresa'] && porFila['Av-3-Avalia Empresa'].length > 0 ? porFila['Av-3-Avalia Empresa'][0] : null;

                        // Calcula percentual de satisfação para cada tipo (com verificação de dados)
                        const av1Satisfaction = av1Data && av1Data['Avaliadas'] > 0 ? (av1Data['Satisfeito'] / av1Data['Avaliadas']) * 100 : 0;
                        const av2Satisfaction = av2Data && av2Data['Avaliadas'] > 0 ? (av2Data['Sim'] / av2Data['Avaliadas']) * 100 : 0;
                        const av3Satisfaction = av3Data && av3Data['Avaliadas'] > 0 ? ((av3Data['Nota 4'] + av3Data['Nota 5']) / av3Data['Avaliadas']) * 100 : 0;

                        // Score médio ponderado (considera apenas avaliações com dados)
                        const scores = [av1Satisfaction, av2Satisfaction, av3Satisfaction].filter(s => s > 0);
                        avgScore = scores.length > 0 ? (scores.reduce((sum, s) => sum + s, 0) / scores.length) : 0;

                        totalAvaliacoes = (av1Data ? av1Data['Avaliadas'] : 0) +
                                        (av2Data ? av2Data['Avaliadas'] : 0) +
                                        (av3Data ? av3Data['Avaliadas'] : 0);
                    }

                    document.getElementById('satisfaction-score').textContent = avgScore.toFixed(1) + '%';

                    // Adiciona informação sobre volume de avaliações
                    const satisfactionElement = document.getElementById('satisfaction-score');
                    if (satisfactionElement && totalAvaliacoes > 0) {
                        satisfactionElement.title = `Baseado em ${totalAvaliacoes} avaliações`;
                    }

                    // Atualiza informação do período
                    const period = satisfactionData.period;
                    if (period) {
                        const startFormatted = new Date(period.start).toLocaleDateString('pt-BR');
                        const endFormatted = new Date(period.end).toLocaleDateString('pt-BR');
                        const periodText = startFormatted === endFormatted ? startFormatted : `${startFormatted} a ${endFormatted}`;

                        document.getElementById('satisfaction-period').textContent = periodText;
                    }
                } else {
                    document.getElementById('satisfaction-score').textContent = (Math.random() * 40 + 40).toFixed(1) + '%';
                }
            }

            // Carrega todos os dados de forma otimizada
            async function loadAllData() {
                console.log('🔄 Iniciando carregamento otimizado de dados...');

                // Mostra loading overlay em mobile
                if (window.innerWidth <= 768) {
                    showLoading();
                }

                // Atualiza indicador de período
                updatePeriodIndicator();

                // Mostra loading nos cards principais
                showLoadingInMetrics();

                try {
                    // Obtém parâmetros dos filtros
                    const params = getFilterParams();
                    console.log('📊 Parâmetros dos filtros:', params);

                    // Carrega dados de forma paralela e otimizada
                    const [
                        distributionResult,
                        satisfactionResult,
                        advancedMetricsResult,
                        performanceDashboardResult,
                        agentsResult
                    ] = await Promise.allSettled([
                        fetch(`/api/distribution?${params}`).then(r => r.json()),
                        fetch(`/api/satisfaction?${params}`).then(r => r.json()),
                        fetch(`/api/advanced-metrics?${params}`).then(r => r.json()),
                        fetch(`/api/performance-dashboard?${params}`).then(r => r.json()),
                        fetch(`/api/agents?${params}`).then(r => r.json())
                    ]);

                    // Processa resultados
                    if (distributionResult.status === 'fulfilled' && distributionResult.value.success) {
                        distributionData = distributionResult.value;
                        console.log('✅ Dados de distribuição carregados:', distributionData.period);
                    }

                    if (satisfactionResult.status === 'fulfilled' && satisfactionResult.value.success) {
                        satisfactionData = satisfactionResult.value;
                        console.log('✅ Dados de satisfação carregados:', satisfactionData.period);
                    }

                    // Renderiza todas as visualizações
                    await renderAllVisualizations({
                        distribution: distributionResult.value,
                        satisfaction: satisfactionResult.value,
                        advancedMetrics: advancedMetricsResult.value,
                        performanceDashboard: performanceDashboardResult.value,
                        agents: agentsResult.value
                    });

                    console.log('✅ Carregamento otimizado concluído com sucesso!');

                } catch (error) {
                    console.error('❌ Erro ao carregar dados:', error);
                    showErrorMessage('Erro ao carregar dados. Tente novamente.');
                } finally {
                    // Esconde loading overlay
                    if (window.innerWidth <= 768) {
                        hideLoading();
                    }
                }
            }

            // Função auxiliar para obter parâmetros dos filtros (sempre usa ambas as filas)
            function getFilterParams() {
                const startDateEl = document.getElementById('startDate');
                const endDateEl = document.getElementById('endDate');

                const startDate = startDateEl ? startDateEl.value : new Date().toISOString().split('T')[0];
                const endDate = endDateEl ? endDateEl.value : new Date().toISOString().split('T')[0];

                // Sempre usa ambas as filas (802 e 803)
                const selectedQueues = '802,803';

                const params = new URLSearchParams();
                params.append('start_date', startDate);
                params.append('end_date', endDate);
                params.append('queues', selectedQueues);

                console.log('📊 Parâmetros fixos - sempre usando filas 802 e 803:', {
                    start_date: startDate,
                    end_date: endDate,
                    queues: selectedQueues
                });

                return params.toString();
            }

            // Função otimizada para renderizar todas as visualizações
            async function renderAllVisualizations(data) {
                console.log('🎨 Renderizando visualizações...');

                try {
                    // Renderiza métricas principais
                    if (data.distribution && data.distribution.success) {
                        loadMetrics();
                        loadDistributionTable();
                        loadHourlyChart();
                        loadCallDetails();
                    }

                    // Renderiza dados de satisfação
                    if (data.satisfaction && data.satisfaction.success) {
                        loadSatisfactionChart();
                        loadSatisfactionTable();
                    }

                    // Renderiza dados de agentes
                    if (data.agents && data.agents.success) {
                        loadAgentsData();
                    }

                    // Renderiza análise automática
                    loadAnalysis();

                    console.log('✅ Todas as visualizações renderizadas!');
                } catch (error) {
                    console.error('❌ Erro ao renderizar visualizações:', error);
                }
            }

            // Função para mostrar mensagens de erro
            function showErrorMessage(message) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
                errorDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
                errorDiv.innerHTML = `
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(errorDiv);

                // Remove automaticamente após 5 segundos
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.parentNode.removeChild(errorDiv);
                    }
                }, 5000);
            }

            // Atualiza indicador de período
            function updatePeriodIndicator() {
                const startDateEl = document.getElementById('startDate');
                const endDateEl = document.getElementById('endDate');

                if (!startDateEl || !endDateEl) {
                    console.warn('Elementos de data não encontrados');
                    return;
                }

                const startDate = startDateEl.value;
                const endDate = endDateEl.value;

                // Sempre usa ambas as filas
                const selectedQueues = ['Fila 802', 'Fila 803'];

                if (startDate && endDate) {
                    const startFormatted = new Date(startDate).toLocaleDateString('pt-BR');
                    const endFormatted = new Date(endDate).toLocaleDateString('pt-BR');
                    const queuesText = selectedQueues.join(', ');

                    // Atualiza o título da página com o período
                    const pageTitle = document.querySelector('.page-title');
                    if (pageTitle) {
                        pageTitle.textContent = `Dashboard - ${startFormatted} a ${endFormatted} - ${queuesText}`;
                    }
                }
            }

            // Mostra loading nos cards de métricas
            function showLoadingInMetrics() {
                const elements = ['total-calls', 'service-level', 'avg-wait-time', 'satisfaction-rate'];
                elements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    }
                });
            }



            // Relatório de distribuição (sempre usa ambas as filas)
            async function getDistributionReport() {
                try {
                    const startDateEl = document.getElementById('startDate');
                    const endDateEl = document.getElementById('endDate');

                    if (!startDateEl || !endDateEl) {
                        console.warn('Elementos de data não encontrados para relatório de distribuição');
                        return;
                    }

                    const startDate = startDateEl.value;
                    const endDate = endDateEl.value;

                    // Sempre usa ambas as filas (802 e 803)
                    const selectedQueues = '802,803';

                    const params = new URLSearchParams();
                    if (startDate) params.append('start_date', startDate);
                    if (endDate) params.append('end_date', endDate);
                    params.append('queues', selectedQueues);

                    console.log('📊 Buscando dados de distribuição para filas 802 e 803:', {
                        start_date: startDate,
                        end_date: endDate,
                        queues: selectedQueues
                    });

                    const response = await fetch(`/api/distribution?${params}`);
                    const data = await response.json();

                    if (data.success) {
                        distributionData = data;
                        console.log('✅ Dados de distribuição carregados:', data.period);
                        return data;
                    } else {
                        console.error('❌ Erro no relatório de distribuição:', data.message);
                        return null;
                    }
                } catch (error) {
                    console.error('❌ Erro na requisição de distribuição:', error);
                    return null;
                }
            }



            // Carrega tabela de distribuição (apenas fila RECEPTIVO AMVOX)
            function loadDistributionTable() {
                if (!distributionData) return;

                const tbody = document.querySelector('#distributionTable tbody');
                const queuesData = distributionData.data['relatorio de distribuição'].chamadas_por_filas;

                let html = '';

                // Filtra apenas a fila RECEPTIVO AMVOX (fila 802)
                const receptivoQueue = queuesData.find(queue => queue.Filas.includes('RECEPTIVO AMVOX') || queue.Filas.includes('802'));

                if (receptivoQueue) {
                    const queueName = receptivoQueue.Filas.split(' | ')[1] || receptivoQueue.Filas;
                    const serviceLevel = parseFloat(receptivoQueue['Nível de serviço']);
                    const attendanceRate = parseFloat(receptivoQueue['Taxa de Atendidas']);
                    const duracaoMedia = receptivoQueue['Duração Média'] || '00:00:00';
                    const esperaMedia = receptivoQueue['Espera Média'] || '00:00:00';

                    html += `
                        <tr>
                            <td><strong>${queueName}</strong></td>
                            <td><span class="badge bg-primary">${receptivoQueue.Recebidas}</span></td>
                            <td><span class="badge bg-success">${receptivoQueue.Atendidas}</span></td>
                            <td><span class="badge bg-danger">${receptivoQueue['Não-Atendidas']}</span></td>
                            <td>
                                <div class="progress progress-custom" style="height: 25px;">
                                    <div class="progress-bar ${serviceLevel >= 80 ? 'bg-success' : serviceLevel >= 60 ? 'bg-warning' : 'bg-danger'}"
                                         style="width: ${serviceLevel}%; font-size: 13px; line-height: 25px;">
                                        ${serviceLevel}%
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="progress progress-custom" style="height: 25px;">
                                    <div class="progress-bar bg-success"
                                         style="width: ${attendanceRate}%; font-size: 13px; line-height: 25px;">
                                        ${attendanceRate}%
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info" style="font-size: 12px;">
                                    <i class="fas fa-clock me-1"></i>${duracaoMedia}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-secondary" style="font-size: 12px;">
                                    <i class="fas fa-hourglass-half me-1"></i>${esperaMedia}
                                </span>
                            </td>
                        </tr>
                    `;
                } else {
                    // Fallback se não encontrar a fila RECEPTIVO AMVOX
                    html = `
                        <tr>
                            <td colspan="8" class="text-center text-muted">
                                <i class="fas fa-info-circle me-2"></i>
                                Nenhum dado disponível para a fila RECEPTIVO AMVOX no período selecionado
                            </td>
                        </tr>
                    `;
                }

                tbody.innerHTML = html;

                console.log('📊 Tabela de distribuição carregada - apenas fila RECEPTIVO AMVOX');
            }

            // Pesquisa de satisfação (sempre usa ambas as filas)
            async function getSatisfactionReport() {
                try {
                    const startDateEl = document.getElementById('startDate');
                    const endDateEl = document.getElementById('endDate');

                    if (!startDateEl || !endDateEl) {
                        console.warn('Elementos de data não encontrados para relatório de satisfação');
                        return;
                    }

                    const startDate = startDateEl.value;
                    const endDate = endDateEl.value;

                    // Sempre usa ambas as filas (802 e 803)
                    const selectedQueues = '802,803';

                    const params = new URLSearchParams();
                    if (startDate) params.append('start_date', startDate);
                    if (endDate) params.append('end_date', endDate);
                    params.append('queues', selectedQueues);

                    console.log('📊 Buscando dados de satisfação para filas 802 e 803:', {
                        start_date: startDate,
                        end_date: endDate,
                        queues: selectedQueues
                    });

                    const response = await fetch(`/api/satisfaction?${params}`);
                    const data = await response.json();

                    if (data.success) {
                        satisfactionData = data;
                        console.log('✅ Dados de satisfação carregados:', data.period);
                        return data;
                    } else {
                        console.error('❌ Erro no relatório de satisfação:', data.message);
                        return null;
                    }
                } catch (error) {
                    console.error('❌ Erro na requisição de satisfação:', error);
                    return null;
                }
            }


            // Carrega gráfico de satisfação
            function loadSatisfactionChart() {
                if (!satisfactionData) return;

                const ctx = document.getElementById('satisfactionChart').getContext('2d');
                const summary = satisfactionData.data['Pesquisa Satisfação'].sumario['Pesquisas Efetuadas'][0];

                if (satisfactionChart) {
                    satisfactionChart.destroy();
                }

                satisfactionChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['Sem Avaliação', 'Av-1 (Atendente)', 'Av-2 (Chamada)', 'Av-3 (Empresa)'],
                        datasets: [{
                            label: 'Quantidade',
                            data: [summary['Sem Avaliacao'], summary['Av-1'], summary['Av-2'], summary['Av-3']],
                            backgroundColor: ['#6c757d', '#3498db', '#2ecc71', '#f39c12'],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            title: {
                                display: true,
                                text: 'Distribuição das Pesquisas de Satisfação'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
            }

            // Carrega tabela de satisfação
            function loadSatisfactionTable() {
                if (!satisfactionData) return;

                const tbody = document.querySelector('#satisfactionTable tbody');
                const summary = satisfactionData.data['Pesquisa Satisfação'].sumario['Pesquisas Efetuadas'][0];
                const porFila = satisfactionData.data['Pesquisa Satisfação']['Pesquisa por fila']['Pesquisa Amvox'];

                // Calcula valores reais baseados nos dados detalhados
                const av1Data = porFila['Av-1-Avalia Atendente'][0];
                const av2Data = porFila['Av-2-Avalia Chamada'][0];
                const av3Data = porFila['Av-3-Avalia Empresa'][0];

                const evaluations = [
                    {
                        name: 'Av-1 (Atendente)',
                        sem: summary['Sem Avaliacao'],
                        aval: summary['Av-1'],
                        pos: av1Data['Satisfeito'] || 0,
                        neg: av1Data['Insatisfeito'] || 0,
                        meta: summary['% meta Av-1']
                    },
                    {
                        name: 'Av-2 (Chamada)',
                        sem: summary['Sem Avaliacao'],
                        aval: summary['Av-2'],
                        pos: av2Data['Sim'] || 0,
                        neg: av2Data['Não'] || 0,
                        meta: summary['% meta Av-2']
                    },
                    {
                        name: 'Av-3 (Empresa)',
                        sem: summary['Sem Avaliacao'],
                        aval: summary['Av-3'],
                        pos: (av3Data['Nota 4'] || 0) + (av3Data['Nota 5'] || 0),
                        neg: (av3Data['Nota 1'] || 0) + (av3Data['Nota 2'] || 0) + (av3Data['Nota 3'] || 0),
                        meta: summary['% meta Av-3']
                    }
                ];

                let html = '';
                evaluations.forEach(eval => {
                    const total = eval.pos + eval.neg;
                    const satisfactionRate = total > 0 ? ((eval.pos / total) * 100).toFixed(1) : 0;
                    const score = total > 0 ? (eval.pos / total * 10).toFixed(1) : 0;

                    html += `
                        <tr>
                            <td class="text-center">
                                <div class="d-flex align-items-center justify-content-center">
                                    <div class="satisfaction-icon me-2">
                                        ${eval.name.includes('Atendente') ? '<i class="fas fa-user text-primary"></i>' :
                                          eval.name.includes('Chamada') ? '<i class="fas fa-phone text-success"></i>' :
                                          '<i class="fas fa-building text-info"></i>'}
                                    </div>
                                    <div>
                                        <strong>${eval.name}</strong>
                                        <br><small class="text-muted">${total} avaliações</small>
                                    </div>
                                </div>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-primary fs-6">${total}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-success fs-6">${eval.pos}</span>
                                <br><small class="text-muted">${satisfactionRate}%</small>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-danger fs-6">${eval.neg}</span>
                                <br><small class="text-muted">${(100 - satisfactionRate).toFixed(1)}%</small>
                            </td>
                            <td class="text-center">
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar ${satisfactionRate >= 80 ? 'bg-success' : satisfactionRate >= 60 ? 'bg-warning' : 'bg-danger'}"
                                         style="width: ${satisfactionRate}%; font-size: 12px; line-height: 20px;">
                                        ${satisfactionRate}%
                                    </div>
                                </div>
                            </td>
                            <td class="text-center">
                                <span class="badge ${score >= 8 ? 'bg-success' : score >= 6 ? 'bg-warning' : 'bg-danger'} fs-6">
                                    ${score}/10
                                </span>
                                <br><small class="text-muted">Meta: ${eval.meta}%</small>
                            </td>
                        </tr>
                    `;
                });

                tbody.innerHTML = html;
            }

            // Carrega dados dos agentes
            function loadAgentsData() {
                if (!satisfactionData) return;

                const agentsContainer = document.getElementById('agents-grid');
                if (!agentsContainer) return;

                const agentsData = satisfactionData.data['Pesquisa Satisfação']['Pesquisa por Agente']['Pesquisa Amvox']['Av-1-Avalia Atendente'];

                let html = '';

                try {
                    // Verifica se agentsData é um objeto (como esperado) ou array
                    if (typeof agentsData === 'object' && !Array.isArray(agentsData)) {
                        // Se for objeto, itera sobre as chaves
                        Object.entries(agentsData).forEach(([agentName, agentInfo]) => {
                            if (typeof agentInfo === 'object') {
                                const satisfeitos = agentInfo.Satisfeitos || 0;
                                const insatisfeitos = agentInfo.Insatisfeitos || 0;
                                const total = satisfeitos + insatisfeitos;
                                const satisfactionRate = total > 0 ? (satisfeitos / total * 100) : 0;
                                const agentDisplayName = agentName.replace('_', ' ');

                                html += `
                                    <div class="col-sm-6 col-lg-4">
                                        <div class="agent-card" onclick="showAgentDetails('${agentName}')">
                                            <div class="agent-name">
                                                <i class="fas fa-user"></i>
                                                ${agentDisplayName}
                                                <i class="fas fa-external-link-alt ms-auto" style="font-size: 0.8rem; opacity: 0.6;"></i>
                                            </div>
                                            <div class="agent-stats">
                                                <div class="agent-stat">
                                                    <div class="agent-stat-value">${total}</div>
                                                    <div class="agent-stat-label">Avaliações</div>
                                                </div>
                                                <div class="agent-stat">
                                                    <div class="agent-stat-value" style="color: var(--success)">${satisfactionRate.toFixed(1)}%</div>
                                                    <div class="agent-stat-label">Satisfação</div>
                                                </div>
                                            </div>
                                            <div class="mb-2">
                                                <div style="font-size: 0.75rem; color: var(--text-muted); margin-bottom: 0.25rem;">Performance</div>
                                                <div class="progress agent-progress-bar">
                                                    <div class="progress-bar ${satisfactionRate >= 80 ? 'bg-success' : satisfactionRate >= 60 ? 'bg-warning' : 'bg-danger'}" style="width: ${satisfactionRate}%">
                                                        <span class="progress-percentage">${satisfactionRate.toFixed(1)}%</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex gap-1">
                                                <span class="status-indicator status-success" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;">
                                                    ✓ ${satisfeitos}
                                                </span>
                                                <span class="status-indicator status-danger" style="font-size: 0.75rem; padding: 0.25rem 0.5rem;">
                                                    ✗ ${insatisfeitos}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            }
                        });
                    } else if (Array.isArray(agentsData)) {
                        // Se for array, usa a estrutura original
                        agentsData.forEach(agent => {
                            const satisfactionRate = parseFloat(agent['% Avaliadas'] || 0);
                            const metaRate = parseFloat(agent['% Meta(2.0)'] || 0);
                            const agentName = agent.Agente || 'Agente_Desconhecido';
                            const agentDisplayName = agentName.replace('_', ' ');

                            html += `
                                <div class="col-sm-6 col-lg-4 mb-3">
                                    <div class="card agent-card-enhanced" style="cursor: pointer;" onclick="showAgentDetails('${agentName}')">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-user me-2"></i>
                                                ${agentDisplayName}
                                                <i class="fas fa-external-link-alt ms-2 text-muted" style="font-size: 0.8rem;"></i>
                                            </h6>
                                            <div class="row text-center mb-3">
                                                <div class="col-6">
                                                    <small class="text-muted">
                                                        <i class="fas fa-clipboard-list me-1"></i>Pesquisas
                                                    </small>
                                                    <div class="h5 text-primary">${agent.Avaliadas || 0}</div>
                                                    <small class="text-muted" style="font-size: 0.7rem;">respondidas</small>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-muted">
                                                        <i class="fas fa-percentage me-1"></i>Participação
                                                    </small>
                                                    <div class="h5 text-info">${satisfactionRate.toFixed(1)}%</div>
                                                    <small class="text-muted" style="font-size: 0.7rem;">dos clientes</small>
                                                </div>
                                            </div>
                                            <div class="satisfaction-section">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <small class="text-muted fw-bold">
                                                        <i class="fas fa-heart me-1"></i>Satisfação Real
                                                    </small>
                                                    <span class="badge ${metaRate >= 70 ? 'bg-success' : metaRate >= 50 ? 'bg-warning' : 'bg-danger'} fs-6">
                                                        ${metaRate >= 70 ? 'Excelente' : metaRate >= 50 ? 'Bom' : 'Regular'}
                                                    </span>
                                                </div>
                                                <div class="mb-1">
                                                    <small class="text-muted" style="font-size: 0.7rem;">
                                                        <i class="fas fa-info-circle me-1"></i>
                                                        Baseado nas ${agent.Avaliadas || 0} pesquisas respondidas
                                                    </small>
                                                </div>
                                                <div class="progress progress-enhanced-agent">
                                                    <div class="progress-bar ${metaRate >= 70 ? 'bg-success' : metaRate >= 50 ? 'bg-warning' : 'bg-danger'}" style="width: ${metaRate}%">
                                                        <span class="progress-text-agent">${metaRate}%</span>
                                                    </div>
                                                </div>
                                                <div class="progress-labels-agent">
                                                    <small class="text-muted">0%</small>
                                                    <small class="text-muted">100%</small>
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <div class="row text-center">
                                                    <div class="col-6">
                                                        <span class="badge bg-success w-100">
                                                            <i class="fas fa-smile me-1"></i>
                                                            ${agent.Satisfeito || 0} Satisfeitos
                                                        </span>
                                                    </div>
                                                    <div class="col-6">
                                                        <span class="badge bg-danger w-100">
                                                            <i class="fas fa-frown me-1"></i>
                                                            ${agent.Insatisfeito || 0} Insatisfeitos
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mt-2 text-center">
                                                <div class="explanation-box">
                                                    <small class="text-info">
                                                        <i class="fas fa-lightbulb me-1"></i>
                                                        <strong>Participação:</strong> ${satisfactionRate.toFixed(1)}% dos clientes responderam à pesquisa
                                                    </small>
                                                </div>
                                                <small class="text-muted mt-1 d-block">
                                                    <i class="fas fa-mouse-pointer me-1"></i>
                                                    Clique para ver detalhes
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                    }

                    agentsContainer.innerHTML = html;
                } catch (error) {
                    console.error('Erro ao carregar dados dos agentes:', error);
                    if (agentsContainer) {
                        agentsContainer.innerHTML = '<div class="col-12"><div class="alert alert-warning">Erro ao carregar dados dos agentes</div></div>';
                    }
                }
            }

            // Carrega gráfico por hora
            function loadHourlyChart() {
                if (!distributionData) return;

                const chartElement = document.getElementById('hourlyChart');
                if (!chartElement) {
                    console.warn('Elemento hourlyChart não encontrado');
                    return;
                }

                const ctx = chartElement.getContext('2d');
                const hourlyData = distributionData.data['relatorio de distribuição']['Chamadas por hora'];

                const labels = hourlyData.map(h => h.Hora.split(' - ')[0]);
                const calls = hourlyData.map(h => h.Recebidas);
                const serviceLevel = hourlyData.map(h => parseFloat(h['Nível de serviço']));

                if (hourlyChart) {
                    hourlyChart.destroy();
                }

                hourlyChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Chamadas Recebidas',
                            data: calls,
                            borderColor: '#3498db',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            yAxisID: 'y'
                        }, {
                            label: 'Service Level (%)',
                            data: serviceLevel,
                            borderColor: '#2ecc71',
                            backgroundColor: 'rgba(46, 204, 113, 0.1)',
                            yAxisID: 'y1'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                title: {
                                    display: true,
                                    text: 'Chamadas'
                                }
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                title: {
                                    display: true,
                                    text: 'Service Level (%)'
                                },
                                grid: {
                                    drawOnChartArea: false,
                                }
                            }
                        }
                    }
                });
            }

            // Função para formatar data no formato dd/mm/yyyy hh:mm:ss
            function formatDateTime(dateTimeString) {
                try {
                    // Se a data já está no formato brasileiro, retorna como está
                    if (dateTimeString.includes('/')) {
                        return dateTimeString;
                    }

                    // Converte de yyyy-mm-dd hh:mm:ss para dd/mm/yyyy hh:mm:ss
                    const date = new Date(dateTimeString);
                    if (isNaN(date.getTime())) {
                        return dateTimeString; // Retorna original se não conseguir converter
                    }

                    const day = String(date.getDate()).padStart(2, '0');
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const year = date.getFullYear();
                    const hours = String(date.getHours()).padStart(2, '0');
                    const minutes = String(date.getMinutes()).padStart(2, '0');
                    const seconds = String(date.getSeconds()).padStart(2, '0');

                    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
                } catch (error) {
                    return dateTimeString; // Retorna original em caso de erro
                }
            }

            // Função para formatar número de telefone
            function formatPhoneNumber(phoneNumber) {
                if (!phoneNumber || phoneNumber === '-' || phoneNumber.trim() === '') {
                    return '-';
                }

                // Remove caracteres não numéricos
                let cleanNumber = phoneNumber.replace(/\D/g, '');

                // Remove prefixo "00" se presente (números brasileiros com prefixo internacional)
                if (cleanNumber.startsWith('00') && cleanNumber.length >= 12) {
                    cleanNumber = cleanNumber.slice(2);
                }

                // Se não tem números suficientes, retorna como está
                if (cleanNumber.length < 8) {
                    return phoneNumber;
                }

                // Formata baseado no tamanho do número
                if (cleanNumber.length === 11) {
                    // Celular brasileiro: (XX) 9 XXXX-XXXX
                    return `(${cleanNumber.slice(0, 2)}) ${cleanNumber.slice(2, 3)} ${cleanNumber.slice(3, 7)}-${cleanNumber.slice(7)}`;
                } else if (cleanNumber.length === 10) {
                    // Fixo brasileiro: (XX) XXXX-XXXX
                    return `(${cleanNumber.slice(0, 2)}) ${cleanNumber.slice(2, 6)}-${cleanNumber.slice(6)}`;
                } else if (cleanNumber.length === 13) {
                    // Internacional real: +XX (XX) 9 XXXX-XXXX
                    const countryCode = cleanNumber.slice(0, 2);
                    const areaCode = cleanNumber.slice(2, 4);
                    const firstDigit = cleanNumber.slice(4, 5);
                    const middlePart = cleanNumber.slice(5, 9);
                    const lastPart = cleanNumber.slice(9);
                    return `+${countryCode} (${areaCode}) ${firstDigit} ${middlePart}-${lastPart}`;
                } else if (cleanNumber.length === 12) {
                    // Internacional fixo: +XX (XX) XXXX-XXXX
                    const countryCode = cleanNumber.slice(0, 2);
                    const areaCode = cleanNumber.slice(2, 4);
                    const middlePart = cleanNumber.slice(4, 8);
                    const lastPart = cleanNumber.slice(8);
                    return `+${countryCode} (${areaCode}) ${middlePart}-${lastPart}`;
                } else {
                    // Outros formatos, tenta aplicar formatação básica
                    if (cleanNumber.length >= 10) {
                        // Tenta como celular se tem 11 dígitos após limpeza
                        if (cleanNumber.length === 11) {
                            return `(${cleanNumber.slice(0, 2)}) ${cleanNumber.slice(2, 3)} ${cleanNumber.slice(3, 7)}-${cleanNumber.slice(7)}`;
                        }
                        // Senão, formato genérico
                        return `(${cleanNumber.slice(0, 2)}) ${cleanNumber.slice(2, -4)}-${cleanNumber.slice(-4)}`;
                    }
                    return phoneNumber;
                }
            }

            // Carrega detalhes das chamadas
            function loadCallDetails() {
                if (!distributionData) return;

                const tbody = document.querySelector('#callDetailsTable tbody');
                const details = distributionData.data['relatorio de distribuição']['Detalhes da Distribuição'];

                let html = '';
                // Mostra as últimas 100 chamadas
                const recentCalls = details.slice(-100).reverse();

                recentCalls.forEach(call => {
                    const eventClass = call.Evento === 'COMPLETEAGENT' ? 'success' :
                                     call.Evento === 'COMPLETECALLER' ? 'info' :
                                     call.Evento === 'NO ANSWER' ? 'warning' : 'secondary';

                    // Formata a data e o número de telefone
                    const formattedDate = formatDateTime(call.Data);
                    const formattedPhone = formatPhoneNumber(call['Número telefônico']);

                    html += `
                        <tr>
                            <td><small>${formattedDate}</small></td>
                            <td><small>${call.Agente.replace('_', ' ')}</small></td>
                            <td><small>${formattedPhone}</small></td>
                            <td><span class="badge bg-${eventClass}">${call.Evento}</span></td>
                            <td><small>${call['Tempo de espera']}s</small></td>
                            <td><small>${call['Tempo das chamadas']}</small></td>
                        </tr>
                    `;
                });

                tbody.innerHTML = html;
            }

            // Carrega status do sistema
            async function loadSystemStatus() {
                try {
                    const response = await fetch('/health');
                    const data = await response.json();

                    let statusHtml = '';
                    if (data.status === 'healthy') {
                        statusHtml = '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i><strong>Sistema Operacional</strong><br>Conexão 3CX: ' + (data.threecx_connection ? 'Ativa' : 'Inativa') + '<br>Última verificação: ' + new Date(data.timestamp).toLocaleString('pt-BR') + '</div>';
                    } else {
                        statusHtml = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i><strong>Sistema com Problemas</strong></div>';
                    }

                    document.getElementById('system-status').innerHTML = statusHtml;
                } catch (error) {
                    document.getElementById('system-status').innerHTML = '<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i><strong>Erro ao verificar status</strong></div>';
                }
            }

            // Define períodos pré-definidos
            function setQuickPeriod(period) {
                const today = new Date();
                let startDate, endDate;

                switch(period) {
                    case 'today':
                        startDate = endDate = today;
                        break;
                    case 'yesterday':
                        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
                        startDate = endDate = yesterday;
                        break;
                    case 'week':
                        endDate = today;
                        startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                        break;
                    case 'month':
                        endDate = today;
                        startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
                        break;
                }

                const startDateEl = document.getElementById('startDate');
                const endDateEl = document.getElementById('endDate');

                if (startDateEl && endDateEl) {
                    startDateEl.value = startDate.toISOString().split('T')[0];
                    endDateEl.value = endDate.toISOString().split('T')[0];

                    // Atualiza automaticamente os dados
                    loadAllData();
                }
            }

            // Carrega análises automáticas
            async function loadAnalysis() {
                try {
                    // Usa as mesmas datas dos filtros principais
                    const startDate = document.getElementById('startDate') ? document.getElementById('startDate').value :
                                     new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
                    const endDate = document.getElementById('endDate') ? document.getElementById('endDate').value :
                                   new Date().toISOString().split('T')[0];

                    // Sempre usa ambas as filas (802 e 803)
                    const selectedQueues = '802,803';

                    const params = new URLSearchParams();
                    params.append('start_date', startDate);
                    params.append('end_date', endDate);
                    params.append('queues', selectedQueues);

                    // Mostra loading
                    const analysisContent = document.getElementById('analysis-content');
                    if (analysisContent) {
                        analysisContent.innerHTML = `
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                                <p>Analisando dados e gerando insights...</p>
                            </div>
                        `;
                    }

                    const response = await fetch(`/api/executive-summary?${params}`);
                    const data = await response.json();

                    console.log('Debug - Dados recebidos do executive-summary:', data);
                    console.log('Debug - Key metrics:', data.executive_summary?.key_metrics);

                    if (data.success && analysisContent) {
                        displayAnalysis(data);
                    } else if (analysisContent) {
                        analysisContent.innerHTML = `
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Não foi possível gerar análises: ${data.message || 'Erro desconhecido'}
                            </div>
                        `;
                    }
                } catch (error) {
                    console.error('Erro ao carregar análises:', error);
                    const analysisContent = document.getElementById('analysis-content');
                    if (analysisContent) {
                        analysisContent.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Erro ao carregar análises automáticas: ${error.message}
                            </div>
                        `;
                    }
                }
            }

            // Exibe análises na interface
            function displayAnalysis(data) {
                const { executive_summary, detailed_analysis } = data;

                let html = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h5><i class="fas fa-chart-line me-2"></i>Resumo Executivo</h5>
                                <p><strong>Performance Geral:</strong>
                                    <span class="badge ${getPerformanceBadgeClass(executive_summary.overall_performance)}">
                                        ${executive_summary.overall_performance}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                `;

                // Métricas principais
                if (executive_summary.key_metrics) {
                    html += `
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6><i class="fas fa-tachometer-alt me-2"></i>Métricas Principais</h6>
                                <div class="row">
                    `;

                    const metrics = executive_summary.key_metrics;
                    console.log('Debug - Métricas recebidas:', metrics);
                    if (metrics.total_chamadas) {
                        console.log('Debug - Total chamadas:', metrics.total_chamadas);
                        html += `
                            <div class="col-6 col-lg-3 mb-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-primary">${metrics.total_chamadas}</h4>
                                        <small>Total Chamadas</small>
                                    </div>
                                </div>
                            </div>
                        `;
                    }

                    if (metrics.service_level_medio) {
                        html += `
                            <div class="col-6 col-lg-3 mb-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-info">${metrics.service_level_medio}%</h4>
                                        <small>Service Level Médio</small>
                                    </div>
                                </div>
                            </div>
                        `;
                    }

                    if (metrics.taxa_participacao) {
                        html += `
                            <div class="col-6 col-lg-3 mb-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-warning">${metrics.taxa_participacao}%</h4>
                                        <small>Taxa Participação</small>
                                    </div>
                                </div>
                            </div>
                        `;
                    }

                    if (metrics.satisfacao_atendente) {
                        html += `
                            <div class="col-6 col-lg-3 mb-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-success">${metrics.satisfacao_atendente}%</h4>
                                        <small>Satisfação Atendente</small>
                                    </div>
                                </div>
                            </div>
                        `;
                    }

                    html += `
                                </div>
                            </div>
                        </div>
                    `;
                }

                // Insights principais
                if (executive_summary.main_insights && executive_summary.main_insights.length > 0) {
                    html += `
                        <div class="row mb-4">
                            <div class="col-lg-6 mb-3 mb-lg-0">
                                <h6><i class="fas fa-lightbulb me-2"></i>Principais Insights</h6>
                                <ul class="list-group list-group-flush">
                    `;

                    executive_summary.main_insights.slice(0, 5).forEach(insight => {
                        html += `<li class="list-group-item">${insight}</li>`;
                    });

                    html += `
                                </ul>
                            </div>
                    `;
                }

                // Recomendações
                if (executive_summary.recommendations && executive_summary.recommendations.length > 0) {
                    html += `
                            <div class="col-lg-6">
                                <h6><i class="fas fa-tasks me-2"></i>Recomendações</h6>
                                <ul class="list-group list-group-flush">
                    `;

                    executive_summary.recommendations.slice(0, 5).forEach(recommendation => {
                        html += `<li class="list-group-item">${recommendation}</li>`;
                    });

                    html += `
                                </ul>
                            </div>
                        </div>
                    `;
                }

                // Ações prioritárias
                if (executive_summary.priority_actions && executive_summary.priority_actions.length > 0) {
                    html += `
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Ações Prioritárias</h6>
                                    <ul class="mb-0">
                    `;

                    executive_summary.priority_actions.forEach(action => {
                        html += `<li>${action}</li>`;
                    });

                    html += `
                                    </ul>
                                </div>
                            </div>
                        </div>
                    `;
                }

                document.getElementById('analysis-content').innerHTML = html;
            }

            // Retorna classe CSS para badge de performance
            function getPerformanceBadgeClass(performance) {
                switch(performance) {
                    case 'Excelente': return 'bg-success';
                    case 'Bom': return 'bg-primary';
                    case 'Regular': return 'bg-warning';
                    case 'Crítico': return 'bg-danger';
                    default: return 'bg-secondary';
                }
            }



            // Funções para loading overlay
            function showLoading() {
                document.getElementById('loadingOverlay').style.display = 'flex';
            }

            function hideLoading() {
                document.getElementById('loadingOverlay').style.display = 'none';
            }

            // Função para download de PDF
            async function downloadPDF(reportType) {
                try {
                    const startDate = document.getElementById('start_date').value;
                    const endDate = document.getElementById('end_date').value;
                    const selectedQueues = Array.from(document.getElementById('queues').selectedOptions).map(option => option.value).join(',');

                    const params = new URLSearchParams();
                    if (startDate) params.append('start_date', startDate);
                    if (endDate) params.append('end_date', endDate);
                    if (selectedQueues) params.append('queues', selectedQueues);

                    const url = `/api/generate-pdf/${reportType}?${params}`;

                    // Cria um link temporário para download
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `relatorio_${reportType}_${startDate}_${endDate}.pdf`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    console.log(`Download do PDF ${reportType} iniciado`);
                } catch (error) {
                    console.error('Erro ao baixar PDF:', error);
                    alert('Erro ao gerar PDF. Tente novamente.');
                }
            }

            // Variável global para armazenar dados do agente atual
            let currentAgentData = null;

            // Função para mostrar detalhes do agente
            async function showAgentDetails(agentName) {
                try {
                    console.log('Carregando detalhes do agente:', agentName);

                    // Usa os filtros corretos da interface
                    const startDate = document.getElementById('startDate') ? document.getElementById('startDate').value :
                                     new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];
                    const endDate = document.getElementById('endDate') ? document.getElementById('endDate').value :
                                   new Date().toISOString().split('T')[0];
                    const selectedQueues = '802,803'; // Sempre usa ambas as filas

                    const params = new URLSearchParams();
                    if (startDate) params.append('start_date', startDate);
                    if (endDate) params.append('end_date', endDate);
                    if (selectedQueues) params.append('queues', selectedQueues);

                    // Mostra o modal
                    const modal = new bootstrap.Modal(document.getElementById('agentModal'));
                    const agentDisplayName = agentName.replace('_', ' ');
                    document.getElementById('agentModalLabel').innerHTML = `<i class="fas fa-user me-2"></i>Detalhes do Agente: ${agentDisplayName}`;
                    modal.show();

                    // Busca dados do agente
                    const url = `/api/agent/${encodeURIComponent(agentName)}?${params}`;
                    console.log('URL da requisição:', url);

                    const response = await fetch(url);
                    const data = await response.json();

                    console.log('Resposta da API:', data);

                    if (data.success) {
                        currentAgentData = data;
                        displayAgentDetailsSimplified(data);
                    } else {
                        let errorHtml = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Erro ao carregar dados:</strong> ${data.message}
                                <br><small class="text-muted">Agente solicitado: ${agentName}</small>
                        `;

                        if (data.available_agents && data.available_agents.length > 0) {
                            errorHtml += `
                                <hr>
                                <strong>Agentes disponíveis (${data.total_agents} total):</strong>
                                <ul class="mb-0 mt-2">
                            `;
                            data.available_agents.forEach(agent => {
                                errorHtml += `<li>${agent.replace('_', ' ')}</li>`;
                            });
                            errorHtml += `</ul>`;
                        }

                        errorHtml += `</div>`;
                        document.getElementById('agentModalContent').innerHTML = errorHtml;
                    }
                } catch (error) {
                    console.error('Erro ao carregar detalhes do agente:', error);
                    document.getElementById('agentModalContent').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Erro de conexão:</strong> Não foi possível carregar os dados do agente.
                            <br><small class="text-muted">Verifique sua conexão e tente novamente.</small>
                        </div>
                    `;
                }
            }

            // Função para gerar insights inteligentes baseados nas métricas
            function generateSmartInsights(metrics, totalAvaliacoes, satisfeitosTotal, insatisfeitosTotal, percentualSatisfacao) {
                const insights = {
                    classification: {},
                    highlights: [],
                    insights: [],
                    recommendations: []
                };

                // Classificação geral
                if (percentualSatisfacao >= 95) {
                    insights.classification = {
                        level: 'Excepcional',
                        class: 'exceptional',
                        icon: 'fas fa-crown',
                        description: 'Performance extraordinária, referência para a equipe'
                    };
                } else if (percentualSatisfacao >= 90) {
                    insights.classification = {
                        level: 'Excelente',
                        class: 'excellent',
                        icon: 'fas fa-trophy',
                        description: 'Performance excelente, consistentemente acima da média'
                    };
                } else if (percentualSatisfacao >= 80) {
                    insights.classification = {
                        level: 'Muito Bom',
                        class: 'very-good',
                        icon: 'fas fa-medal',
                        description: 'Performance sólida com bons resultados'
                    };
                } else if (percentualSatisfacao >= 70) {
                    insights.classification = {
                        level: 'Bom',
                        class: 'good',
                        icon: 'fas fa-thumbs-up',
                        description: 'Performance adequada com espaço para crescimento'
                    };
                } else {
                    insights.classification = {
                        level: 'Precisa Melhorar',
                        class: 'needs-improvement',
                        icon: 'fas fa-exclamation-triangle',
                        description: 'Performance abaixo do esperado, requer atenção'
                    };
                }

                // Destaques baseados nas métricas
                if (totalAvaliacoes >= 50) {
                    insights.highlights.push({
                        type: 'positive',
                        icon: 'fas fa-chart-line',
                        text: `Alto volume de feedback: ${totalAvaliacoes} avaliações`
                    });
                } else if (totalAvaliacoes >= 20) {
                    insights.highlights.push({
                        type: 'neutral',
                        icon: 'fas fa-chart-bar',
                        text: `Volume moderado: ${totalAvaliacoes} avaliações`
                    });
                } else {
                    insights.highlights.push({
                        type: 'warning',
                        icon: 'fas fa-chart-line',
                        text: `Volume baixo: ${totalAvaliacoes} avaliações`
                    });
                }

                if (percentualSatisfacao >= 90) {
                    insights.highlights.push({
                        type: 'positive',
                        icon: 'fas fa-heart',
                        text: `Satisfação excepcional: ${percentualSatisfacao.toFixed(1)}%`
                    });
                }

                if (insatisfeitosTotal === 0 && totalAvaliacoes > 5) {
                    insights.highlights.push({
                        type: 'positive',
                        icon: 'fas fa-shield-alt',
                        text: 'Zero insatisfações no período'
                    });
                }

                // Insights detalhados por tipo de avaliação
                Object.entries(metrics.avaliacoes_por_tipo).forEach(([tipo, dados]) => {
                    const percentual = dados.percentual || 0;
                    const tipoNome = tipo === 'atendente' ? 'Atendimento' :
                                   tipo === 'chamada' ? 'Qualidade da Chamada' :
                                   tipo === 'empresa' ? 'Imagem da Empresa' : tipo;

                    if (percentual >= 95) {
                        insights.insights.push({
                            type: 'success',
                            icon: 'fas fa-star',
                            title: `${tipoNome} Excepcional`,
                            description: `${percentual.toFixed(1)}% de satisfação - Performance de referência`
                        });
                    } else if (percentual >= 85) {
                        insights.insights.push({
                            type: 'success',
                            icon: 'fas fa-check-circle',
                            title: `${tipoNome} Excelente`,
                            description: `${percentual.toFixed(1)}% de satisfação - Muito acima da média`
                        });
                    } else if (percentual < 70) {
                        insights.insights.push({
                            type: 'warning',
                            icon: 'fas fa-exclamation-circle',
                            title: `${tipoNome} Precisa Atenção`,
                            description: `${percentual.toFixed(1)}% de satisfação - Abaixo do esperado`
                        });
                    }
                });

                // Recomendações baseadas na performance
                if (percentualSatisfacao >= 95) {
                    insights.recommendations.push({
                        priority: 'high',
                        title: 'Mentor da Equipe',
                        description: 'Considere designar como mentor para treinar outros agentes'
                    });
                    insights.recommendations.push({
                        priority: 'medium',
                        title: 'Compartilhar Práticas',
                        description: 'Documente e compartilhe as melhores práticas com a equipe'
                    });
                } else if (percentualSatisfacao >= 85) {
                    insights.recommendations.push({
                        priority: 'medium',
                        title: 'Desenvolvimento Contínuo',
                        description: 'Mantenha o bom trabalho e busque oportunidades de crescimento'
                    });
                } else if (percentualSatisfacao < 70) {
                    insights.recommendations.push({
                        priority: 'high',
                        title: 'Treinamento Focado',
                        description: 'Implementar plano de desenvolvimento personalizado'
                    });
                    insights.recommendations.push({
                        priority: 'high',
                        title: 'Acompanhamento Próximo',
                        description: 'Supervisão mais frequente e feedback constante'
                    });
                }

                if (totalAvaliacoes < 10) {
                    insights.recommendations.push({
                        priority: 'medium',
                        title: 'Aumentar Participação',
                        description: 'Incentivar mais clientes a responderem às pesquisas'
                    });
                }

                return insights;
            }

            // Função simplificada para exibir detalhes do agente
            function displayAgentDetailsSimplified(data) {
                const { agent_name, period, metrics, details, analysis } = data;
                const agentDisplayName = agent_name.replace('_', ' ');

                console.log('Dados recebidos:', data); // Debug

                // Calcula métricas principais
                const totalAvaliacoes = metrics.total_avaliacoes || 0;
                const satisfeitosTotal = metrics.satisfeitos_total || 0;
                const insatisfeitosTotal = metrics.insatisfeitos_total || 0;
                const percentualSatisfacao = metrics.percentual_satisfacao || 0;

                // Determina classificação
                let classificacao = 'Regular';
                let classeCSS = 'warning';
                let icone = 'fas fa-meh';

                if (percentualSatisfacao >= 90) {
                    classificacao = 'Excepcional';
                    classeCSS = 'success';
                    icone = 'fas fa-star';
                } else if (percentualSatisfacao >= 80) {
                    classificacao = 'Excelente';
                    classeCSS = 'success';
                    icone = 'fas fa-thumbs-up';
                } else if (percentualSatisfacao >= 70) {
                    classificacao = 'Bom';
                    classeCSS = 'info';
                    icone = 'fas fa-smile';
                } else if (percentualSatisfacao >= 60) {
                    classificacao = 'Regular';
                    classeCSS = 'warning';
                    icone = 'fas fa-meh';
                } else {
                    classificacao = 'Precisa Melhorar';
                    classeCSS = 'danger';
                    icone = 'fas fa-frown';
                }

                const html = `
                    <!-- Header com período -->
                    <div class="alert alert-primary d-flex align-items-center mb-4">
                        <i class="fas fa-calendar-alt me-3 fa-2x"></i>
                        <div>
                            <strong>Período de Análise:</strong> ${period.start} a ${period.end}
                            <br><small class="text-muted">Dados consolidados do agente ${agentDisplayName}</small>
                        </div>
                    </div>

                    <!-- Resumo Principal -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="row g-3">
                                <div class="col-6">
                                    <div class="card text-center border-primary">
                                        <div class="card-body">
                                            <i class="fas fa-clipboard-list text-primary fa-2x mb-2"></i>
                                            <h4 class="text-primary mb-0">${totalAvaliacoes}</h4>
                                            <small class="text-muted">Total de Avaliações</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="card text-center border-${classeCSS}">
                                        <div class="card-body">
                                            <i class="${icone} text-${classeCSS} fa-2x mb-2"></i>
                                            <h4 class="text-${classeCSS} mb-0">${percentualSatisfacao.toFixed(1)}%</h4>
                                            <small class="text-muted">Satisfação Geral</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-${classeCSS} h-100">
                                <div class="card-body text-center d-flex flex-column justify-content-center">
                                    <i class="${icone} text-${classeCSS} fa-3x mb-2"></i>
                                    <h5 class="text-${classeCSS} mb-1">${classificacao}</h5>
                                    <small class="text-muted">Classificação Geral</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detalhamento por Tipo -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-muted mb-3">
                                <i class="fas fa-chart-bar me-2"></i>Detalhamento por Tipo de Avaliação
                            </h6>
                            <div class="row g-3">
                `;

                let finalHtml = html + generateTypeDetails(metrics.avaliacoes_por_tipo || details) + `
                            </div>
                        </div>
                    </div>

                    <!-- Resumo de Performance -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-smile text-success fa-2x mb-2"></i>
                                    <h4 class="text-success mb-0">${satisfeitosTotal}</h4>
                                    <small class="text-muted">Clientes Satisfeitos</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <i class="fas fa-frown text-danger fa-2x mb-2"></i>
                                    <h4 class="text-danger mb-0">${insatisfeitosTotal}</h4>
                                    <small class="text-muted">Clientes Insatisfeitos</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Adiciona insights se disponíveis
                if (analysis && analysis.insights && analysis.insights.length > 0) {
                    finalHtml += `
                        <div class="row">
                            <div class="col-12">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <i class="fas fa-lightbulb me-2"></i>
                                        <strong>Insights e Análises</strong>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <h6 class="text-muted mb-2">💡 Principais Insights:</h6>
                                                <ul class="list-unstyled">
                    `;

                    analysis.insights.forEach(insight => {
                        finalHtml += `<li class="mb-1"><i class="fas fa-check-circle text-success me-2"></i>${insight}</li>`;
                    });

                    finalHtml += `
                                                </ul>
                                            </div>
                                            <div class="col-md-4">
                                                <h6 class="text-muted mb-2">🎯 Sugestões:</h6>
                                                <ul class="list-unstyled">
                    `;

                    if (analysis.suggestions && analysis.suggestions.length > 0) {
                        analysis.suggestions.forEach(suggestion => {
                            finalHtml += `<li class="mb-1"><i class="fas fa-arrow-right text-primary me-2"></i>${suggestion}</li>`;
                        });
                    } else {
                        finalHtml += `<li class="text-muted">Nenhuma sugestão específica</li>`;
                    }

                    finalHtml += `
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }

                document.getElementById('agentModalContent').innerHTML = finalHtml;
            }

            // Função auxiliar para gerar detalhes por tipo
            function generateTypeDetails(details) {
                console.log('Detalhes recebidos:', details); // Debug

                if (!details || Object.keys(details).length === 0) {
                    return '<div class="col-12"><div class="alert alert-info">Nenhum detalhe específico disponível para este período.</div></div>';
                }

                let html = '';
                const tipos = {
                    'atendente': { nome: 'Avaliação do Atendente', icone: 'fas fa-user-tie', cor: 'primary' },
                    'chamada': { nome: 'Qualidade da Chamada', icone: 'fas fa-phone', cor: 'info' },
                    'empresa': { nome: 'Imagem da Empresa', icone: 'fas fa-building', cor: 'secondary' }
                };

                // Processa cada tipo de avaliação
                Object.entries(tipos).forEach(([tipoKey, config]) => {
                    let dados = details[tipoKey];
                    let positivos = 0;
                    let total = 0;
                    let percentual = 0;

                    if (dados) {
                        // Estrutura específica para cada tipo
                        if (tipoKey === 'atendente') {
                            positivos = dados.satisfeitos || 0;
                            total = dados.total || 0;
                            percentual = dados.percentual || 0;
                        } else if (tipoKey === 'chamada') {
                            positivos = dados.sim || 0;
                            total = dados.total || 0;
                            percentual = dados.percentual || 0;
                        } else if (tipoKey === 'empresa') {
                            positivos = dados.satisfatorias || 0; // Campo correto para empresa
                            total = dados.total || 0;
                            percentual = dados.percentual || 0;
                        }

                        console.log(`${tipoKey}:`, { positivos, total, percentual, dados }); // Debug
                    }

                    // Determina cor baseada na performance
                    let corFinal = config.cor;
                    if (percentual >= 90) corFinal = 'success';
                    else if (percentual >= 80) corFinal = 'info';
                    else if (percentual >= 70) corFinal = 'warning';
                    else if (percentual > 0) corFinal = 'danger';

                    html += `
                        <div class="col-md-4">
                            <div class="card border-${corFinal} h-100">
                                <div class="card-header bg-${corFinal} text-white text-center">
                                    <i class="${config.icone} me-2"></i>
                                    <strong>${config.nome}</strong>
                                </div>
                                <div class="card-body text-center">
                                    <h4 class="text-${corFinal} mb-2">${percentual.toFixed(1)}%</h4>
                                    <div class="progress mb-2" style="height: 10px;">
                                        <div class="progress-bar bg-${corFinal}" style="width: ${percentual}%"></div>
                                    </div>
                                    <small class="text-muted">${positivos}/${total} avaliações</small>
                                    ${total === 0 ? '<br><small class="text-warning">Sem dados no período</small>' : ''}
                                </div>
                            </div>
                        </div>
                    `;
                });

                return html;
            }

            // Função original mantida para compatibilidade (caso necessário)
            function displayAgentDetails(data) {
                const { agent_name, period, metrics, details, analysis } = data;

                // Calcula métricas adicionais
                const totalAvaliacoes = metrics.total_avaliacoes || 0;
                const satisfeitosTotal = metrics.satisfeitos_total || 0;
                const insatisfeitosTotal = metrics.insatisfeitos_total || 0;
                const percentualSatisfacao = totalAvaliacoes > 0 ? (satisfeitosTotal / totalAvaliacoes * 100) : 0;

                // Calcula taxa de participação estimada (baseado em volume típico)
                const estimatedCalls = Math.round(totalAvaliacoes * 1.4); // Estima que 70% respondem
                const participationRate = estimatedCalls > 0 ? (totalAvaliacoes / estimatedCalls * 100) : 0;

                let html = `
                    <!-- Header com Período -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="alert alert-primary d-flex align-items-center">
                                <i class="fas fa-calendar-alt me-3 fa-2x"></i>
                                <div>
                                    <strong>Período de Análise:</strong> ${new Date(period.start).toLocaleDateString('pt-BR')} a ${new Date(period.end).toLocaleDateString('pt-BR')}
                                    <br><small class="text-muted">Dados consolidados de todas as avaliações do agente</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Resumo Executivo -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-gradient-primary text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-pie me-2"></i>Resumo Executivo
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="row">
                                                <div class="col-6 col-lg-3 mb-3">
                                                    <div class="metric-card-enhanced">
                                                        <div class="metric-icon bg-primary">
                                                            <i class="fas fa-clipboard-list"></i>
                                                        </div>
                                                        <div class="metric-content">
                                                            <h3 class="metric-value">${totalAvaliacoes}</h3>
                                                            <p class="metric-label">Pesquisas Respondidas</p>
                                                            <small class="text-muted">Total no período</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-6 col-lg-3 mb-3">
                                                    <div class="metric-card-enhanced">
                                                        <div class="metric-icon bg-success">
                                                            <i class="fas fa-smile"></i>
                                                        </div>
                                                        <div class="metric-content">
                                                            <h3 class="metric-value">${satisfeitosTotal}</h3>
                                                            <p class="metric-label">Clientes Satisfeitos</p>
                                                            <small class="text-muted">${totalAvaliacoes > 0 ? ((satisfeitosTotal/totalAvaliacoes)*100).toFixed(1) : 0}% do total</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-6 col-lg-3 mb-3">
                                                    <div class="metric-card-enhanced">
                                                        <div class="metric-icon bg-danger">
                                                            <i class="fas fa-frown"></i>
                                                        </div>
                                                        <div class="metric-content">
                                                            <h3 class="metric-value">${insatisfeitosTotal}</h3>
                                                            <p class="metric-label">Clientes Insatisfeitos</p>
                                                            <small class="text-muted">${totalAvaliacoes > 0 ? ((insatisfeitosTotal/totalAvaliacoes)*100).toFixed(1) : 0}% do total</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-6 col-lg-3 mb-3">
                                                    <div class="metric-card-enhanced">
                                                        <div class="metric-icon bg-info">
                                                            <i class="fas fa-percentage"></i>
                                                        </div>
                                                        <div class="metric-content">
                                                            <h3 class="metric-value">${participationRate.toFixed(1)}%</h3>
                                                            <p class="metric-label">Taxa Participação</p>
                                                            <small class="text-muted">Estimativa de resposta</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="performance-summary">
                                                <div class="performance-circle ${percentualSatisfacao >= 90 ? 'excellent' : percentualSatisfacao >= 80 ? 'good' : percentualSatisfacao >= 70 ? 'average' : 'poor'}">
                                                    <div class="performance-percentage">${percentualSatisfacao.toFixed(1)}%</div>
                                                    <div class="performance-label">Satisfação Geral</div>
                                                </div>
                                                <div class="performance-status mt-3 text-center">
                                                    <span class="badge ${percentualSatisfacao >= 90 ? 'bg-success' : percentualSatisfacao >= 80 ? 'bg-info' : percentualSatisfacao >= 70 ? 'bg-warning' : 'bg-danger'} fs-6">
                                                        ${percentualSatisfacao >= 90 ? 'Excelente' : percentualSatisfacao >= 80 ? 'Muito Bom' : percentualSatisfacao >= 70 ? 'Bom' : 'Precisa Melhorar'}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Detalhamento por Tipo de Avaliação -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-gradient-secondary text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-list-alt me-2"></i>Detalhamento por Tipo de Avaliação
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                `;

                for (const [tipo, dados] of Object.entries(metrics.avaliacoes_por_tipo)) {
                    const tipoInfo = {
                        'atendente': {
                            nome: 'Avaliação do Atendente',
                            icon: 'fas fa-user-tie',
                            color: 'primary',
                            description: 'Como o cliente avaliou o atendimento pessoal'
                        },
                        'chamada': {
                            nome: 'Qualidade da Chamada',
                            icon: 'fas fa-phone',
                            color: 'success',
                            description: 'Avaliação técnica da ligação (áudio, conexão)'
                        },
                        'empresa': {
                            nome: 'Imagem da Empresa',
                            icon: 'fas fa-building',
                            color: 'info',
                            description: 'Percepção geral sobre a empresa'
                        }
                    };

                    const info = tipoInfo[tipo] || {
                        nome: tipo,
                        icon: 'fas fa-star',
                        color: 'secondary',
                        description: 'Avaliação geral'
                    };

                    // Define valores baseado no tipo de avaliação
                    let positivos, negativos;
                    if (tipo === 'atendente') {
                        positivos = dados.satisfeitos || 0;
                        negativos = dados.insatisfeitos || 0;
                    } else if (tipo === 'chamada') {
                        positivos = dados.sim || 0;
                        negativos = dados.nao || 0;
                    } else if (tipo === 'empresa') {
                        positivos = (dados.nota4 || 0) + (dados.nota5 || 0);
                        negativos = (dados.nota1 || 0) + (dados.nota2 || 0) + (dados.nota3 || 0);
                    } else {
                        positivos = dados.satisfeitos || dados.sim || dados.satisfatorias || 0;
                        negativos = dados.insatisfeitos || dados.nao || ((dados.total || 0) - positivos) || 0;
                    }

                    const percentual = dados.percentual || 0;
                    const badgeClass = percentual >= 90 ? 'bg-success' : percentual >= 80 ? 'bg-info' : percentual >= 70 ? 'bg-warning' : 'bg-danger';

                    html += `
                        <div class="col-md-4 mb-3">
                            <div class="evaluation-type-card">
                                <div class="card-header-custom bg-${info.color}">
                                    <div class="d-flex align-items-center">
                                        <i class="${info.icon} me-2"></i>
                                        <div>
                                            <h6 class="mb-0 text-white">${info.nome}</h6>
                                            <small class="text-white-50">${info.description}</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body-custom">
                                    <div class="evaluation-metrics">
                                        <div class="metric-row-small">
                                            <div class="metric-item-small">
                                                <span class="metric-label-small">Total</span>
                                                <span class="metric-value-small">${dados.total || 0}</span>
                                            </div>
                                            <div class="metric-item-small">
                                                <span class="metric-label-small">Positivas</span>
                                                <span class="metric-value-small text-success">${positivos}</span>
                                            </div>
                                            <div class="metric-item-small">
                                                <span class="metric-label-small">Negativas</span>
                                                <span class="metric-value-small text-danger">${negativos}</span>
                                            </div>
                                        </div>
                                        <div class="performance-bar-container mt-3">
                                            <div class="d-flex justify-content-between align-items-center mb-1">
                                                <small class="text-muted">Performance</small>
                                                <span class="badge ${badgeClass}">${percentual.toFixed(1)}%</span>
                                            </div>
                                            <div class="progress progress-enhanced-modal">
                                                <div class="progress-bar ${badgeClass.replace('bg-', '')}"
                                                     style="width: ${percentual}%">
                                                    <span class="progress-text-modal">${percentual.toFixed(1)}%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }

                html += `
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Gera análise inteligente baseada nas métricas
                const smartInsights = generateSmartInsights(metrics, totalAvaliacoes, satisfeitosTotal, insatisfeitosTotal, percentualSatisfacao);

                html += `
                    <!-- Análise Inteligente -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-gradient-dark text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-brain me-2"></i>Análise Inteligente de Performance
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="performance-classification">
                                                <h6 class="text-muted mb-3">
                                                    <i class="fas fa-award me-2"></i>Classificação Geral
                                                </h6>
                                                <div class="classification-badge ${smartInsights.classification.class}">
                                                    <div class="classification-icon">
                                                        <i class="${smartInsights.classification.icon}"></i>
                                                    </div>
                                                    <div class="classification-text">
                                                        <h4>${smartInsights.classification.level}</h4>
                                                        <p>${smartInsights.classification.description}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="performance-highlights">
                                                <h6 class="text-muted mb-3">
                                                    <i class="fas fa-star me-2"></i>Destaques
                                                </h6>
                                                <div class="highlights-list">
                                                    ${smartInsights.highlights.map(highlight => `
                                                        <div class="highlight-item ${highlight.type}">
                                                            <i class="${highlight.icon} me-2"></i>
                                                            <span>${highlight.text}</span>
                                                        </div>
                                                    `).join('')}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <hr class="my-4">

                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="text-muted mb-3">
                                                <i class="fas fa-lightbulb me-2"></i>Insights Baseados em Dados
                                            </h6>
                                            <div class="insights-list">
                                                ${smartInsights.insights.map(insight => `
                                                    <div class="insight-item">
                                                        <div class="insight-icon ${insight.type}">
                                                            <i class="${insight.icon}"></i>
                                                        </div>
                                                        <div class="insight-content">
                                                            <strong>${insight.title}</strong>
                                                            <p class="mb-0">${insight.description}</p>
                                                        </div>
                                                    </div>
                                                `).join('')}
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="text-muted mb-3">
                                                <i class="fas fa-tasks me-2"></i>Recomendações Estratégicas
                                            </h6>
                                            <div class="recommendations-list">
                                                ${smartInsights.recommendations.map(rec => `
                                                    <div class="recommendation-item">
                                                        <div class="recommendation-priority ${rec.priority}">
                                                            ${rec.priority === 'high' ? 'Alta' : rec.priority === 'medium' ? 'Média' : 'Baixa'}
                                                        </div>
                                                        <div class="recommendation-content">
                                                            <strong>${rec.title}</strong>
                                                            <p class="mb-0">${rec.description}</p>
                                                        </div>
                                                    </div>
                                                `).join('')}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.getElementById('agentModalContent').innerHTML = html;
            }

            // Função para download do PDF do agente
            async function downloadAgentPDF() {
                if (!currentAgentData) {
                    alert('Nenhum agente selecionado');
                    return;
                }

                try {
                    const startDate = document.getElementById('start_date').value;
                    const endDate = document.getElementById('end_date').value;
                    const selectedQueues = Array.from(document.getElementById('queues').selectedOptions).map(option => option.value).join(',');

                    const params = new URLSearchParams();
                    if (startDate) params.append('start_date', startDate);
                    if (endDate) params.append('end_date', endDate);
                    if (selectedQueues) params.append('queues', selectedQueues);

                    const url = `/api/generate-pdf/agents?${params}`;

                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `relatorio_agente_${currentAgentData.agent_name}_${startDate}_${endDate}.pdf`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    console.log(`Download do PDF do agente ${currentAgentData.agent_name} iniciado`);
                } catch (error) {
                    console.error('Erro ao baixar PDF do agente:', error);
                    alert('Erro ao gerar PDF. Tente novamente.');
                }
            }

            // Inicialização
            document.addEventListener('DOMContentLoaded', function() {
                // Define período padrão (ontem - 28/07/2025 tem dados)
                setQuickPeriod('yesterday');

                // Carrega status do sistema
                loadSystemStatus();

                // Auto-refresh a cada 2 minutos
                setInterval(() => {
                    loadAllData();
                    loadSystemStatus();
                }, 120000);
            });
        </script>
    </body>
    </html>
    """)


@app.get("/health")
async def health_check():
    """Endpoint de health check"""
    try:
        # Testa conexão com 3CX
        is_connected = await test_3cx_connection()

        return {
            "status": "healthy" if is_connected else "degraded",
            "timestamp": datetime.now().isoformat(),
            "threecx_connection": is_connected,
            "version": "2.1.0"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "version": "2.1.0"
        }


@app.get("/test-3cx")
async def test_3cx_endpoint():
    """Testa conexão com 3CX"""
    try:
        is_connected = await test_3cx_connection()

        if is_connected:
            return {"success": True, "message": "Conexão com 3CX estabelecida com sucesso!"}
        else:
            return {"success": False, "error": "Falha na conexão com 3CX"}

    except Exception as e:
        return {"success": False, "error": str(e)}


@app.get("/test-timing")
async def test_timing_endpoint():
    """Endpoint de teste para timing"""
    return {"success": True, "message": "Endpoint de timing funcionando"}


@app.get("/api/distribution")
async def get_distribution_api(
    start_date: str = None,
    end_date: str = None,
    queues: str = None
):
    """API de relatório de distribuição"""
    try:
        # Usa datas fornecidas ou padrão (hoje)
        if not start_date or not end_date:
            today = date.today()
            start_date = today.isoformat()
            end_date = today.isoformat()

        # Processa filas
        if queues:
            queue_list = [int(q.strip()) for q in queues.split(",")]
        else:
            queue_list = AVAILABLE_QUEUES

        # Chama API do 3CX com parâmetros
        data = await call_3cx_api_with_params("Distribuição", start_date, end_date, queue_list)

        return {
            "success": True,
            "message": f"Relatório de distribuição gerado para {start_date} a {end_date}",
            "data": data,
            "period": {"start": start_date, "end": end_date},
            "queues": queue_list
        }
    except Exception as e:
        return {"success": False, "message": f"Erro: {str(e)}"}


@app.get("/api/satisfaction")
async def get_satisfaction_api(
    start_date: str = None,
    end_date: str = None,
    queues: str = None
):
    """API de pesquisa de satisfação"""
    try:
        # Usa datas fornecidas ou padrão (hoje)
        if not start_date or not end_date:
            today = date.today()
            start_date = today.isoformat()
            end_date = today.isoformat()

        # Processa filas
        if queues:
            queue_list = [int(q.strip()) for q in queues.split(",")]
        else:
            queue_list = AVAILABLE_QUEUES

        # Chama API do 3CX com parâmetros
        data = await call_3cx_api_with_params("Pesquisa Satisfação", start_date, end_date, queue_list)

        return {
            "success": True,
            "message": f"Pesquisa de satisfação gerada para {start_date} a {end_date}",
            "data": data,
            "period": {"start": start_date, "end": end_date},
            "queues": queue_list
        }
    except Exception as e:
        return {"success": False, "message": f"Erro: {str(e)}"}


@app.get("/api/timing")
async def timing_api(start_date: str = None, end_date: str = None, queues: str = None):
    """API de métricas de timing avançadas"""
    try:
        # Usa datas fornecidas ou padrão (hoje)
        if not start_date or not end_date:
            today = date.today()
            start_date = today.isoformat()
            end_date = today.isoformat()

        # Processa filas
        if queues:
            queue_list = [int(q.strip()) for q in queues.split(",")]
        else:
            queue_list = AVAILABLE_QUEUES

        # Obtém dados de distribuição
        distribution_data = await call_3cx_api_with_params("Distribuição", start_date, end_date, queue_list)

        # Importa e usa o calculador avançado
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'services'))
        from advanced_metrics import advanced_metrics

        # Calcula métricas avançadas de timing
        timing_metrics = advanced_metrics._calculate_timing_metrics(distribution_data)

        # Adiciona métricas básicas para compatibilidade
        basic_metrics = {
            "asa_medio": timing_metrics.get("asa_seconds", 0),
            "asa_formatado": timing_metrics.get("asa_formatted", "00:00:00"),
            "aht_medio": timing_metrics.get("aht_seconds", 0),
            "aht_formatado": timing_metrics.get("aht_formatted", "00:00:00"),
            "service_level": timing_metrics.get("service_level_percentage", 0),
            "taxa_abandono": timing_metrics.get("abandonment_rate", 0),
            "taxa_atendimento": timing_metrics.get("answer_rate", 0),
            "tempo_espera_max": timing_metrics.get("peak_wait_time", "00:00:00"),
            "tempo_espera_min": timing_metrics.get("min_wait_time", "00:00:00"),
            "score_eficiencia": timing_metrics.get("efficiency_score", 0)
        }

        return {
            "success": True,
            "message": f"Métricas de timing calculadas para {start_date} a {end_date}",
            "data": basic_metrics,
            "advanced_metrics": timing_metrics,
            "period": {"start": start_date, "end": end_date},
            "queues": queue_list
        }
    except Exception as e:
        logger.error(f"Erro ao calcular métricas de timing: {str(e)}")
        return {"success": False, "message": f"Erro: {str(e)}"}

@app.get("/api/advanced-metrics")
async def get_advanced_metrics(start_date: str = None, end_date: str = None, queues: str = None):
    """API de métricas avançadas completas"""
    try:
        # Usa datas fornecidas ou padrão (hoje)
        if not start_date or not end_date:
            today = date.today()
            start_date = today.isoformat()
            end_date = today.isoformat()

        # Processa filas
        if queues:
            queue_list = [int(q.strip()) for q in queues.split(",")]
        else:
            queue_list = AVAILABLE_QUEUES

        # Obtém dados de distribuição e satisfação
        distribution_data = await call_3cx_api_with_params("Distribuição", start_date, end_date, queue_list)
        satisfaction_data = await call_3cx_api_with_params("Pesquisa Satisfação", start_date, end_date, queue_list)

        # Importa e usa o calculador avançado
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'services'))
        from advanced_metrics import advanced_metrics

        # Calcula métricas abrangentes
        comprehensive_metrics = advanced_metrics.calculate_comprehensive_metrics(
            distribution_data, satisfaction_data
        )

        return {
            "success": True,
            "message": f"Métricas avançadas calculadas para {start_date} a {end_date}",
            "data": comprehensive_metrics,
            "period": {"start": start_date, "end": end_date},
            "queues": queue_list,
            "generated_at": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Erro ao calcular métricas avançadas: {str(e)}")
        return {"success": False, "message": f"Erro: {str(e)}"}

@app.get("/api/performance-dashboard")
async def get_performance_dashboard(start_date: str = None, end_date: str = None, queues: str = None):
    """API para dashboard de performance com métricas intuitivas"""
    try:
        # Usa datas fornecidas ou padrão (hoje)
        if not start_date or not end_date:
            today = date.today()
            start_date = today.isoformat()
            end_date = today.isoformat()

        # Processa filas
        if queues:
            queue_list = [int(q.strip()) for q in queues.split(",")]
        else:
            queue_list = AVAILABLE_QUEUES

        # Obtém dados
        distribution_data = await call_3cx_api_with_params("Distribuição", start_date, end_date, queue_list)
        satisfaction_data = await call_3cx_api_with_params("Pesquisa Satisfação", start_date, end_date, queue_list)

        # Importa calculador avançado
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'services'))
        from advanced_metrics import advanced_metrics

        # Calcula métricas
        comprehensive_metrics = advanced_metrics.calculate_comprehensive_metrics(
            distribution_data, satisfaction_data
        )

        # Formata para dashboard
        dashboard_data = {
            "kpis": {
                "service_level": {
                    "value": comprehensive_metrics.get("timing_metrics", {}).get("service_level_percentage", 0),
                    "target": 80,
                    "status": "good" if comprehensive_metrics.get("timing_metrics", {}).get("service_level_percentage", 0) >= 80 else "warning",
                    "trend": "stable"
                },
                "satisfaction": {
                    "value": comprehensive_metrics.get("satisfaction_metrics", {}).get("overall_satisfaction", 0),
                    "target": 85,
                    "status": "good" if comprehensive_metrics.get("satisfaction_metrics", {}).get("overall_satisfaction", 0) >= 85 else "warning",
                    "trend": "stable"
                },
                "efficiency": {
                    "value": comprehensive_metrics.get("timing_metrics", {}).get("efficiency_score", 0),
                    "target": 75,
                    "status": "good" if comprehensive_metrics.get("timing_metrics", {}).get("efficiency_score", 0) >= 75 else "warning",
                    "trend": "stable"
                },
                "answer_rate": {
                    "value": comprehensive_metrics.get("timing_metrics", {}).get("answer_rate", 0),
                    "target": 95,
                    "status": "good" if comprehensive_metrics.get("timing_metrics", {}).get("answer_rate", 0) >= 95 else "warning",
                    "trend": "stable"
                }
            },
            "insights": comprehensive_metrics.get("operational_insights", []),
            "performance_classification": comprehensive_metrics.get("performance_classification", {}),
            "agent_performance": {
                "top_performers": comprehensive_metrics.get("satisfaction_metrics", {}).get("top_performers", []),
                "critical_agents": comprehensive_metrics.get("satisfaction_metrics", {}).get("critical_agents", []),
                "improvement_areas": comprehensive_metrics.get("satisfaction_metrics", {}).get("improvement_areas", [])
            }
        }

        return {
            "success": True,
            "message": f"Dashboard de performance gerado para {start_date} a {end_date}",
            "data": dashboard_data,
            "period": {"start": start_date, "end": end_date},
            "queues": queue_list,
            "generated_at": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Erro ao gerar dashboard de performance: {str(e)}")
        return {"success": False, "message": f"Erro: {str(e)}"}



@app.get("/api/agents")
async def get_agents_api(
    start_date: str = None,
    end_date: str = None,
    queues: str = None
):
    """API de dados dos agentes"""
    try:
        # Usa datas fornecidas ou padrão (hoje)
        if not start_date or not end_date:
            today = date.today()
            start_date = today.isoformat()
            end_date = today.isoformat()

        # Processa filas
        if queues:
            queue_list = [int(q.strip()) for q in queues.split(",")]
        else:
            queue_list = AVAILABLE_QUEUES

        # Obtém dados de satisfação que contém informações dos agentes
        satisfaction_data = await call_3cx_api_with_params("Pesquisa Satisfação", start_date, end_date, queue_list)

        if satisfaction_data and 'Pesquisa Satisfação' in satisfaction_data:
            agents_data = satisfaction_data['Pesquisa Satisfação']['Pesquisa por Agente']['Pesquisa Amvox']['Av-1-Avalia Atendente']

            return {
                "success": True,
                "message": f"Dados dos agentes obtidos para {start_date} a {end_date}",
                "data": agents_data,
                "period": {"start": start_date, "end": end_date},
                "queues": queue_list
            }
        else:
            return {"success": False, "message": "Dados dos agentes não disponíveis"}

    except Exception as e:
        return {"success": False, "message": f"Erro: {str(e)}"}


@app.get("/api/hourly")
async def get_hourly_api():
    """API de dados por hora"""
    try:
        # Obtém dados de distribuição que contém informações por hora
        distribution_data = await call_3cx_api("Distribuição")

        if distribution_data and 'relatorio de distribuição' in distribution_data:
            hourly_data = distribution_data['relatorio de distribuição']['Chamadas por hora']

            return {
                "success": True,
                "message": "Dados por hora obtidos com sucesso",
                "data": hourly_data
            }
        else:
            return {"success": False, "message": "Dados por hora não disponíveis"}

    except Exception as e:
        return {"success": False, "message": f"Erro: {str(e)}"}


@app.get("/api/call-details")
async def get_call_details_api():
    """API de detalhes das chamadas"""
    try:
        # Obtém dados de distribuição que contém detalhes das chamadas
        distribution_data = await call_3cx_api("Distribuição")

        if distribution_data and 'relatorio de distribuição' in distribution_data:
            call_details = distribution_data['relatorio de distribuição']['Detalhes da Distribuição']

            return {
                "success": True,
                "message": "Detalhes das chamadas obtidos com sucesso",
                "data": call_details[-100:]  # Últimas 100 chamadas
            }
        else:
            return {"success": False, "message": "Detalhes das chamadas não disponíveis"}

    except Exception as e:
        return {"success": False, "message": f"Erro: {str(e)}"}


async def test_3cx_connection():
    """Testa conexão com a API do 3CX"""
    try:
        today = date.today()

        payload = {
            "user": THREECX_USER,
            "password": THREECX_PASSWORD,
            "data_inicial": today.isoformat(),
            "data_final": today.isoformat(),
            "filas": AVAILABLE_QUEUES,
            "relatorio": "Distribuição"
        }

        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(THREECX_API_URL, json=payload)
            return response.status_code == 200

    except Exception as e:
        logger.error(f"Erro no teste de conexão: {str(e)}")
        return False


async def call_3cx_api(report_type):
    """Chama a API do 3CX com data atual"""
    today = date.today()
    return await call_3cx_api_with_params(report_type, today.isoformat(), today.isoformat(), AVAILABLE_QUEUES)


async def call_3cx_api_with_params(report_type, start_date, end_date, queues):
    """Chama a API do 3CX com parâmetros específicos"""
    try:
        payload = {
            "user": THREECX_USER,
            "password": THREECX_PASSWORD,
            "data_inicial": start_date,
            "data_final": end_date,
            "filas": queues,
            "relatorio": report_type
        }

        logger.info(f"Chamando 3CX API: {report_type} de {start_date} a {end_date} para filas {queues}")

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(THREECX_API_URL, json=payload)
            response.raise_for_status()
            return response.json()

    except Exception as e:
        logger.error(f"Erro na API 3CX: {str(e)}")
        raise Exception(f"Erro na comunicação com 3CX: {str(e)}")


@app.get("/api/generate-pdf/{report_type}")
async def generate_pdf_endpoint(
    report_type: str,
    start_date: str = None,
    end_date: str = None,
    queues: str = None
):
    """Endpoint para gerar relatórios em PDF"""
    try:
        # Usa datas fornecidas ou padrão (hoje)
        if not start_date or not end_date:
            today = date.today()
            start_date = today.isoformat()
            end_date = today.isoformat()

        # Processa filas
        if queues:
            queue_list = [int(q.strip()) for q in queues.split(",")]
        else:
            queue_list = AVAILABLE_QUEUES

        # Obtém dados baseado no tipo de relatório
        if report_type == "distribution":
            data = await call_3cx_api_with_params("Distribuição", start_date, end_date, queue_list)
            report_data = {
                "success": True,
                "data": data,
                "period": {"start": start_date, "end": end_date},
                "queues": queue_list
            }
        elif report_type == "satisfaction":
            data = await call_3cx_api_with_params("Pesquisa Satisfação", start_date, end_date, queue_list)
            report_data = {
                "success": True,
                "data": data,
                "period": {"start": start_date, "end": end_date},
                "queues": queue_list
            }
        elif report_type == "agents":
            satisfaction_data = await call_3cx_api_with_params("Pesquisa Satisfação", start_date, end_date, queue_list)
            if satisfaction_data and 'Pesquisa Satisfação' in satisfaction_data:
                agents_data = satisfaction_data['Pesquisa Satisfação']['Pesquisa por Agente']['Pesquisa Amvox']['Av-1-Avalia Atendente']
                report_data = {
                    "success": True,
                    "data": agents_data,
                    "period": {"start": start_date, "end": end_date},
                    "queues": queue_list
                }
            else:
                return {"success": False, "message": "Dados dos agentes não disponíveis"}
        else:
            return {"success": False, "message": "Tipo de relatório inválido"}

        # Gera o PDF
        period_info = {"start": start_date, "end": end_date}
        pdf_buffer = generate_pdf_report(report_data, report_type, period_info)

        # Retorna o PDF como resposta
        from fastapi.responses import StreamingResponse

        filename = f"relatorio_{report_type}_{start_date}_{end_date}.pdf"

        return StreamingResponse(
            io.BytesIO(pdf_buffer.read()),
            media_type="application/pdf",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        logger.error(f"Erro ao gerar PDF: {str(e)}")
        return {"success": False, "message": f"Erro ao gerar PDF: {str(e)}"}


@app.get("/api/agent/{agent_name}")
async def get_agent_details(
    agent_name: str,
    start_date: str = None,
    end_date: str = None,
    queues: str = None
):
    """Endpoint para obter detalhes de um agente específico"""
    try:
        # Usa datas fornecidas ou padrão (hoje)
        if not start_date or not end_date:
            today = date.today()
            start_date = today.isoformat()
            end_date = today.isoformat()

        # Processa filas
        if queues:
            queue_list = [int(q.strip()) for q in queues.split(",")]
        else:
            queue_list = AVAILABLE_QUEUES

        # Obtém dados de satisfação
        satisfaction_data = await call_3cx_api_with_params("Pesquisa Satisfação", start_date, end_date, queue_list)

        if not satisfaction_data or 'Pesquisa Satisfação' not in satisfaction_data:
            return {"success": False, "message": "Dados de satisfação não disponíveis"}

        # Busca dados do agente específico
        agents_data = satisfaction_data['Pesquisa Satisfação']['Pesquisa por Agente']['Pesquisa Amvox']

        agent_details = {}

        # Av-1: Avalia Atendente
        if 'Av-1-Avalia Atendente' in agents_data:
            for agent in agents_data['Av-1-Avalia Atendente']:
                if isinstance(agent, dict) and agent.get('Agente') == agent_name:
                    agent_details['atendente'] = agent
                    break

        # Av-2: Avalia Chamada
        if 'Av-2-Avalia Chamada' in agents_data:
            for agent in agents_data['Av-2-Avalia Chamada']:
                if isinstance(agent, dict) and agent.get('Agente') == agent_name:
                    agent_details['chamada'] = agent
                    break

        # Av-3: Avalia Empresa
        if 'Av-3-Avalia Empresa' in agents_data:
            for agent in agents_data['Av-3-Avalia Empresa']:
                if isinstance(agent, dict) and agent.get('Agente') == agent_name:
                    agent_details['empresa'] = agent
                    break

        if not agent_details:
            # Lista agentes disponíveis para debug
            available_agents = []
            if 'Av-1-Avalia Atendente' in agents_data:
                available_agents = [agent.get('Agente', 'N/A') for agent in agents_data['Av-1-Avalia Atendente'] if isinstance(agent, dict)]

            return {
                "success": False,
                "message": f"Agente '{agent_name}' não encontrado",
                "available_agents": available_agents[:10],  # Limita a 10 para não sobrecarregar
                "total_agents": len(available_agents)
            }

        # Calcula métricas consolidadas
        consolidated_metrics = calculate_agent_metrics(agent_details)

        # Gera análise do agente
        agent_analysis = analyze_agent_metrics({"metrics": consolidated_metrics}, agent_name)

        return {
            "success": True,
            "agent_name": agent_name,
            "period": {"start": start_date, "end": end_date},
            "queues": queue_list,
            "details": agent_details,
            "metrics": consolidated_metrics,
            "analysis": agent_analysis
        }

    except Exception as e:
        logger.error(f"Erro ao obter detalhes do agente: {str(e)}")
        return {"success": False, "message": f"Erro: {str(e)}"}


def calculate_agent_metrics(agent_details):
    """Calcula métricas consolidadas do agente"""
    metrics = {
        "total_avaliacoes": 0,
        "satisfeitos_total": 0,
        "insatisfeitos_total": 0,
        "percentual_satisfacao": 0,
        "avaliacoes_por_tipo": {}
    }

    for tipo, dados in agent_details.items():
        if isinstance(dados, dict):
            # Para Av-1 (Atendente)
            if tipo == 'atendente':
                satisfeitos = dados.get('Satisfeito', 0)
                insatisfeitos = dados.get('Insatisfeito', 0)
                total = dados.get('Avaliadas', 0)
                sem_avaliacao = dados.get('Sem Avaliacao', 0)

                metrics["total_avaliacoes"] += total
                metrics["satisfeitos_total"] += satisfeitos
                metrics["insatisfeitos_total"] += insatisfeitos

                percent = (satisfeitos / total * 100) if total > 0 else 0
                metrics["avaliacoes_por_tipo"][tipo] = {
                    "total": total,
                    "sem_avaliacao": sem_avaliacao,
                    "satisfeitos": satisfeitos,
                    "insatisfeitos": insatisfeitos,
                    "percentual": round(percent, 1),
                    "meta": dados.get('% Meta(2.0)', 0)
                }

            # Para Av-2 (Chamada)
            elif tipo == 'chamada':
                sim = dados.get('Sim', 0)
                nao = dados.get('Não', 0)
                total = dados.get('Avaliadas', 0)
                sem_avaliacao = dados.get('Sem Avaliacao', 0)

                percent = (sim / total * 100) if total > 0 else 0
                metrics["avaliacoes_por_tipo"][tipo] = {
                    "total": total,
                    "sem_avaliacao": sem_avaliacao,
                    "sim": sim,
                    "nao": nao,
                    "percentual": round(percent, 1),
                    "meta": dados.get('% Meta(2.0)', 0)
                }

            # Para Av-3 (Empresa)
            elif tipo == 'empresa':
                total = dados.get('Avaliadas', 0)
                sem_avaliacao = dados.get('Sem Avaliacao', 0)
                nota1 = dados.get('Nota 1', 0)
                nota2 = dados.get('Nota 2', 0)
                nota3 = dados.get('Nota 3', 0)
                nota4 = dados.get('Nota 4', 0)
                nota5 = dados.get('Nota 5', 0)

                # Considera notas 4 e 5 como satisfatórias
                satisfatorias = nota4 + nota5
                percent = (satisfatorias / total * 100) if total > 0 else 0

                metrics["avaliacoes_por_tipo"][tipo] = {
                    "total": total,
                    "sem_avaliacao": sem_avaliacao,
                    "nota1": nota1,
                    "nota2": nota2,
                    "nota3": nota3,
                    "nota4": nota4,
                    "nota5": nota5,
                    "satisfatorias": satisfatorias,
                    "percentual": round(percent, 1),
                    "meta": dados.get('% Meta(5.0)', 0)
                }

    # Calcula percentual geral baseado apenas no Av-1 (principal métrica)
    if 'atendente' in metrics["avaliacoes_por_tipo"]:
        atendente_data = metrics["avaliacoes_por_tipo"]['atendente']
        metrics["percentual_satisfacao"] = atendente_data["percentual"]
        metrics["total_avaliacoes"] = atendente_data["total"]
        metrics["satisfeitos_total"] = atendente_data["satisfeitos"]
        metrics["insatisfeitos_total"] = atendente_data["insatisfeitos"]

    return metrics


@app.get("/api/executive-summary")
async def get_executive_summary(
    start_date: str = None,
    end_date: str = None,
    queues: str = None
):
    """Endpoint para obter resumo executivo com análises"""
    try:
        # Usa datas fornecidas ou padrão (hoje)
        if not start_date or not end_date:
            today = date.today()
            start_date = today.isoformat()
            end_date = today.isoformat()

        # Processa filas
        if queues:
            queue_list = [int(q.strip()) for q in queues.split(",")]
        else:
            queue_list = AVAILABLE_QUEUES

        # Obtém dados de distribuição e satisfação
        distribution_data = await call_3cx_api_with_params("Distribuição", start_date, end_date, queue_list)
        satisfaction_data = await call_3cx_api_with_params("Pesquisa Satisfação", start_date, end_date, queue_list)

        # Prepara dados para análise
        dist_formatted = {
            "success": True,
            "data": distribution_data,
            "period": {"start": start_date, "end": end_date}
        }

        sat_formatted = {
            "success": True,
            "data": satisfaction_data,
            "period": {"start": start_date, "end": end_date}
        }

        # Usa métricas avançadas
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'services'))
        from advanced_metrics import advanced_metrics

        # Calcula métricas abrangentes
        comprehensive_metrics = advanced_metrics.calculate_comprehensive_metrics(
            distribution_data, satisfaction_data
        )

        # Gera resumo executivo melhorado
        period_info = {"start": start_date, "end": end_date}
        executive_summary = generate_executive_summary(dist_formatted, sat_formatted, period_info)

        # Adiciona métricas avançadas ao resumo
        if comprehensive_metrics:
            executive_summary["advanced_metrics"] = {
                "timing_score": comprehensive_metrics.get("timing_metrics", {}).get("efficiency_score", 0),
                "satisfaction_score": comprehensive_metrics.get("satisfaction_metrics", {}).get("overall_satisfaction", 0),
                "quality_index": comprehensive_metrics.get("quality_metrics", {}).get("service_quality_index", 0),
                "performance_level": comprehensive_metrics.get("performance_classification", {}).get("level", "Regular")
            }

            # Adiciona insights operacionais
            executive_summary["operational_insights"] = comprehensive_metrics.get("operational_insights", [])

            # Adiciona classificação de performance
            executive_summary["performance_classification"] = comprehensive_metrics.get("performance_classification", {})

        # Adiciona análises individuais (mantém compatibilidade)
        dist_analysis = analyze_distribution_metrics(dist_formatted)
        sat_analysis = analyze_satisfaction_metrics(sat_formatted)

        return {
            "success": True,
            "period": {"start": start_date, "end": end_date},
            "queues": queue_list,
            "executive_summary": executive_summary,
            "detailed_analysis": {
                "distribution": dist_analysis,
                "satisfaction": sat_analysis
            },
            "comprehensive_metrics": comprehensive_metrics
        }

    except Exception as e:
        logger.error(f"Erro ao gerar resumo executivo: {str(e)}")
        return {"success": False, "message": f"Erro: {str(e)}"}

@app.get("/api/trends-analysis")
async def get_trends_analysis(
    current_start: str = None,
    current_end: str = None,
    previous_start: str = None,
    previous_end: str = None,
    queues: str = None
):
    """API para análise de tendências comparando dois períodos"""
    try:
        # Define períodos padrão se não fornecidos
        if not current_start or not current_end:
            today = date.today()
            current_end = today.isoformat()
            current_start = (today - timedelta(days=7)).isoformat()

        if not previous_start or not previous_end:
            current_start_date = date.fromisoformat(current_start)
            period_length = (date.fromisoformat(current_end) - current_start_date).days
            previous_end = (current_start_date - timedelta(days=1)).isoformat()
            previous_start = (current_start_date - timedelta(days=period_length + 1)).isoformat()

        # Processa filas
        if queues:
            queue_list = [int(q.strip()) for q in queues.split(",")]
        else:
            queue_list = AVAILABLE_QUEUES

        # Obtém dados dos dois períodos
        current_dist = await call_3cx_api_with_params("Distribuição", current_start, current_end, queue_list)
        current_sat = await call_3cx_api_with_params("Pesquisa Satisfação", current_start, current_end, queue_list)

        previous_dist = await call_3cx_api_with_params("Distribuição", previous_start, previous_end, queue_list)
        previous_sat = await call_3cx_api_with_params("Pesquisa Satisfação", previous_start, previous_end, queue_list)

        # Importa calculador avançado
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'services'))
        from advanced_metrics import advanced_metrics

        # Calcula métricas para ambos os períodos
        current_metrics = advanced_metrics.calculate_comprehensive_metrics(current_dist, current_sat)
        previous_metrics = advanced_metrics.calculate_comprehensive_metrics(previous_dist, previous_sat)

        # Calcula variações
        trends = {}

        # Timing trends
        current_timing = current_metrics.get("timing_metrics", {})
        previous_timing = previous_metrics.get("timing_metrics", {})

        timing_comparisons = [
            ("service_level_percentage", "Service Level"),
            ("answer_rate", "Taxa de Atendimento"),
            ("asa_seconds", "ASA (segundos)"),
            ("efficiency_score", "Score de Eficiência")
        ]

        for metric_key, metric_name in timing_comparisons:
            current_val = current_timing.get(metric_key, 0)
            previous_val = previous_timing.get(metric_key, 0)

            if previous_val > 0:
                variation = ((current_val - previous_val) / previous_val) * 100
                trend_direction = "up" if variation > 2 else "down" if variation < -2 else "stable"

                trends[metric_key] = {
                    "name": metric_name,
                    "current": current_val,
                    "previous": previous_val,
                    "variation_percent": round(variation, 2),
                    "trend": trend_direction,
                    "status": "good" if (variation > 0 and metric_key != "asa_seconds") or (variation < 0 and metric_key == "asa_seconds") else "warning"
                }

        # Satisfaction trends
        current_satisfaction = current_metrics.get("satisfaction_metrics", {})
        previous_satisfaction = previous_metrics.get("satisfaction_metrics", {})

        satisfaction_comparisons = [
            ("overall_satisfaction", "Satisfação Geral"),
            ("participation_rate", "Taxa de Participação"),
            ("nps_score", "NPS Score")
        ]

        for metric_key, metric_name in satisfaction_comparisons:
            current_val = current_satisfaction.get(metric_key, 0)
            previous_val = previous_satisfaction.get(metric_key, 0)

            if previous_val > 0:
                variation = ((current_val - previous_val) / previous_val) * 100
                trend_direction = "up" if variation > 2 else "down" if variation < -2 else "stable"

                trends[metric_key] = {
                    "name": metric_name,
                    "current": current_val,
                    "previous": previous_val,
                    "variation_percent": round(variation, 2),
                    "trend": trend_direction,
                    "status": "good" if variation > 0 else "warning"
                }

        # Gera insights de tendências
        trend_insights = []

        for metric_key, trend_data in trends.items():
            variation = trend_data["variation_percent"]
            name = trend_data["name"]

            if abs(variation) > 10:
                if variation > 0:
                    trend_insights.append(f"📈 {name}: Melhoria significativa (+{variation:.1f}%)")
                else:
                    trend_insights.append(f"📉 {name}: Declínio significativo ({variation:.1f}%)")
            elif abs(variation) > 5:
                if variation > 0:
                    trend_insights.append(f"↗️ {name}: Melhoria moderada (+{variation:.1f}%)")
                else:
                    trend_insights.append(f"↘️ {name}: Declínio moderado ({variation:.1f}%)")
            else:
                trend_insights.append(f"➡️ {name}: Estável ({variation:+.1f}%)")

        # Performance comparison
        current_performance = current_metrics.get("performance_classification", {})
        previous_performance = previous_metrics.get("performance_classification", {})

        performance_comparison = {
            "current_level": current_performance.get("level", "Regular"),
            "previous_level": previous_performance.get("level", "Regular"),
            "current_score": current_performance.get("score", 0),
            "previous_score": previous_performance.get("score", 0),
            "improvement": current_performance.get("score", 0) > previous_performance.get("score", 0)
        }

        return {
            "success": True,
            "message": f"Análise de tendências gerada",
            "data": {
                "periods": {
                    "current": {"start": current_start, "end": current_end},
                    "previous": {"start": previous_start, "end": previous_end}
                },
                "trends": trends,
                "trend_insights": trend_insights,
                "performance_comparison": performance_comparison,
                "summary": {
                    "total_metrics_analyzed": len(trends),
                    "improving_metrics": len([t for t in trends.values() if t["variation_percent"] > 2]),
                    "declining_metrics": len([t for t in trends.values() if t["variation_percent"] < -2]),
                    "stable_metrics": len([t for t in trends.values() if abs(t["variation_percent"]) <= 2])
                }
            },
            "queues": queue_list,
            "generated_at": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Erro ao gerar análise de tendências: {str(e)}")
        return {"success": False, "message": f"Erro: {str(e)}"}

@app.get("/api/alerts-recommendations")
async def get_alerts_recommendations(start_date: str = None, end_date: str = None, queues: str = None):
    """API para alertas e recomendações automáticas"""
    try:
        # Usa datas fornecidas ou padrão (hoje)
        if not start_date or not end_date:
            today = date.today()
            start_date = today.isoformat()
            end_date = today.isoformat()

        # Processa filas
        if queues:
            queue_list = [int(q.strip()) for q in queues.split(",")]
        else:
            queue_list = AVAILABLE_QUEUES

        # Obtém dados
        distribution_data = await call_3cx_api_with_params("Distribuição", start_date, end_date, queue_list)
        satisfaction_data = await call_3cx_api_with_params("Pesquisa Satisfação", start_date, end_date, queue_list)

        # Importa calculador avançado
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'services'))
        from advanced_metrics import advanced_metrics

        # Calcula métricas
        comprehensive_metrics = advanced_metrics.calculate_comprehensive_metrics(
            distribution_data, satisfaction_data
        )

        alerts = []
        recommendations = []

        # Análise de timing
        timing_metrics = comprehensive_metrics.get("timing_metrics", {})

        # Alertas de Service Level
        service_level = timing_metrics.get("service_level_percentage", 0)
        if service_level < 60:
            alerts.append({
                "type": "critical",
                "category": "service_level",
                "title": "Service Level Crítico",
                "message": f"Service Level atual ({service_level:.1f}%) está muito abaixo da meta (80%)",
                "priority": "high",
                "impact": "customer_experience"
            })
            recommendations.append({
                "category": "service_level",
                "title": "Melhorar Service Level",
                "actions": [
                    "Aumentar número de agentes nos horários de pico",
                    "Revisar processos de atendimento para reduzir tempo médio",
                    "Implementar sistema de callback para reduzir abandono"
                ],
                "expected_impact": "Redução do tempo de espera e melhoria da satisfação",
                "priority": "high"
            })
        elif service_level < 80:
            alerts.append({
                "type": "warning",
                "category": "service_level",
                "title": "Service Level Abaixo da Meta",
                "message": f"Service Level atual ({service_level:.1f}%) precisa melhorar para atingir a meta (80%)",
                "priority": "medium",
                "impact": "performance"
            })
            recommendations.append({
                "category": "service_level",
                "title": "Otimizar Service Level",
                "actions": [
                    "Analisar distribuição de chamadas por horário",
                    "Treinar agentes para reduzir tempo de atendimento",
                    "Implementar scripts padronizados"
                ],
                "expected_impact": "Melhoria gradual do tempo de resposta",
                "priority": "medium"
            })

        # Alertas de Taxa de Abandono
        abandonment_rate = timing_metrics.get("abandonment_rate", 0)
        if abandonment_rate > 15:
            alerts.append({
                "type": "critical",
                "category": "abandonment",
                "title": "Taxa de Abandono Crítica",
                "message": f"Taxa de abandono ({abandonment_rate:.1f}%) está muito alta",
                "priority": "high",
                "impact": "revenue"
            })
            recommendations.append({
                "category": "abandonment",
                "title": "Reduzir Taxa de Abandono",
                "actions": [
                    "Implementar música de espera e mensagens informativas",
                    "Oferecer opção de callback",
                    "Aumentar capacidade de atendimento nos picos"
                ],
                "expected_impact": "Redução significativa do abandono de chamadas",
                "priority": "high"
            })
        elif abandonment_rate > 8:
            alerts.append({
                "type": "warning",
                "category": "abandonment",
                "title": "Taxa de Abandono Elevada",
                "message": f"Taxa de abandono ({abandonment_rate:.1f}%) acima do ideal (5%)",
                "priority": "medium",
                "impact": "customer_satisfaction"
            })

        # Análise de satisfação
        satisfaction_metrics = comprehensive_metrics.get("satisfaction_metrics", {})
        overall_satisfaction = satisfaction_metrics.get("overall_satisfaction", 0)

        if overall_satisfaction < 60:
            alerts.append({
                "type": "critical",
                "category": "satisfaction",
                "title": "Satisfação Crítica",
                "message": f"Satisfação geral ({overall_satisfaction:.1f}%) está muito baixa",
                "priority": "high",
                "impact": "customer_retention"
            })
            recommendations.append({
                "category": "satisfaction",
                "title": "Melhorar Satisfação do Cliente",
                "actions": [
                    "Implementar programa de treinamento intensivo",
                    "Revisar scripts de atendimento",
                    "Criar sistema de feedback em tempo real",
                    "Implementar coaching individual para agentes"
                ],
                "expected_impact": "Melhoria significativa na experiência do cliente",
                "priority": "high"
            })
        elif overall_satisfaction < 80:
            alerts.append({
                "type": "warning",
                "category": "satisfaction",
                "title": "Satisfação Abaixo do Ideal",
                "message": f"Satisfação geral ({overall_satisfaction:.1f}%) pode melhorar",
                "priority": "medium",
                "impact": "customer_experience"
            })

        # Análise de agentes críticos
        critical_agents = satisfaction_metrics.get("critical_agents", [])
        if critical_agents:
            alerts.append({
                "type": "warning",
                "category": "agents",
                "title": "Agentes com Performance Crítica",
                "message": f"{len(critical_agents)} agente(s) com satisfação abaixo de 70%",
                "priority": "medium",
                "impact": "team_performance"
            })
            recommendations.append({
                "category": "agents",
                "title": "Desenvolver Agentes com Baixa Performance",
                "actions": [
                    "Implementar plano de desenvolvimento individual",
                    "Aumentar frequência de coaching",
                    "Revisar processos de treinamento",
                    "Considerar mentoria com top performers"
                ],
                "expected_impact": "Melhoria da performance individual e da equipe",
                "priority": "medium"
            })

        # Análise de participação nas pesquisas
        participation_rate = satisfaction_metrics.get("participation_rate", 0)
        if participation_rate < 20:
            alerts.append({
                "type": "info",
                "category": "surveys",
                "title": "Baixa Participação nas Pesquisas",
                "message": f"Taxa de participação ({participation_rate:.1f}%) muito baixa",
                "priority": "low",
                "impact": "data_quality"
            })
            recommendations.append({
                "category": "surveys",
                "title": "Aumentar Participação nas Pesquisas",
                "actions": [
                    "Simplificar processo de avaliação",
                    "Implementar incentivos para participação",
                    "Melhorar comunicação sobre importância do feedback",
                    "Otimizar momento de envio das pesquisas"
                ],
                "expected_impact": "Maior volume de feedback para análises mais precisas",
                "priority": "low"
            })

        # Análise de eficiência
        efficiency_score = timing_metrics.get("efficiency_score", 0)
        if efficiency_score < 60:
            recommendations.append({
                "category": "efficiency",
                "title": "Melhorar Eficiência Operacional",
                "actions": [
                    "Implementar ferramentas de automação",
                    "Otimizar fluxos de trabalho",
                    "Revisar distribuição de recursos",
                    "Implementar métricas de produtividade"
                ],
                "expected_impact": "Aumento da eficiência e redução de custos",
                "priority": "medium"
            })

        # Recomendações gerais baseadas na classificação de performance
        performance_classification = comprehensive_metrics.get("performance_classification", {})
        performance_level = performance_classification.get("level", "Regular")

        if performance_level == "Crítico":
            recommendations.append({
                "category": "general",
                "title": "Plano de Recuperação Urgente",
                "actions": [
                    "Implementar plano de ação imediato",
                    "Aumentar supervisão e monitoramento",
                    "Revisar todos os processos operacionais",
                    "Considerar recursos adicionais temporários"
                ],
                "expected_impact": "Recuperação rápida dos indicadores críticos",
                "priority": "critical"
            })
        elif performance_level == "Regular":
            recommendations.append({
                "category": "general",
                "title": "Plano de Melhoria Contínua",
                "actions": [
                    "Implementar ciclos de melhoria contínua",
                    "Estabelecer metas incrementais",
                    "Aumentar frequência de análises",
                    "Investir em treinamento da equipe"
                ],
                "expected_impact": "Evolução gradual para nível 'Bom'",
                "priority": "medium"
            })

        return {
            "success": True,
            "message": f"Alertas e recomendações gerados para {start_date} a {end_date}",
            "data": {
                "alerts": alerts,
                "recommendations": recommendations,
                "summary": {
                    "total_alerts": len(alerts),
                    "critical_alerts": len([a for a in alerts if a["type"] == "critical"]),
                    "warning_alerts": len([a for a in alerts if a["type"] == "warning"]),
                    "info_alerts": len([a for a in alerts if a["type"] == "info"]),
                    "total_recommendations": len(recommendations),
                    "high_priority_recommendations": len([r for r in recommendations if r.get("priority") == "high"]),
                    "performance_level": performance_level,
                    "overall_health": "critical" if len([a for a in alerts if a["type"] == "critical"]) > 0 else
                                   "warning" if len([a for a in alerts if a["type"] == "warning"]) > 0 else "good"
                }
            },
            "period": {"start": start_date, "end": end_date},
            "queues": queue_list,
            "generated_at": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Erro ao gerar alertas e recomendações: {str(e)}")
        return {"success": False, "message": f"Erro: {str(e)}"}


# ============================================================================
# CHATWOOT INTEGRATION - MINIMAL IMPLEMENTATION
# ============================================================================

# Configurações do Chatwoot
CHATWOOT_DB_HOST = os.getenv("CHATWOOT_DB_HOST", "n8n_chatwoot_postgres")
CHATWOOT_DB_PORT = int(os.getenv("CHATWOOT_DB_PORT", "5432"))
CHATWOOT_DB_NAME = os.getenv("CHATWOOT_DB_NAME", "chatwoot")
CHATWOOT_DB_USER = os.getenv("CHATWOOT_DB_USER", "chatwoot")
CHATWOOT_DB_PASSWORD = os.getenv("CHATWOOT_DB_PASSWORD", "chatwoot_password_123")
CHATWOOT_ACCOUNT_ID = int(os.getenv("CHATWOOT_ACCOUNT_ID", "1"))

# Pool de conexão global
chatwoot_pool = None

async def init_chatwoot():
    """Inicializa conexão com Chatwoot"""
    global chatwoot_pool
    try:
        import asyncpg
        database_url = f"postgresql://{CHATWOOT_DB_USER}:{CHATWOOT_DB_PASSWORD}@{CHATWOOT_DB_HOST}:{CHATWOOT_DB_PORT}/{CHATWOOT_DB_NAME}"
        chatwoot_pool = await asyncpg.create_pool(
            database_url,
            min_size=1,
            max_size=5,
            command_timeout=60
        )
        logger.info("✅ Conexão com Chatwoot estabelecida")
    except Exception as e:
        logger.error(f"❌ Erro ao conectar com Chatwoot: {e}")

async def close_chatwoot():
    """Fecha conexão com Chatwoot"""
    global chatwoot_pool
    if chatwoot_pool:
        await chatwoot_pool.close()
        logger.info("🔌 Conexão com Chatwoot fechada")

async def test_chatwoot_connection():
    """Testa conexão com Chatwoot"""
    global chatwoot_pool
    try:
        if not chatwoot_pool:
            return False
        async with chatwoot_pool.acquire() as conn:
            await conn.fetchval("SELECT version()")
            return True
    except Exception as e:
        logger.error(f"❌ Erro no teste de conexão Chatwoot: {e}")
        return False

# Eventos de inicialização e finalização
@app.on_event("startup")
async def startup_event():
    await init_chatwoot()

@app.on_event("shutdown")
async def shutdown_event():
    await close_chatwoot()

# Rotas do Chatwoot
@app.get("/chatwoot", response_class=HTMLResponse)
async def chatwoot_dashboard(request: Request):
    """Dashboard Chatwoot"""
    return templates.TemplateResponse("chatwoot_simple.html", {
        "request": request,
        "title": "Dashboard Chatwoot - WhatsApp"
    })

@app.get("/api/v1/chatwoot/health")
async def chatwoot_health():
    """Health check específico do Chatwoot"""
    try:
        is_connected = await test_chatwoot_connection()
        return {
            "status": "healthy" if is_connected else "unhealthy",
            "service": "chatwoot",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": "chatwoot",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@app.get("/api/v1/chatwoot/agents")
async def get_chatwoot_agents():
    """Lista agentes do Chatwoot"""
    global chatwoot_pool
    try:
        if not chatwoot_pool:
            raise HTTPException(status_code=503, detail="Chatwoot não conectado")

        async with chatwoot_pool.acquire() as conn:
            rows = await conn.fetch("SELECT id, name, email FROM users ORDER BY name")
            agents = [dict(row) for row in rows]

        return {
            "success": True,
            "data": agents,
            "total": len(agents)
        }
    except Exception as e:
        logger.error(f"❌ Erro ao buscar agentes: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/api/v1/chatwoot/conversations")
async def get_chatwoot_conversations(
    start_date: date,
    end_date: date,
    limit: int = 100
):
    """Lista conversas do Chatwoot"""
    global chatwoot_pool
    try:
        if not chatwoot_pool:
            raise HTTPException(status_code=503, detail="Chatwoot não conectado")

        async with chatwoot_pool.acquire() as conn:
            query = """
                SELECT id, status, created_at, updated_at
                FROM conversations
                WHERE created_at BETWEEN $1 AND $2
                ORDER BY created_at DESC
                LIMIT $3
            """
            rows = await conn.fetch(query, start_date, end_date, limit)
            conversations = [dict(row) for row in rows]

        return {
            "success": True,
            "data": conversations,
            "total": len(conversations),
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        }
    except Exception as e:
        logger.error(f"❌ Erro ao buscar conversas: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/api/v1/chatwoot/metrics")
async def get_chatwoot_metrics(
    start_date: date,
    end_date: date
):
    """Métricas completas do Chatwoot"""
    global chatwoot_pool
    try:
        if not chatwoot_pool:
            raise HTTPException(status_code=503, detail="Chatwoot não conectado")

        async with chatwoot_pool.acquire() as conn:
            # Métricas gerais de conversas
            conversations_query = """
                SELECT
                    COUNT(*) as total_conversations,
                    COUNT(CASE WHEN status = 0 THEN 1 END) as open_conversations,
                    COUNT(CASE WHEN status = 1 THEN 1 END) as resolved_conversations,
                    COUNT(CASE WHEN status = 2 THEN 1 END) as pending_conversations,
                    COUNT(CASE WHEN status = 3 THEN 1 END) as snoozed_conversations,
                    AVG(CASE WHEN first_reply_created_at IS NOT NULL
                        THEN EXTRACT(EPOCH FROM (first_reply_created_at - created_at))/60
                        END) as avg_first_response_time_minutes,
                    COUNT(DISTINCT assignee_id) as active_agents
                FROM conversations
                WHERE created_at BETWEEN $1 AND $2
            """

            # Volume por dia
            daily_volume_query = """
                SELECT
                    DATE(created_at) as date,
                    COUNT(*) as conversations,
                    COUNT(CASE WHEN status = 1 THEN 1 END) as resolved,
                    COUNT(CASE WHEN status = 0 THEN 1 END) as open,
                    COUNT(CASE WHEN status = 2 THEN 1 END) as pending
                FROM conversations
                WHERE created_at BETWEEN $1 AND $2
                GROUP BY DATE(created_at)
                ORDER BY date
            """

            # Performance por agente
            agents_performance_query = """
                SELECT
                    u.name as agent_name,
                    u.id as agent_id,
                    COUNT(c.id) as total_conversations,
                    COUNT(CASE WHEN c.status = 1 THEN 1 END) as resolved_conversations,
                    AVG(CASE WHEN c.first_reply_created_at IS NOT NULL
                        THEN EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at))/60
                        END) as avg_response_time_minutes,
                    ROUND(
                        COUNT(CASE WHEN c.status = 1 THEN 1 END) * 100.0 / NULLIF(COUNT(c.id), 0), 2
                    ) as resolution_rate
                FROM conversations c
                LEFT JOIN users u ON c.assignee_id = u.id
                WHERE c.created_at BETWEEN $1 AND $2 AND c.assignee_id IS NOT NULL
                GROUP BY u.id, u.name
                ORDER BY total_conversations DESC
            """

            # Volume por hora do dia
            hourly_volume_query = """
                SELECT
                    EXTRACT(HOUR FROM created_at) as hour,
                    COUNT(*) as conversations
                FROM conversations
                WHERE created_at BETWEEN $1 AND $2
                GROUP BY EXTRACT(HOUR FROM created_at)
                ORDER BY hour
            """

            # Executar todas as queries
            conversations_result = await conn.fetchrow(conversations_query, start_date, end_date)
            daily_volume_result = await conn.fetch(daily_volume_query, start_date, end_date)
            agents_result = await conn.fetch(agents_performance_query, start_date, end_date)
            hourly_result = await conn.fetch(hourly_volume_query, start_date, end_date)

            # Processar resultados
            metrics = dict(conversations_result) if conversations_result else {}
            daily_volume = [dict(row) for row in daily_volume_result]
            agents_performance = [dict(row) for row in agents_result]
            hourly_volume = [dict(row) for row in hourly_result]

            # Calcular taxa de resolução geral
            total_conversations = metrics.get('total_conversations', 0)
            resolved_conversations = metrics.get('resolved_conversations', 0)
            resolution_rate = (resolved_conversations * 100 / total_conversations) if total_conversations > 0 else 0

            return {
                "success": True,
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "summary": {
                    "total_conversations": total_conversations,
                    "open_conversations": metrics.get('open_conversations', 0),
                    "resolved_conversations": resolved_conversations,
                    "pending_conversations": metrics.get('pending_conversations', 0),
                    "snoozed_conversations": metrics.get('snoozed_conversations', 0),
                    "resolution_rate": round(resolution_rate, 2),
                    "avg_first_response_time_minutes": round(metrics.get('avg_first_response_time_minutes', 0) or 0, 2),
                    "active_agents": metrics.get('active_agents', 0)
                },
                "daily_volume": daily_volume,
                "agents_performance": agents_performance,
                "hourly_volume": hourly_volume
            }

    except Exception as e:
        logger.error(f"❌ Erro ao buscar métricas: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/api/v1/chatwoot/satisfaction")
async def get_chatwoot_satisfaction(
    start_date: date,
    end_date: date
):
    """Métricas de satisfação do Chatwoot"""
    global chatwoot_pool
    try:
        if not chatwoot_pool:
            raise HTTPException(status_code=503, detail="Chatwoot não conectado")

        async with chatwoot_pool.acquire() as conn:
            # Verificar se existe tabela de satisfação
            table_check = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = 'csat_survey_responses'
                )
            """)

            if not table_check:
                return {
                    "success": True,
                    "message": "Tabela de satisfação não encontrada",
                    "data": [],
                    "summary": {
                        "total_ratings": 0,
                        "average_rating": 0,
                        "satisfaction_rate": 0
                    }
                }

            # Buscar dados de satisfação
            satisfaction_query = """
                SELECT
                    u.name as agent_name,
                    u.id as agent_id,
                    COUNT(csr.id) as total_ratings,
                    AVG(csr.rating) as average_rating,
                    COUNT(CASE WHEN csr.rating >= 4 THEN 1 END) as positive_ratings,
                    COUNT(CASE WHEN csr.rating <= 2 THEN 1 END) as negative_ratings,
                    ROUND(
                        COUNT(CASE WHEN csr.rating >= 4 THEN 1 END) * 100.0 / NULLIF(COUNT(csr.id), 0), 2
                    ) as satisfaction_percentage
                FROM csat_survey_responses csr
                LEFT JOIN users u ON csr.assigned_agent_id = u.id
                WHERE csr.created_at BETWEEN $1 AND $2
                GROUP BY u.id, u.name
                ORDER BY average_rating DESC
            """

            satisfaction_result = await conn.fetch(satisfaction_query, start_date, end_date)
            satisfaction_data = [dict(row) for row in satisfaction_result]

            # Calcular resumo
            total_ratings = sum(row['total_ratings'] for row in satisfaction_data)
            total_positive = sum(row['positive_ratings'] for row in satisfaction_data)
            avg_rating = sum(row['average_rating'] * row['total_ratings'] for row in satisfaction_data) / total_ratings if total_ratings > 0 else 0
            satisfaction_rate = (total_positive * 100 / total_ratings) if total_ratings > 0 else 0

            return {
                "success": True,
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "data": satisfaction_data,
                "summary": {
                    "total_ratings": total_ratings,
                    "average_rating": round(avg_rating, 2),
                    "satisfaction_rate": round(satisfaction_rate, 2),
                    "total_agents": len(satisfaction_data)
                }
            }

    except Exception as e:
        logger.error(f"❌ Erro ao buscar satisfação: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/api/v1/chatwoot/messages")
async def get_chatwoot_messages(
    start_date: date,
    end_date: date,
    limit: int = 1000
):
    """Análise detalhada de mensagens"""
    global chatwoot_pool
    try:
        if not chatwoot_pool:
            raise HTTPException(status_code=503, detail="Chatwoot não conectado")

        async with chatwoot_pool.acquire() as conn:
            # Métricas de mensagens
            messages_query = """
                SELECT
                    COUNT(*) as total_messages,
                    COUNT(CASE WHEN message_type = 0 THEN 1 END) as incoming_messages,
                    COUNT(CASE WHEN message_type = 1 THEN 1 END) as outgoing_messages,
                    COUNT(CASE WHEN message_type = 2 THEN 1 END) as activity_messages,
                    COUNT(CASE WHEN message_type = 3 THEN 1 END) as template_messages,
                    COUNT(DISTINCT conversation_id) as conversations_with_messages,
                    COUNT(DISTINCT sender_id) as unique_senders,
                    AVG(LENGTH(COALESCE(content, ''))) as avg_message_length,
                    COUNT(CASE WHEN content_type::text = 'text' THEN 1 END) as text_messages,
                    COUNT(CASE WHEN content_type::text = 'image' THEN 1 END) as image_messages,
                    COUNT(CASE WHEN content_type::text = 'audio' THEN 1 END) as audio_messages,
                    COUNT(CASE WHEN content_type::text = 'video' THEN 1 END) as video_messages,
                    COUNT(CASE WHEN content_type::text = 'file' THEN 1 END) as file_messages,
                    COUNT(CASE WHEN content_type::text = 'location' THEN 1 END) as location_messages
                FROM messages
                WHERE created_at BETWEEN $1 AND $2
            """

            # Volume de mensagens por hora
            hourly_messages_query = """
                SELECT
                    EXTRACT(HOUR FROM created_at) as hour,
                    COUNT(*) as total_messages,
                    COUNT(CASE WHEN message_type = 0 THEN 1 END) as incoming,
                    COUNT(CASE WHEN message_type = 1 THEN 1 END) as outgoing
                FROM messages
                WHERE created_at BETWEEN $1 AND $2
                GROUP BY EXTRACT(HOUR FROM created_at)
                ORDER BY hour
            """

            # Volume por dia
            daily_messages_query = """
                SELECT
                    DATE(created_at) as date,
                    COUNT(*) as total_messages,
                    COUNT(CASE WHEN message_type = 0 THEN 1 END) as incoming,
                    COUNT(CASE WHEN message_type = 1 THEN 1 END) as outgoing,
                    COUNT(DISTINCT conversation_id) as active_conversations
                FROM messages
                WHERE created_at BETWEEN $1 AND $2
                GROUP BY DATE(created_at)
                ORDER BY date
            """

            # Performance por agente (mensagens)
            agent_messages_query = """
                SELECT
                    u.name as agent_name,
                    u.id as agent_id,
                    COUNT(m.id) as total_messages_sent,
                    AVG(LENGTH(COALESCE(m.content, ''))) as avg_message_length,
                    COUNT(DISTINCT m.conversation_id) as conversations_participated,
                    MIN(m.created_at) as first_message_time,
                    MAX(m.created_at) as last_message_time
                FROM messages m
                LEFT JOIN users u ON m.sender_id = u.id
                WHERE m.created_at BETWEEN $1 AND $2
                AND m.message_type = 1
                AND u.id IS NOT NULL
                GROUP BY u.id, u.name
                ORDER BY total_messages_sent DESC
            """

            # Executar queries
            messages_result = await conn.fetchrow(messages_query, start_date, end_date)
            hourly_result = await conn.fetch(hourly_messages_query, start_date, end_date)
            daily_result = await conn.fetch(daily_messages_query, start_date, end_date)
            agent_result = await conn.fetch(agent_messages_query, start_date, end_date)

            return {
                "success": True,
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "summary": dict(messages_result) if messages_result else {},
                "hourly_volume": [dict(row) for row in hourly_result],
                "daily_volume": [dict(row) for row in daily_result],
                "agent_performance": [dict(row) for row in agent_result]
            }

    except Exception as e:
        logger.error(f"❌ Erro ao buscar mensagens: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/api/v1/chatwoot/contacts")
async def get_chatwoot_contacts(
    start_date: date,
    end_date: date
):
    """Análise de contatos e clientes"""
    global chatwoot_pool
    try:
        if not chatwoot_pool:
            raise HTTPException(status_code=503, detail="Chatwoot não conectado")

        async with chatwoot_pool.acquire() as conn:
            # Métricas de contatos
            contacts_query = """
                SELECT
                    COUNT(*) as total_contacts,
                    COUNT(CASE WHEN created_at BETWEEN $1 AND $2 THEN 1 END) as new_contacts_period,
                    COUNT(CASE WHEN phone_number IS NOT NULL AND phone_number != '' THEN 1 END) as contacts_with_phone,
                    COUNT(CASE WHEN email IS NOT NULL AND email != '' THEN 1 END) as contacts_with_email,
                    COUNT(CASE WHEN name IS NOT NULL AND name != '' THEN 1 END) as contacts_with_name,
                    COUNT(CASE WHEN blocked = true THEN 1 END) as blocked_contacts
                FROM contacts
            """

            # Contatos mais ativos
            active_contacts_query = """
                SELECT
                    c.name,
                    c.phone_number,
                    c.email,
                    COUNT(DISTINCT conv.id) as total_conversations,
                    COUNT(m.id) as total_messages,
                    MAX(conv.created_at) as last_conversation,
                    MIN(conv.created_at) as first_conversation
                FROM contacts c
                LEFT JOIN conversations conv ON c.id = conv.contact_id
                LEFT JOIN messages m ON conv.id = m.conversation_id AND m.message_type = 0
                WHERE conv.created_at BETWEEN $1 AND $2
                GROUP BY c.id, c.name, c.phone_number, c.email
                HAVING COUNT(DISTINCT conv.id) > 0
                ORDER BY total_conversations DESC, total_messages DESC
                LIMIT 50
            """

            # Novos contatos por dia
            daily_contacts_query = """
                SELECT
                    DATE(created_at) as date,
                    COUNT(*) as new_contacts
                FROM contacts
                WHERE created_at BETWEEN $1 AND $2
                GROUP BY DATE(created_at)
                ORDER BY date
            """

            # Executar queries
            contacts_result = await conn.fetchrow(contacts_query, start_date, end_date)
            active_result = await conn.fetch(active_contacts_query, start_date, end_date)
            daily_result = await conn.fetch(daily_contacts_query, start_date, end_date)

            return {
                "success": True,
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "summary": dict(contacts_result) if contacts_result else {},
                "most_active": [dict(row) for row in active_result],
                "daily_new_contacts": [dict(row) for row in daily_result]
            }

    except Exception as e:
        logger.error(f"❌ Erro ao buscar contatos: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/api/v1/chatwoot/response-times")
async def get_chatwoot_response_times(
    start_date: date,
    end_date: date
):
    """Análise detalhada de tempos de resposta"""
    global chatwoot_pool
    try:
        if not chatwoot_pool:
            raise HTTPException(status_code=503, detail="Chatwoot não conectado")

        async with chatwoot_pool.acquire() as conn:
            # Tempos de resposta por agente (simplificado)
            response_times_query = """
                SELECT
                    u.name as agent_name,
                    u.id as agent_id,
                    COUNT(c.id) as total_conversations,
                    AVG(CASE WHEN c.first_reply_created_at IS NOT NULL
                        THEN EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at))/60
                        END) as avg_first_response_minutes,
                    COUNT(CASE WHEN c.first_reply_created_at IS NOT NULL
                        AND EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at))/60 <= 5
                        THEN 1 END) as responses_under_5min,
                    COUNT(CASE WHEN c.first_reply_created_at IS NOT NULL
                        AND EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at))/60 <= 15
                        THEN 1 END) as responses_under_15min,
                    COUNT(CASE WHEN c.first_reply_created_at IS NOT NULL
                        AND EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at))/60 > 60
                        THEN 1 END) as responses_over_1hour
                FROM conversations c
                LEFT JOIN users u ON c.assignee_id = u.id
                WHERE c.created_at BETWEEN $1 AND $2 AND c.assignee_id IS NOT NULL
                GROUP BY u.id, u.name
                ORDER BY avg_first_response_minutes ASC
            """

            # Distribuição simplificada
            distribution_query = """
                SELECT
                    '0-5 min' as time_range,
                    COUNT(*) as conversation_count
                FROM conversations
                WHERE created_at BETWEEN $1 AND $2
                AND first_reply_created_at IS NOT NULL
                AND EXTRACT(EPOCH FROM (first_reply_created_at - created_at))/60 <= 5
                UNION ALL
                SELECT
                    '5-15 min' as time_range,
                    COUNT(*) as conversation_count
                FROM conversations
                WHERE created_at BETWEEN $1 AND $2
                AND first_reply_created_at IS NOT NULL
                AND EXTRACT(EPOCH FROM (first_reply_created_at - created_at))/60 > 5
                AND EXTRACT(EPOCH FROM (first_reply_created_at - created_at))/60 <= 15
                UNION ALL
                SELECT
                    '15+ min' as time_range,
                    COUNT(*) as conversation_count
                FROM conversations
                WHERE created_at BETWEEN $1 AND $2
                AND first_reply_created_at IS NOT NULL
                AND EXTRACT(EPOCH FROM (first_reply_created_at - created_at))/60 > 15
            """

            # Tempos por hora do dia
            hourly_response_query = """
                SELECT
                    EXTRACT(HOUR FROM created_at) as hour,
                    COUNT(*) as total_conversations,
                    AVG(CASE WHEN first_reply_created_at IS NOT NULL
                        THEN EXTRACT(EPOCH FROM (first_reply_created_at - created_at))/60
                        END) as avg_response_time_minutes
                FROM conversations
                WHERE created_at BETWEEN $1 AND $2
                GROUP BY EXTRACT(HOUR FROM created_at)
                ORDER BY hour
            """

            # Executar queries
            response_times_result = await conn.fetch(response_times_query, start_date, end_date)
            distribution_result = await conn.fetch(distribution_query, start_date, end_date)
            hourly_result = await conn.fetch(hourly_response_query, start_date, end_date)

            return {
                "success": True,
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "agent_performance": [dict(row) for row in response_times_result],
                "time_distribution": [dict(row) for row in distribution_result],
                "hourly_performance": [dict(row) for row in hourly_result]
            }

    except Exception as e:
        logger.error(f"❌ Erro ao buscar tempos de resposta: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/api/v1/chatwoot/workload")
async def get_chatwoot_workload(
    start_date: date,
    end_date: date
):
    """Análise de carga de trabalho dos agentes"""
    global chatwoot_pool
    try:
        if not chatwoot_pool:
            raise HTTPException(status_code=503, detail="Chatwoot não conectado")

        async with chatwoot_pool.acquire() as conn:
            # Carga de trabalho por agente (simplificado)
            workload_query = """
                SELECT
                    u.name as agent_name,
                    u.id as agent_id,
                    u.email as agent_email,
                    COUNT(DISTINCT c.id) as total_conversations,
                    COUNT(DISTINCT CASE WHEN c.status = 0 THEN c.id END) as open_conversations,
                    COUNT(DISTINCT CASE WHEN c.status = 1 THEN c.id END) as resolved_conversations,
                    COUNT(DISTINCT CASE WHEN c.status = 2 THEN c.id END) as pending_conversations,
                    COUNT(DISTINCT DATE(c.created_at)) as active_days,
                    MIN(c.created_at) as first_conversation,
                    MAX(c.created_at) as last_conversation
                FROM users u
                LEFT JOIN conversations c ON u.id = c.assignee_id AND c.created_at BETWEEN $1 AND $2
                WHERE c.id IS NOT NULL
                GROUP BY u.id, u.name, u.email
                HAVING COUNT(DISTINCT c.id) > 0
                ORDER BY total_conversations DESC
            """

            # Distribuição de carga (simplificado)
            load_distribution_query = """
                SELECT
                    '1-5 conversas' as load_range,
                    COUNT(*) as agent_count
                FROM (
                    SELECT u.id, COUNT(DISTINCT c.id) as conversation_count
                    FROM users u
                    LEFT JOIN conversations c ON u.id = c.assignee_id AND c.created_at BETWEEN $1 AND $2
                    GROUP BY u.id
                    HAVING COUNT(DISTINCT c.id) BETWEEN 1 AND 5
                ) agent_loads
                UNION ALL
                SELECT
                    '6-15 conversas' as load_range,
                    COUNT(*) as agent_count
                FROM (
                    SELECT u.id, COUNT(DISTINCT c.id) as conversation_count
                    FROM users u
                    LEFT JOIN conversations c ON u.id = c.assignee_id AND c.created_at BETWEEN $1 AND $2
                    GROUP BY u.id
                    HAVING COUNT(DISTINCT c.id) BETWEEN 6 AND 15
                ) agent_loads
                UNION ALL
                SELECT
                    '16+ conversas' as load_range,
                    COUNT(*) as agent_count
                FROM (
                    SELECT u.id, COUNT(DISTINCT c.id) as conversation_count
                    FROM users u
                    LEFT JOIN conversations c ON u.id = c.assignee_id AND c.created_at BETWEEN $1 AND $2
                    GROUP BY u.id
                    HAVING COUNT(DISTINCT c.id) > 15
                ) agent_loads
            """

            # Atividade por dia da semana
            weekday_activity_query = """
                SELECT
                    EXTRACT(DOW FROM c.created_at) as day_of_week,
                    CASE EXTRACT(DOW FROM c.created_at)
                        WHEN 0 THEN 'Domingo'
                        WHEN 1 THEN 'Segunda'
                        WHEN 2 THEN 'Terça'
                        WHEN 3 THEN 'Quarta'
                        WHEN 4 THEN 'Quinta'
                        WHEN 5 THEN 'Sexta'
                        WHEN 6 THEN 'Sábado'
                    END as day_name,
                    COUNT(DISTINCT c.id) as total_conversations,
                    COUNT(DISTINCT c.assignee_id) as active_agents,
                    AVG(CASE WHEN c.first_reply_created_at IS NOT NULL
                        THEN EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at))/60
                        END) as avg_response_time_minutes
                FROM conversations c
                WHERE c.created_at BETWEEN $1 AND $2
                GROUP BY EXTRACT(DOW FROM c.created_at)
                ORDER BY day_of_week
            """

            # Executar queries
            workload_result = await conn.fetch(workload_query, start_date, end_date)
            distribution_result = await conn.fetch(load_distribution_query, start_date, end_date)
            weekday_result = await conn.fetch(weekday_activity_query, start_date, end_date)

            return {
                "success": True,
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "agent_workload": [dict(row) for row in workload_result],
                "load_distribution": [dict(row) for row in distribution_result],
                "weekday_activity": [dict(row) for row in weekday_result]
            }

    except Exception as e:
        logger.error(f"❌ Erro ao buscar carga de trabalho: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/api/v1/chatwoot/channels")
async def get_chatwoot_channels(
    start_date: date,
    end_date: date
):
    """Análise por canais de comunicação"""
    global chatwoot_pool
    try:
        if not chatwoot_pool:
            raise HTTPException(status_code=503, detail="Chatwoot não conectado")

        async with chatwoot_pool.acquire() as conn:
            # Performance por canal
            channels_query = """
                SELECT
                    ch.channel_type,
                    ch.name as channel_name,
                    COUNT(DISTINCT c.id) as total_conversations,
                    COUNT(DISTINCT CASE WHEN c.status = 1 THEN c.id END) as resolved_conversations,
                    COUNT(DISTINCT c.contact_id) as unique_contacts,
                    COUNT(m.id) as total_messages,
                    AVG(CASE WHEN c.first_reply_created_at IS NOT NULL
                        THEN EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at))/60
                        END) as avg_first_response_minutes,
                    ROUND(COUNT(DISTINCT CASE WHEN c.status = 1 THEN c.id END) * 100.0 /
                        NULLIF(COUNT(DISTINCT c.id), 0), 2) as resolution_rate
                FROM channel_api ch
                LEFT JOIN conversations c ON ch.id = c.inbox_id AND c.created_at BETWEEN $1 AND $2
                LEFT JOIN messages m ON c.id = m.conversation_id
                WHERE ch.account_id = $3
                GROUP BY ch.id, ch.channel_type, ch.name
                ORDER BY total_conversations DESC
            """

            # Volume por canal e dia
            daily_channel_query = """
                SELECT
                    DATE(c.created_at) as date,
                    ch.channel_type,
                    ch.name as channel_name,
                    COUNT(DISTINCT c.id) as conversations,
                    COUNT(m.id) as messages
                FROM conversations c
                LEFT JOIN channel_api ch ON c.inbox_id = ch.id
                LEFT JOIN messages m ON c.id = m.conversation_id
                WHERE c.created_at BETWEEN $1 AND $2 AND ch.account_id = $3
                GROUP BY DATE(c.created_at), ch.id, ch.channel_type, ch.name
                ORDER BY date, conversations DESC
            """

            # Executar queries
            channels_result = await conn.fetch(channels_query, start_date, end_date, CHATWOOT_ACCOUNT_ID)
            daily_result = await conn.fetch(daily_channel_query, start_date, end_date, CHATWOOT_ACCOUNT_ID)

            return {
                "success": True,
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "channel_performance": [dict(row) for row in channels_result],
                "daily_volume": [dict(row) for row in daily_result]
            }

    except Exception as e:
        logger.error(f"❌ Erro ao buscar dados de canais: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main_simple:app", host="0.0.0.0", port=8000, reload=True)
