# 📊 API de Métricas e Avaliações do Chatwoot

## 📋 **V<PERSON><PERSON>**

Este documento fornece todas as informações necessárias para desenvolver uma API externa que acesse os dados de métricas, avaliações e relatórios do sistema Chatwoot.

---

## 🔐 **1. CREDENCIAIS DE ACESSO AO BANCO DE DADOS**

### **1.1 Banco de Dados do Chatwoot**

```yaml
# Configurações de Conexão
Host: localhost (ou IP do servidor)
Porta: 3024 (porta externa mapeada)
Banco: chatwoot
Usuário: chatwoot
Senha: chatwoot123
Schema: public
```

### **1.2 String de Conexão**

```bash
# PostgreSQL Connection String
postgresql://chatwoot:chatwoot123@localhost:3024/chatwoot

# Para aplicações Node.js
DATABASE_URL=postgresql://chatwoot:chatwoot123@localhost:3024/chatwoot

# Para aplicações Python
DATABASE_URL=postgresql://chatwoot:chatwoot123@localhost:3024/chatwoot

# Para aplicações Java
****************************************************************************
```

### **1.3 Configuração Docker**

```yaml
# Container do banco Chatwoot
Container: n8n_chatwoot_postgres
Imagem: ankane/pgvector
Porta Interna: 5432
Porta Externa: 3024
```

---

## 🗄️ **2. ESTRUTURA DAS TABELAS PRINCIPAIS**

### **2.1 Tabela: `conversations`**
**Descrição**: Armazena todas as conversas/atendimentos

```sql
-- Estrutura da tabela conversations
CREATE TABLE conversations (
    id INTEGER PRIMARY KEY,
    account_id INTEGER NOT NULL,
    inbox_id INTEGER NOT NULL,
    status INTEGER NOT NULL DEFAULT 0, -- 0=open, 1=resolved, 2=pending
    assignee_id INTEGER, -- ID do agente responsável
    created_at TIMESTAMP NOT NULL, -- Data/hora de criação
    updated_at TIMESTAMP NOT NULL, -- Última atualização
    contact_id BIGINT, -- ID do contato/cliente
    display_id INTEGER NOT NULL, -- ID de exibição
    contact_last_seen_at TIMESTAMP, -- Última visualização do cliente
    agent_last_seen_at TIMESTAMP, -- Última visualização do agente
    assignee_last_seen_at TIMESTAMP, -- Última visualização do responsável
    first_reply_created_at TIMESTAMP, -- Primeira resposta do agente
    priority INTEGER, -- Prioridade (0=none, 1=low, 2=medium, 3=high, 4=urgent)
    sla_policy_id BIGINT, -- Política de SLA
    waiting_since TIMESTAMP, -- Tempo de espera atual
    uuid UUID NOT NULL UNIQUE,
    identifier VARCHAR,
    last_activity_at TIMESTAMP NOT NULL,
    team_id BIGINT,
    campaign_id BIGINT,
    snoozed_until TIMESTAMP,
    additional_attributes JSONB DEFAULT '{}',
    custom_attributes JSONB DEFAULT '{}',
    cached_label_list TEXT
);
```

**Campos Importantes para Métricas:**
- `status`: Status da conversa (0=aberta, 1=resolvida, 2=pendente)
- `created_at`: Início do atendimento
- `first_reply_created_at`: Tempo da primeira resposta
- `assignee_id`: Agente responsável
- `priority`: Prioridade do atendimento
- `waiting_since`: Tempo de espera atual

### **2.2 Tabela: `csat_survey_responses`**
**Descrição**: Armazena as avaliações de satisfação dos clientes

```sql
-- Estrutura da tabela csat_survey_responses
CREATE TABLE csat_survey_responses (
    id BIGINT PRIMARY KEY,
    account_id BIGINT NOT NULL,
    conversation_id BIGINT NOT NULL, -- Referência à conversa avaliada
    message_id BIGINT NOT NULL, -- Mensagem que solicitou avaliação
    rating INTEGER NOT NULL, -- Nota de 1 a 5
    feedback_message TEXT, -- Comentário opcional do cliente
    contact_id BIGINT NOT NULL, -- Cliente que avaliou
    assigned_agent_id BIGINT, -- Agente que foi avaliado
    created_at TIMESTAMP NOT NULL, -- Data/hora da avaliação
    updated_at TIMESTAMP NOT NULL
);
```

**Campos Importantes:**
- `rating`: Nota de 1 a 5 estrelas
- `feedback_message`: Comentário do cliente
- `assigned_agent_id`: Agente avaliado
- `conversation_id`: Conversa avaliada
- `created_at`: Data da avaliação

### **2.3 Tabela: `messages`**
**Descrição**: Todas as mensagens trocadas nas conversas

```sql
-- Estrutura da tabela messages
CREATE TABLE messages (
    id INTEGER PRIMARY KEY,
    content TEXT, -- Conteúdo da mensagem
    account_id INTEGER NOT NULL,
    inbox_id INTEGER NOT NULL,
    conversation_id INTEGER NOT NULL, -- Referência à conversa
    message_type INTEGER NOT NULL, -- 0=incoming, 1=outgoing, 2=activity, 3=template
    created_at TIMESTAMP NOT NULL, -- Data/hora da mensagem
    updated_at TIMESTAMP NOT NULL,
    private BOOLEAN NOT NULL DEFAULT FALSE, -- Mensagem privada (nota interna)
    status INTEGER DEFAULT 0, -- Status da mensagem
    source_id VARCHAR, -- ID da fonte externa
    content_type INTEGER NOT NULL DEFAULT 0, -- Tipo de conteúdo
    content_attributes JSON DEFAULT '{}', -- Atributos do conteúdo
    sender_type VARCHAR, -- Tipo do remetente (User, Contact, etc.)
    sender_id BIGINT, -- ID do remetente
    external_source_ids JSONB DEFAULT '{}',
    additional_attributes JSONB DEFAULT '{}',
    processed_message_content TEXT,
    sentiment JSONB DEFAULT '{}'
);
```

**Campos Importantes:**
- `message_type`: Tipo da mensagem (0=recebida, 1=enviada, 2=atividade)
- `sender_type`: Tipo do remetente
- `created_at`: Timestamp da mensagem
- `private`: Se é nota interna ou mensagem pública

### **2.4 Tabela: `reporting_events`**
**Descrição**: Eventos específicos para relatórios e métricas

```sql
-- Estrutura da tabela reporting_events
CREATE TABLE reporting_events (
    id BIGINT PRIMARY KEY,
    name VARCHAR, -- Nome do evento (ex: 'conversation_resolved')
    value DOUBLE PRECISION, -- Valor da métrica
    account_id INTEGER,
    inbox_id INTEGER,
    user_id INTEGER, -- Agente relacionado
    conversation_id INTEGER, -- Conversa relacionada
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    value_in_business_hours DOUBLE PRECISION, -- Valor em horário comercial
    event_start_time TIMESTAMP, -- Início do evento
    event_end_time TIMESTAMP -- Fim do evento
);
```

### **2.5 Tabelas Auxiliares Importantes**

```sql
-- Tabela de usuários/agentes
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    account_id INTEGER NOT NULL,
    name VARCHAR NOT NULL,
    email VARCHAR NOT NULL,
    role INTEGER NOT NULL, -- 0=agent, 1=admin
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

-- Tabela de contas
CREATE TABLE accounts (
    id INTEGER PRIMARY KEY,
    name VARCHAR NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

-- Tabela de contatos
CREATE TABLE contacts (
    id BIGINT PRIMARY KEY,
    account_id INTEGER NOT NULL,
    name VARCHAR,
    email VARCHAR,
    phone_number VARCHAR,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

-- Tabela de inboxes (canais de atendimento)
CREATE TABLE inboxes (
    id INTEGER PRIMARY KEY,
    account_id INTEGER NOT NULL,
    name VARCHAR NOT NULL,
    channel_type VARCHAR NOT NULL, -- 'Channel::WebWidget', 'Channel::WhatsApp', etc.
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);
```

---

## 📊 **3. QUERIES ESSENCIAIS PARA MÉTRICAS**

### **3.1 Tempo Médio de Primeira Resposta**

```sql
-- Tempo médio de primeira resposta por agente (em minutos)
SELECT 
    u.name as agent_name,
    u.id as agent_id,
    COUNT(c.id) as total_conversations,
    AVG(
        EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at)) / 60
    ) as avg_first_response_time_minutes
FROM conversations c
JOIN users u ON c.assignee_id = u.id
WHERE c.first_reply_created_at IS NOT NULL
    AND c.created_at >= '2024-01-01'
    AND c.account_id = 1 -- Substitua pelo ID da conta
GROUP BY u.id, u.name
ORDER BY avg_first_response_time_minutes ASC;
```

### **3.2 Avaliações de Satisfação**

```sql
-- Relatório de satisfação por agente
SELECT 
    u.name as agent_name,
    u.id as agent_id,
    COUNT(csr.id) as total_ratings,
    AVG(csr.rating) as average_rating,
    COUNT(CASE WHEN csr.rating >= 4 THEN 1 END) as positive_ratings,
    COUNT(CASE WHEN csr.rating <= 2 THEN 1 END) as negative_ratings,
    ROUND(
        (COUNT(CASE WHEN csr.rating >= 4 THEN 1 END) * 100.0 / COUNT(csr.id)), 2
    ) as satisfaction_percentage
FROM csat_survey_responses csr
JOIN users u ON csr.assigned_agent_id = u.id
WHERE csr.created_at >= '2024-01-01'
    AND csr.account_id = 1
GROUP BY u.id, u.name
ORDER BY average_rating DESC;
```

### **3.3 Volume de Atendimentos**

```sql
-- Volume de conversas por período e status
SELECT 
    DATE(c.created_at) as date,
    COUNT(*) as total_conversations,
    COUNT(CASE WHEN c.status = 0 THEN 1 END) as open_conversations,
    COUNT(CASE WHEN c.status = 1 THEN 1 END) as resolved_conversations,
    COUNT(CASE WHEN c.status = 2 THEN 1 END) as pending_conversations,
    AVG(
        CASE 
            WHEN c.first_reply_created_at IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at)) / 60
        END
    ) as avg_response_time_minutes
FROM conversations c
WHERE c.created_at >= '2024-01-01'
    AND c.account_id = 1
GROUP BY DATE(c.created_at)
ORDER BY date DESC;
```

### **3.4 Performance por Canal**

```sql
-- Métricas por canal de atendimento
SELECT 
    i.name as inbox_name,
    i.channel_type,
    COUNT(c.id) as total_conversations,
    AVG(
        EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at)) / 60
    ) as avg_first_response_minutes,
    COUNT(csr.id) as total_ratings,
    AVG(csr.rating) as average_rating
FROM conversations c
JOIN inboxes i ON c.inbox_id = i.id
LEFT JOIN csat_survey_responses csr ON c.id = csr.conversation_id
WHERE c.created_at >= '2024-01-01'
    AND c.account_id = 1
GROUP BY i.id, i.name, i.channel_type
ORDER BY total_conversations DESC;
```

---

## 🚀 **4. EXEMPLOS DE IMPLEMENTAÇÃO**

### **4.1 Node.js com PostgreSQL**

```javascript
const { Pool } = require('pg');

// Configuração da conexão
const pool = new Pool({
    user: 'chatwoot',
    host: 'localhost',
    database: 'chatwoot',
    password: 'chatwoot123',
    port: 3024,
});

// Função para buscar métricas de satisfação
async function getSatisfactionMetrics(accountId, startDate, endDate) {
    const query = `
        SELECT
            u.name as agent_name,
            u.id as agent_id,
            COUNT(csr.id) as total_ratings,
            AVG(csr.rating) as average_rating,
            COUNT(CASE WHEN csr.rating >= 4 THEN 1 END) as positive_ratings,
            COUNT(CASE WHEN csr.rating <= 2 THEN 1 END) as negative_ratings
        FROM csat_survey_responses csr
        JOIN users u ON csr.assigned_agent_id = u.id
        WHERE csr.created_at BETWEEN $1 AND $2
            AND csr.account_id = $3
        GROUP BY u.id, u.name
        ORDER BY average_rating DESC
    `;

    try {
        const result = await pool.query(query, [startDate, endDate, accountId]);
        return result.rows;
    } catch (error) {
        console.error('Erro ao buscar métricas:', error);
        throw error;
    }
}

// Função para buscar tempo de resposta
async function getResponseTimeMetrics(accountId, startDate, endDate) {
    const query = `
        SELECT
            u.name as agent_name,
            COUNT(c.id) as total_conversations,
            AVG(
                EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at)) / 60
            ) as avg_first_response_time_minutes
        FROM conversations c
        JOIN users u ON c.assignee_id = u.id
        WHERE c.first_reply_created_at IS NOT NULL
            AND c.created_at BETWEEN $1 AND $2
            AND c.account_id = $3
        GROUP BY u.id, u.name
        ORDER BY avg_first_response_time_minutes ASC
    `;

    try {
        const result = await pool.query(query, [startDate, endDate, accountId]);
        return result.rows;
    } catch (error) {
        console.error('Erro ao buscar tempo de resposta:', error);
        throw error;
    }
}

// Exemplo de uso
async function main() {
    try {
        const accountId = 1;
        const startDate = '2024-01-01';
        const endDate = '2024-12-31';

        const satisfactionData = await getSatisfactionMetrics(accountId, startDate, endDate);
        const responseTimeData = await getResponseTimeMetrics(accountId, startDate, endDate);

        console.log('Métricas de Satisfação:', satisfactionData);
        console.log('Métricas de Tempo de Resposta:', responseTimeData);
    } catch (error) {
        console.error('Erro:', error);
    }
}
```

### **4.2 Python com psycopg2**

```python
import psycopg2
import pandas as pd
from datetime import datetime, timedelta

# Configuração da conexão
def get_connection():
    return psycopg2.connect(
        host="localhost",
        port=3024,
        database="chatwoot",
        user="chatwoot",
        password="chatwoot123"
    )

# Função para buscar métricas de satisfação
def get_satisfaction_metrics(account_id, start_date, end_date):
    query = """
        SELECT
            u.name as agent_name,
            u.id as agent_id,
            COUNT(csr.id) as total_ratings,
            AVG(csr.rating) as average_rating,
            COUNT(CASE WHEN csr.rating >= 4 THEN 1 END) as positive_ratings,
            COUNT(CASE WHEN csr.rating <= 2 THEN 1 END) as negative_ratings,
            ROUND(
                (COUNT(CASE WHEN csr.rating >= 4 THEN 1 END) * 100.0 / COUNT(csr.id)), 2
            ) as satisfaction_percentage
        FROM csat_survey_responses csr
        JOIN users u ON csr.assigned_agent_id = u.id
        WHERE csr.created_at BETWEEN %s AND %s
            AND csr.account_id = %s
        GROUP BY u.id, u.name
        ORDER BY average_rating DESC
    """

    conn = get_connection()
    try:
        df = pd.read_sql_query(query, conn, params=[start_date, end_date, account_id])
        return df
    finally:
        conn.close()

# Função para buscar volume de conversas
def get_conversation_volume(account_id, start_date, end_date):
    query = """
        SELECT
            DATE(c.created_at) as date,
            COUNT(*) as total_conversations,
            COUNT(CASE WHEN c.status = 0 THEN 1 END) as open_conversations,
            COUNT(CASE WHEN c.status = 1 THEN 1 END) as resolved_conversations,
            COUNT(CASE WHEN c.status = 2 THEN 1 END) as pending_conversations
        FROM conversations c
        WHERE c.created_at BETWEEN %s AND %s
            AND c.account_id = %s
        GROUP BY DATE(c.created_at)
        ORDER BY date DESC
    """

    conn = get_connection()
    try:
        df = pd.read_sql_query(query, conn, params=[start_date, end_date, account_id])
        return df
    finally:
        conn.close()

# Exemplo de uso
if __name__ == "__main__":
    account_id = 1
    start_date = "2024-01-01"
    end_date = "2024-12-31"

    # Buscar métricas
    satisfaction_df = get_satisfaction_metrics(account_id, start_date, end_date)
    volume_df = get_conversation_volume(account_id, start_date, end_date)

    print("Métricas de Satisfação:")
    print(satisfaction_df)

    print("\nVolume de Conversas:")
    print(volume_df)
```

---

## 🔍 **5. QUERIES AVANÇADAS PARA RELATÓRIOS**

### **5.1 Relatório Completo de Performance**

```sql
-- Relatório completo de performance por agente
WITH agent_stats AS (
    SELECT
        u.id as agent_id,
        u.name as agent_name,
        COUNT(DISTINCT c.id) as total_conversations,
        COUNT(DISTINCT CASE WHEN c.status = 1 THEN c.id END) as resolved_conversations,
        AVG(
            CASE
                WHEN c.first_reply_created_at IS NOT NULL
                THEN EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at)) / 60
            END
        ) as avg_first_response_minutes,
        COUNT(DISTINCT m.id) as total_messages_sent
    FROM users u
    LEFT JOIN conversations c ON u.id = c.assignee_id
        AND c.created_at >= '2024-01-01'
        AND c.account_id = 1
    LEFT JOIN messages m ON u.id = m.sender_id
        AND m.sender_type = 'User'
        AND m.message_type = 1 -- outgoing messages
        AND m.created_at >= '2024-01-01'
        AND m.account_id = 1
    WHERE u.account_id = 1
        AND u.role = 0 -- agents only
    GROUP BY u.id, u.name
),
satisfaction_stats AS (
    SELECT
        csr.assigned_agent_id as agent_id,
        COUNT(csr.id) as total_ratings,
        AVG(csr.rating) as average_rating,
        COUNT(CASE WHEN csr.rating >= 4 THEN 1 END) as positive_ratings,
        COUNT(CASE WHEN csr.rating <= 2 THEN 1 END) as negative_ratings
    FROM csat_survey_responses csr
    WHERE csr.created_at >= '2024-01-01'
        AND csr.account_id = 1
    GROUP BY csr.assigned_agent_id
)
SELECT
    a.agent_name,
    a.total_conversations,
    a.resolved_conversations,
    ROUND(
        (a.resolved_conversations * 100.0 / NULLIF(a.total_conversations, 0)), 2
    ) as resolution_rate_percentage,
    ROUND(a.avg_first_response_minutes, 2) as avg_first_response_minutes,
    a.total_messages_sent,
    COALESCE(s.total_ratings, 0) as total_ratings,
    ROUND(COALESCE(s.average_rating, 0), 2) as average_rating,
    COALESCE(s.positive_ratings, 0) as positive_ratings,
    COALESCE(s.negative_ratings, 0) as negative_ratings,
    ROUND(
        (COALESCE(s.positive_ratings, 0) * 100.0 / NULLIF(s.total_ratings, 0)), 2
    ) as satisfaction_percentage
FROM agent_stats a
LEFT JOIN satisfaction_stats s ON a.agent_id = s.agent_id
WHERE a.total_conversations > 0
ORDER BY a.total_conversations DESC;
```

### **5.2 Análise de Tendências Temporais**

```sql
-- Análise de tendências por semana
SELECT
    DATE_TRUNC('week', c.created_at) as week_start,
    COUNT(*) as total_conversations,
    COUNT(CASE WHEN c.status = 1 THEN 1 END) as resolved_conversations,
    AVG(
        CASE
            WHEN c.first_reply_created_at IS NOT NULL
            THEN EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at)) / 60
        END
    ) as avg_response_time_minutes,
    COUNT(csr.id) as total_ratings,
    AVG(csr.rating) as average_rating
FROM conversations c
LEFT JOIN csat_survey_responses csr ON c.id = csr.conversation_id
WHERE c.created_at >= '2024-01-01'
    AND c.account_id = 1
GROUP BY DATE_TRUNC('week', c.created_at)
ORDER BY week_start DESC;
```

### **5.3 Análise por Horário**

```sql
-- Análise de volume por hora do dia
SELECT
    EXTRACT(HOUR FROM c.created_at) as hour_of_day,
    COUNT(*) as total_conversations,
    AVG(
        CASE
            WHEN c.first_reply_created_at IS NOT NULL
            THEN EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at)) / 60
        END
    ) as avg_response_time_minutes
FROM conversations c
WHERE c.created_at >= '2024-01-01'
    AND c.account_id = 1
GROUP BY EXTRACT(HOUR FROM c.created_at)
ORDER BY hour_of_day;
```

---

## ⚠️ **6. CONSIDERAÇÕES IMPORTANTES**

### **6.1 Performance e Otimização**

```sql
-- Índices recomendados para melhor performance
CREATE INDEX IF NOT EXISTS idx_conversations_account_created
ON conversations(account_id, created_at);

CREATE INDEX IF NOT EXISTS idx_conversations_assignee_created
ON conversations(assignee_id, created_at);

CREATE INDEX IF NOT EXISTS idx_csat_account_created
ON csat_survey_responses(account_id, created_at);

CREATE INDEX IF NOT EXISTS idx_csat_agent_created
ON csat_survey_responses(assigned_agent_id, created_at);

CREATE INDEX IF NOT EXISTS idx_messages_conversation_created
ON messages(conversation_id, created_at);
```

### **6.2 Filtros Importantes**

```sql
-- Sempre filtrar por account_id para multi-tenancy
WHERE account_id = ?

-- Usar intervalos de data para performance
WHERE created_at BETWEEN ? AND ?

-- Filtrar apenas conversas com primeira resposta para métricas de tempo
WHERE first_reply_created_at IS NOT NULL
```

### **6.3 Status das Conversas**

```sql
-- Status possíveis das conversas
-- 0 = Open (Aberta)
-- 1 = Resolved (Resolvida)
-- 2 = Pending (Pendente)
-- 3 = Snoozed (Adiada)
```

### **6.4 Tipos de Mensagem**

```sql
-- Tipos de mensagem
-- 0 = Incoming (Recebida do cliente)
-- 1 = Outgoing (Enviada pelo agente)
-- 2 = Activity (Atividade do sistema)
-- 3 = Template (Mensagem template)
```

---

## 🔒 **7. SEGURANÇA E BOAS PRÁTICAS**

### **7.1 Conexão Segura**

```javascript
// Use SSL em produção
const pool = new Pool({
    user: 'chatwoot',
    host: 'localhost',
    database: 'chatwoot',
    password: 'chatwoot123',
    port: 3024,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    max: 20, // máximo de conexões no pool
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
});
```

### **7.2 Validação de Dados**

```javascript
// Sempre validar e sanitizar parâmetros
function validateAccountId(accountId) {
    const id = parseInt(accountId);
    if (isNaN(id) || id <= 0) {
        throw new Error('Account ID inválido');
    }
    return id;
}

function validateDateRange(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        throw new Error('Datas inválidas');
    }

    if (start > end) {
        throw new Error('Data inicial deve ser anterior à data final');
    }

    return { start, end };
}
```

### **7.3 Rate Limiting**

```javascript
// Implementar rate limiting para evitar sobrecarga
const rateLimit = require('express-rate-limit');

const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutos
    max: 100, // máximo 100 requests por IP
    message: 'Muitas requisições, tente novamente em 15 minutos'
});

app.use('/api/metrics', limiter);
```

---

## 📚 **8. EXEMPLOS DE ENDPOINTS DA API**

### **8.1 GET /api/metrics/satisfaction**
**Descrição**: Retorna métricas de satisfação

```javascript
app.get('/api/metrics/satisfaction', async (req, res) => {
    try {
        const { accountId, startDate, endDate, agentId } = req.query;

        // Validações
        const validAccountId = validateAccountId(accountId);
        const { start, end } = validateDateRange(startDate, endDate);

        // Query
        let query = `
            SELECT
                u.name as agent_name,
                u.id as agent_id,
                COUNT(csr.id) as total_ratings,
                AVG(csr.rating) as average_rating,
                COUNT(CASE WHEN csr.rating >= 4 THEN 1 END) as positive_ratings,
                COUNT(CASE WHEN csr.rating <= 2 THEN 1 END) as negative_ratings,
                ROUND(
                    (COUNT(CASE WHEN csr.rating >= 4 THEN 1 END) * 100.0 / COUNT(csr.id)), 2
                ) as satisfaction_percentage
            FROM csat_survey_responses csr
            JOIN users u ON csr.assigned_agent_id = u.id
            WHERE csr.created_at BETWEEN $1 AND $2
                AND csr.account_id = $3
        `;

        const params = [start, end, validAccountId];

        if (agentId) {
            query += ' AND csr.assigned_agent_id = $4';
            params.push(parseInt(agentId));
        }

        query += ' GROUP BY u.id, u.name ORDER BY average_rating DESC';

        const result = await pool.query(query, params);

        res.json({
            success: true,
            data: result.rows,
            period: { startDate, endDate },
            totalAgents: result.rows.length
        });

    } catch (error) {
        console.error('Erro ao buscar métricas de satisfação:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor'
        });
    }
});
```

### **8.2 GET /api/metrics/response-time**
**Descrição**: Retorna métricas de tempo de resposta

```javascript
app.get('/api/metrics/response-time', async (req, res) => {
    try {
        const { accountId, startDate, endDate, agentId } = req.query;

        const validAccountId = validateAccountId(accountId);
        const { start, end } = validateDateRange(startDate, endDate);

        let query = `
            SELECT
                u.name as agent_name,
                u.id as agent_id,
                COUNT(c.id) as total_conversations,
                AVG(
                    EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at)) / 60
                ) as avg_first_response_time_minutes,
                MIN(
                    EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at)) / 60
                ) as min_response_time_minutes,
                MAX(
                    EXTRACT(EPOCH FROM (c.first_reply_created_at - c.created_at)) / 60
                ) as max_response_time_minutes
            FROM conversations c
            JOIN users u ON c.assignee_id = u.id
            WHERE c.first_reply_created_at IS NOT NULL
                AND c.created_at BETWEEN $1 AND $2
                AND c.account_id = $3
        `;

        const params = [start, end, validAccountId];

        if (agentId) {
            query += ' AND c.assignee_id = $4';
            params.push(parseInt(agentId));
        }

        query += ' GROUP BY u.id, u.name ORDER BY avg_first_response_time_minutes ASC';

        const result = await pool.query(query, params);

        res.json({
            success: true,
            data: result.rows,
            period: { startDate, endDate },
            totalAgents: result.rows.length
        });

    } catch (error) {
        console.error('Erro ao buscar métricas de tempo de resposta:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor'
        });
    }
});
```

### **8.3 GET /api/metrics/dashboard**
**Descrição**: Retorna um resumo geral para dashboard

```javascript
app.get('/api/metrics/dashboard', async (req, res) => {
    try {
        const { accountId, startDate, endDate } = req.query;

        const validAccountId = validateAccountId(accountId);
        const { start, end } = validateDateRange(startDate, endDate);

        // Query para métricas gerais
        const summaryQuery = `
            SELECT
                COUNT(*) as total_conversations,
                COUNT(CASE WHEN status = 1 THEN 1 END) as resolved_conversations,
                COUNT(CASE WHEN status = 0 THEN 1 END) as open_conversations,
                COUNT(CASE WHEN status = 2 THEN 1 END) as pending_conversations,
                AVG(
                    CASE
                        WHEN first_reply_created_at IS NOT NULL
                        THEN EXTRACT(EPOCH FROM (first_reply_created_at - created_at)) / 60
                    END
                ) as avg_first_response_time_minutes,
                COUNT(DISTINCT assignee_id) as active_agents
            FROM conversations
            WHERE created_at BETWEEN $1 AND $2
                AND account_id = $3
        `;

        // Query para satisfação geral
        const satisfactionQuery = `
            SELECT
                COUNT(*) as total_ratings,
                AVG(rating) as average_rating,
                COUNT(CASE WHEN rating >= 4 THEN 1 END) as positive_ratings,
                COUNT(CASE WHEN rating <= 2 THEN 1 END) as negative_ratings
            FROM csat_survey_responses
            WHERE created_at BETWEEN $1 AND $2
                AND account_id = $3
        `;

        const [summaryResult, satisfactionResult] = await Promise.all([
            pool.query(summaryQuery, [start, end, validAccountId]),
            pool.query(satisfactionQuery, [start, end, validAccountId])
        ]);

        const summary = summaryResult.rows[0];
        const satisfaction = satisfactionResult.rows[0];

        // Calcular percentuais
        const resolutionRate = summary.total_conversations > 0
            ? (summary.resolved_conversations * 100 / summary.total_conversations).toFixed(2)
            : 0;

        const satisfactionRate = satisfaction.total_ratings > 0
            ? (satisfaction.positive_ratings * 100 / satisfaction.total_ratings).toFixed(2)
            : 0;

        res.json({
            success: true,
            data: {
                period: { startDate, endDate },
                conversations: {
                    total: parseInt(summary.total_conversations),
                    resolved: parseInt(summary.resolved_conversations),
                    open: parseInt(summary.open_conversations),
                    pending: parseInt(summary.pending_conversations),
                    resolutionRate: parseFloat(resolutionRate)
                },
                responseTime: {
                    averageMinutes: summary.avg_first_response_time_minutes
                        ? parseFloat(summary.avg_first_response_time_minutes).toFixed(2)
                        : null
                },
                satisfaction: {
                    totalRatings: parseInt(satisfaction.total_ratings),
                    averageRating: satisfaction.average_rating
                        ? parseFloat(satisfaction.average_rating).toFixed(2)
                        : null,
                    positiveRatings: parseInt(satisfaction.positive_ratings),
                    negativeRatings: parseInt(satisfaction.negative_ratings),
                    satisfactionRate: parseFloat(satisfactionRate)
                },
                agents: {
                    active: parseInt(summary.active_agents)
                }
            }
        });

    } catch (error) {
        console.error('Erro ao buscar dashboard:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor'
        });
    }
});
```

---

## 🧪 **9. TESTES E VALIDAÇÃO**

### **9.1 Teste de Conexão**

```bash
# Teste de conexão direta ao banco
docker exec -it n8n_chatwoot_postgres psql -U chatwoot -d chatwoot -c "SELECT COUNT(*) FROM conversations;"

# Teste de conectividade externa
psql -h localhost -p 3024 -U chatwoot -d chatwoot -c "SELECT version();"
```

### **9.2 Queries de Validação**

```sql
-- Verificar se há dados nas tabelas principais
SELECT
    'conversations' as table_name, COUNT(*) as record_count
FROM conversations
UNION ALL
SELECT
    'csat_survey_responses' as table_name, COUNT(*) as record_count
FROM csat_survey_responses
UNION ALL
SELECT
    'messages' as table_name, COUNT(*) as record_count
FROM messages
UNION ALL
SELECT
    'users' as table_name, COUNT(*) as record_count
FROM users;

-- Verificar integridade dos dados
SELECT
    COUNT(*) as total_conversations,
    COUNT(assignee_id) as conversations_with_agent,
    COUNT(first_reply_created_at) as conversations_with_first_reply
FROM conversations
WHERE account_id = 1;
```

---

## 📖 **10. DOCUMENTAÇÃO ADICIONAL**

### **10.1 Campos Calculados Úteis**

```sql
-- Tempo de primeira resposta em minutos
EXTRACT(EPOCH FROM (first_reply_created_at - created_at)) / 60 as response_time_minutes

-- Tempo de resolução em horas
EXTRACT(EPOCH FROM (updated_at - created_at)) / 3600 as resolution_time_hours

-- Taxa de satisfação
(COUNT(CASE WHEN rating >= 4 THEN 1 END) * 100.0 / COUNT(rating)) as satisfaction_percentage

-- Taxa de resolução
(COUNT(CASE WHEN status = 1 THEN 1 END) * 100.0 / COUNT(*)) as resolution_percentage
```

### **10.2 Filtros Avançados**

```sql
-- Filtrar apenas horário comercial (9h às 18h, seg-sex)
WHERE EXTRACT(DOW FROM created_at) BETWEEN 1 AND 5  -- Segunda a Sexta
    AND EXTRACT(HOUR FROM created_at) BETWEEN 9 AND 18

-- Filtrar por prioridade
WHERE priority IN (3, 4)  -- High e Urgent

-- Filtrar por canal específico
WHERE inbox_id IN (SELECT id FROM inboxes WHERE channel_type = 'Channel::WhatsApp')
```

### **10.3 Agregações Úteis**

```sql
-- Mediana do tempo de resposta
PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY
    EXTRACT(EPOCH FROM (first_reply_created_at - created_at)) / 60
) as median_response_time_minutes

-- Percentil 95 do tempo de resposta
PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY
    EXTRACT(EPOCH FROM (first_reply_created_at - created_at)) / 60
) as p95_response_time_minutes
```

---

## ✅ **11. CHECKLIST DE IMPLEMENTAÇÃO**

- [ ] Configurar conexão com o banco de dados
- [ ] Implementar autenticação e autorização
- [ ] Criar endpoints básicos (satisfação, tempo de resposta, volume)
- [ ] Implementar validação de parâmetros
- [ ] Adicionar rate limiting
- [ ] Implementar cache para queries pesadas
- [ ] Criar testes unitários
- [ ] Documentar API (Swagger/OpenAPI)
- [ ] Implementar logging e monitoramento
- [ ] Configurar SSL para produção
- [ ] Otimizar queries com índices apropriados
- [ ] Implementar paginação para resultados grandes

---

**📝 Documento criado em:** 2025-01-30
**🔄 Última atualização:** 2025-01-30
**👨‍💻 Autor:** Augment Agent
**📋 Status:** Completo e Pronto para Implementação
