#!/bin/bash

# Script para atualizar containers com o novo serviço Chatwoot
# Autor: Augment Agent
# Data: 2025-01-30

set -e

echo "🚀 Iniciando atualização dos containers..."
echo "================================================"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar se Docker está rodando
if ! docker info > /dev/null 2>&1; then
    log_error "Docker não está rodando. Por favor, inicie o Docker primeiro."
    exit 1
fi

log_success "Docker está rodando"

# Verificar se docker-compose está disponível
if ! command -v docker-compose &> /dev/null; then
    log_error "docker-compose não encontrado. Por favor, instale o docker-compose."
    exit 1
fi

log_success "docker-compose encontrado"

# Mostrar status atual
log_info "Status atual dos containers:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo ""
log_info "Parando container atual..."

# Parar o container atual
docker-compose down

log_success "Container parado"

# Fazer backup da imagem atual (opcional)
log_info "Fazendo backup da imagem atual..."
docker tag amvox_relatorio_amvox-reports:latest amvox_relatorio_amvox-reports:backup-$(date +%Y%m%d-%H%M%S) || true

# Limpar imagens antigas (opcional)
log_info "Limpando imagens não utilizadas..."
docker image prune -f

# Reconstruir a imagem
log_info "Reconstruindo a imagem com as novas funcionalidades..."
docker-compose build --no-cache

log_success "Imagem reconstruída com sucesso"

# Iniciar os containers
log_info "Iniciando containers atualizados..."
docker-compose up -d

log_success "Containers iniciados"

# Aguardar um pouco para os containers iniciarem
log_info "Aguardando containers iniciarem..."
sleep 10

# Verificar status dos containers
log_info "Verificando status dos containers:"
docker-compose ps

# Verificar logs para possíveis erros
log_info "Verificando logs recentes..."
docker-compose logs --tail=20 amvox-reports

# Testar conectividade
log_info "Testando conectividade..."

# Aguardar o serviço ficar disponível
for i in {1..30}; do
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_success "Serviço está respondendo!"
        break
    fi
    if [ $i -eq 30 ]; then
        log_error "Serviço não está respondendo após 30 tentativas"
        log_info "Verificando logs de erro..."
        docker-compose logs --tail=50 amvox-reports
        exit 1
    fi
    echo -n "."
    sleep 2
done

# Testar conexão com Chatwoot
log_info "Testando conexão com Chatwoot..."
if curl -f http://localhost:8000/api/v1/chatwoot/health > /dev/null 2>&1; then
    log_success "Conexão com Chatwoot funcionando!"
else
    log_warning "Conexão com Chatwoot pode ter problemas. Verifique as configurações."
fi

echo ""
echo "================================================"
log_success "Atualização concluída com sucesso!"
echo ""
log_info "Novos recursos disponíveis:"
echo "  📊 Dashboard Chatwoot: http://localhost:8000/chatwoot"
echo "  😊 Satisfação: http://localhost:8000/chatwoot/satisfaction"
echo "  ⏱️  Tempo de Resposta: http://localhost:8000/chatwoot/response-time"
echo "  📈 Volume: http://localhost:8000/chatwoot/volume"
echo "  🏆 Performance: http://localhost:8000/chatwoot/performance"
echo ""
log_info "APIs disponíveis:"
echo "  🔍 Health Check: http://localhost:8000/api/v1/chatwoot/health"
echo "  📊 Dashboard API: http://localhost:8000/api/v1/chatwoot/dashboard"
echo ""
log_info "Para verificar logs em tempo real:"
echo "  docker-compose logs -f amvox-reports"
echo ""
log_info "Para parar os containers:"
echo "  docker-compose down"
echo ""
log_success "Sistema pronto para uso! 🎉"
