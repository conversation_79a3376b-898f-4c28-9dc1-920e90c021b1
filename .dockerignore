# Git
.git
.gitignore
.gitattributes

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
.venv/
.env/

# Environment files
.env
.env.local
.env.production
.env.development

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Temporary files
temp/
tmp/
*.tmp

# Documentation
README.md
DEPLOY.md
docs/

# Scripts
deploy.sh
*.sh

# SSL certificates
nginx/ssl/
*.pem
*.key
*.crt

# Backup files
*.bak
*.backup
backup-*

# Test files
test_*.pdf
*_test.pdf

# Node modules (if any)
node_modules/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml

# Pytest
.pytest_cache/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
