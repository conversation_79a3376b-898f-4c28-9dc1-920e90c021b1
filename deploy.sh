#!/bin/bash

# =====================================================
# SCRIPT DE DEPLOY - AMVOX CALL CENTER ANALYTICS
# =====================================================

set -e  # Parar em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Banner
echo -e "${BLUE}"
echo "=================================================="
echo "   AMVOX CALL CENTER ANALYTICS - DEPLOY"
echo "=================================================="
echo -e "${NC}"

# Verificar se Docker está instalado
if ! command -v docker &> /dev/null; then
    error "Docker não está instalado. Instale o Docker primeiro."
fi

if ! command -v docker-compose &> /dev/null; then
    error "Docker Compose não está instalado. Instale o Docker Compose primeiro."
fi

# Verificar se arquivo .env existe
if [ ! -f .env ]; then
    warning "Arquivo .env não encontrado!"
    info "Copiando .env.production para .env..."
    
    if [ -f .env.production ]; then
        cp .env.production .env
        warning "IMPORTANTE: Edite o arquivo .env com suas configurações reais!"
        warning "Execute: nano .env"
        read -p "Pressione Enter após editar o arquivo .env..."
    else
        error "Arquivo .env.production não encontrado!"
    fi
fi

# Verificar variáveis obrigatórias
log "Verificando configurações..."

if ! grep -q "THREECX_API_URL=" .env || grep -q "SEU-SERVIDOR-3CX" .env; then
    error "Configure THREECX_API_URL no arquivo .env"
fi

if ! grep -q "THREECX_USERNAME=" .env || grep -q "seu_usuario_3cx" .env; then
    error "Configure THREECX_USERNAME no arquivo .env"
fi

if ! grep -q "THREECX_PASSWORD=" .env || grep -q "sua_senha_3cx" .env; then
    error "Configure THREECX_PASSWORD no arquivo .env"
fi

# Criar diretórios necessários
log "Criando diretórios..."
mkdir -p logs temp data nginx/ssl

# Parar containers existentes
log "Parando containers existentes..."
docker-compose down --remove-orphans || true

# Construir imagens
log "Construindo imagens Docker..."
docker-compose build --no-cache

# Iniciar serviços
log "Iniciando serviços..."
docker-compose up -d

# Aguardar inicialização
log "Aguardando inicialização dos serviços..."
sleep 30

# Verificar health check
log "Verificando saúde da aplicação..."
for i in {1..10}; do
    if curl -f http://localhost:8000/health &> /dev/null; then
        log "✅ Aplicação está funcionando!"
        break
    else
        warning "Tentativa $i/10 - Aguardando aplicação..."
        sleep 10
    fi
    
    if [ $i -eq 10 ]; then
        error "❌ Aplicação não respondeu após 10 tentativas"
    fi
done

# Mostrar status
log "Status dos containers:"
docker-compose ps

# Mostrar logs recentes
log "Logs recentes:"
docker-compose logs --tail=20

# Informações finais
echo -e "${GREEN}"
echo "=================================================="
echo "           DEPLOY CONCLUÍDO COM SUCESSO!"
echo "=================================================="
echo -e "${NC}"

echo -e "${BLUE}🌐 Acesso à aplicação:${NC}"
echo "   Local: http://localhost:8000"
echo "   Rede:  http://$(hostname -I | awk '{print $1}'):8000"

echo -e "${BLUE}📊 Endpoints importantes:${NC}"
echo "   Health Check: http://localhost:8000/health"
echo "   Dashboard:    http://localhost:8000/"

echo -e "${BLUE}🔧 Comandos úteis:${NC}"
echo "   Ver logs:     docker-compose logs -f"
echo "   Parar:        docker-compose down"
echo "   Reiniciar:    docker-compose restart"
echo "   Atualizar:    git pull && ./deploy.sh"

echo -e "${YELLOW}⚠️  Lembre-se:${NC}"
echo "   - Configure SSL/TLS para produção"
echo "   - Configure backup dos dados"
echo "   - Monitore os logs regularmente"
echo "   - Mantenha o sistema atualizado"

log "Deploy finalizado! 🎉"
