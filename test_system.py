#!/usr/bin/env python3
"""
Script de teste para verificar se todas as funcionalidades do sistema estão funcionando
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000"

def test_endpoint(endpoint, description):
    """Testa um endpoint específico"""
    try:
        response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success', False):
                print(f"✅ {description}: OK")
                return True
            else:
                print(f"❌ {description}: Falha - {data.get('message', 'Erro desconhecido')}")
                return False
        else:
            print(f"❌ {description}: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ {description}: Erro - {str(e)}")
        return False

def main():
    print("🔍 TESTANDO SISTEMA AMVOX RELATÓRIOS 3CX")
    print("=" * 50)
    
    # Parâmetros de teste
    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    end_date = datetime.now().strftime('%Y-%m-%d')
    queues = "802,803"
    
    params = f"?start_date={start_date}&end_date={end_date}&queues={queues}"
    
    # Lista de testes
    tests = [
        ("/health", "Health Check"),
        (f"/api/distribution{params}", "Distribuição de Chamadas"),
        (f"/api/satisfaction{params}", "Pesquisa de Satisfação"),
        (f"/api/timing{params}", "Métricas de Timing"),
        (f"/api/agents{params}", "Lista de Agentes"),
        (f"/api/executive-summary{params}", "Resumo Executivo com IA"),
    ]
    
    # Executa testes
    passed = 0
    total = len(tests)
    
    for endpoint, description in tests:
        if test_endpoint(endpoint, description):
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 RESULTADO: {passed}/{total} testes passaram")
    
    if passed == total:
        print("🎉 SISTEMA FUNCIONANDO PERFEITAMENTE!")
    elif passed >= total * 0.8:
        print("⚠️  Sistema funcionando com pequenos problemas")
    else:
        print("🚨 Sistema com problemas críticos")
    
    # Teste específico de agente
    print("\n🔍 Testando análise individual de agente...")
    try:
        # Primeiro pega a lista de agentes
        response = requests.get(f"{BASE_URL}/api/agents{params}", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('data'):
                agent_name = data['data'][0]['Agente']
                agent_endpoint = f"/api/agent/{agent_name}{params}"
                
                if test_endpoint(agent_endpoint, f"Análise do Agente {agent_name}"):
                    print("✅ Análise individual de agentes: OK")
                else:
                    print("❌ Análise individual de agentes: Falha")
            else:
                print("❌ Não foi possível obter lista de agentes")
        else:
            print("❌ Erro ao acessar lista de agentes")
    except Exception as e:
        print(f"❌ Erro no teste de agente: {str(e)}")
    
    print("\n🌐 Para acessar o sistema: http://localhost:8000")
    print("📋 Dashboard com todas as funcionalidades disponível!")

if __name__ == "__main__":
    main()
