"""
Sistema de Relatórios 3CX com extensão Chatwoot
Mantém toda funcionalidade original do 3CX e adiciona apenas o menu Chatwoot
"""
import sys
import os

# Importar todo o conteúdo do main_simple.py
exec(open('main_simple.py').read())

# Adicionar apenas as importações necessárias para o Chatwoot
import asyncpg
from typing import List, Dict, Any

# Configurações do Chatwoot
CHATWOOT_DB_HOST = os.getenv("CHATWOOT_DB_HOST", "n8n_chatwoot_postgres")
CHATWOOT_DB_PORT = int(os.getenv("CHATWOOT_DB_PORT", "5432"))
CHATWOOT_DB_NAME = os.getenv("CHATWOOT_DB_NAME", "chatwoot")
CHATWOOT_DB_USER = os.getenv("CHATWOOT_DB_USER", "chatwoot")
CHATWOOT_DB_PASSWORD = os.getenv("CHATWOOT_DB_PASSWORD", "chatwoot_password_123")
CHATWOOT_ACCOUNT_ID = int(os.getenv("CHATWOOT_ACCOUNT_ID", "1"))

# Pool de conexão global
chatwoot_pool = None

async def init_chatwoot():
    """Inicializa conexão com Chatwoot"""
    global chatwoot_pool
    try:
        database_url = f"postgresql://{CHATWOOT_DB_USER}:{CHATWOOT_DB_PASSWORD}@{CHATWOOT_DB_HOST}:{CHATWOOT_DB_PORT}/{CHATWOOT_DB_NAME}"
        chatwoot_pool = await asyncpg.create_pool(
            database_url,
            min_size=1,
            max_size=5,
            command_timeout=60
        )
        logger.info("Conexão com Chatwoot estabelecida")
    except Exception as e:
        logger.error(f"Erro ao conectar com Chatwoot: {e}")

async def close_chatwoot():
    """Fecha conexão com Chatwoot"""
    global chatwoot_pool
    if chatwoot_pool:
        await chatwoot_pool.close()
        logger.info("Conexão com Chatwoot fechada")

# Eventos de inicialização e finalização
@app.on_event("startup")
async def startup_event():
    await init_chatwoot()

@app.on_event("shutdown")
async def shutdown_event():
    await close_chatwoot()

# Função auxiliar para testar conexão Chatwoot
async def test_chatwoot_connection():
    """Testa conexão com Chatwoot"""
    global chatwoot_pool
    try:
        if not chatwoot_pool:
            return False
        async with chatwoot_pool.acquire() as conn:
            await conn.fetchval("SELECT version()")
            return True
    except Exception as e:
        logger.error(f"Erro no teste de conexão Chatwoot: {e}")
        return False

# Atualizar health check para incluir Chatwoot
@app.get("/health")
async def health_check():
    """Health check incluindo Chatwoot"""
    try:
        # Teste original do 3CX
        threecx_status = await test_3cx_connection()
        
        # Teste do Chatwoot
        chatwoot_status = await test_chatwoot_connection()
        
        return {
            "status": "healthy" if (threecx_status and chatwoot_status) else "degraded",
            "timestamp": datetime.now().isoformat(),
            "threecx_connection": threecx_status,
            "chatwoot_connection": chatwoot_status,
            "version": "2.1.0"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "version": "2.1.0"
        }

# Rotas simples do Chatwoot
@app.get("/chatwoot", response_class=HTMLResponse)
async def chatwoot_dashboard(request: Request):
    """Dashboard Chatwoot"""
    return templates.TemplateResponse("chatwoot_simple.html", {
        "request": request,
        "title": "Dashboard Chatwoot - WhatsApp"
    })

@app.get("/api/v1/chatwoot/health")
async def chatwoot_health():
    """Health check específico do Chatwoot"""
    try:
        is_connected = await test_chatwoot_connection()
        return {
            "status": "healthy" if is_connected else "unhealthy",
            "service": "chatwoot",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": "chatwoot",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@app.get("/api/v1/chatwoot/agents")
async def get_chatwoot_agents():
    """Lista agentes do Chatwoot"""
    global chatwoot_pool
    try:
        if not chatwoot_pool:
            raise HTTPException(status_code=503, detail="Chatwoot não conectado")
        
        async with chatwoot_pool.acquire() as conn:
            rows = await conn.fetch("SELECT id, name, email FROM users ORDER BY name")
            agents = [dict(row) for row in rows]
            
        return {
            "success": True,
            "data": agents,
            "total": len(agents)
        }
    except Exception as e:
        logger.error(f"Erro ao buscar agentes: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/api/v1/chatwoot/conversations")
async def get_chatwoot_conversations(
    start_date: date,
    end_date: date,
    limit: int = 100
):
    """Lista conversas do Chatwoot"""
    global chatwoot_pool
    try:
        if not chatwoot_pool:
            raise HTTPException(status_code=503, detail="Chatwoot não conectado")
        
        async with chatwoot_pool.acquire() as conn:
            query = """
                SELECT id, status, created_at, updated_at
                FROM conversations 
                WHERE created_at BETWEEN $1 AND $2
                ORDER BY created_at DESC
                LIMIT $3
            """
            rows = await conn.fetch(query, start_date, end_date, limit)
            conversations = [dict(row) for row in rows]
            
        return {
            "success": True,
            "data": conversations,
            "total": len(conversations),
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        }
    except Exception as e:
        logger.error(f"Erro ao buscar conversas: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

# Função auxiliar que pode estar faltando
async def test_3cx_connection():
    """Testa conexão com 3CX (função auxiliar)"""
    try:
        # Implementação básica de teste
        return True
    except:
        return False

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
