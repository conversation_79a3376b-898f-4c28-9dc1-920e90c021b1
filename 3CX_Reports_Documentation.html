<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Relatórios 3CX - Documentação</title>
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        
        @media print {
            body { 
                font-family: Arial, sans-serif;
                font-size: 11pt;
                line-height: 1.6;
                color: #333;
            }
            
            h1 {
                color: #2c3e50;
                font-size: 24pt;
                margin-top: 30pt;
                margin-bottom: 20pt;
                border-bottom: 3px solid #3498db;
                padding-bottom: 10pt;
                page-break-before: always;
            }
            
            h1:first-child {
                page-break-before: avoid;
            }
            
            h2 {
                color: #34495e;
                font-size: 18pt;
                margin-top: 25pt;
                margin-bottom: 15pt;
                border-bottom: 2px solid #ecf0f1;
                padding-bottom: 5pt;
            }
            
            h3 {
                color: #2c3e50;
                font-size: 14pt;
                margin-top: 20pt;
                margin-bottom: 10pt;
            }
            
            h4 {
                color: #34495e;
                font-size: 12pt;
                margin-top: 15pt;
                margin-bottom: 8pt;
            }
            
            p {
                margin-bottom: 10pt;
                text-align: justify;
            }
            
            code {
                background-color: #f8f9fa;
                padding: 2pt 4pt;
                border-radius: 3pt;
                font-family: 'Courier New', monospace;
                font-size: 9pt;
            }
            
            pre {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 5pt;
                padding: 10pt;
                margin: 10pt 0;
                overflow-x: auto;
                font-family: 'Courier New', monospace;
                font-size: 9pt;
                line-height: 1.4;
                page-break-inside: avoid;
            }
            
            ul {
                margin: 10pt 0;
                padding-left: 20pt;
            }
            
            li {
                margin-bottom: 5pt;
            }
            
            .print-instructions {
                display: none;
            }
        }
        
        /* Estilos para tela */
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        h2 {
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 5px;
        }
        
        h3 {
            color: #2c3e50;
        }
        
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        
        pre {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
        
        .print-instructions {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .cover-page {
            text-align: center;
            margin: 50px 0;
        }
        
        .toc {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .endpoint {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }
        
        .metric {
            background-color: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 10px;
            margin: 10px 0;
        }
        
        .example {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="print-instructions">
        <h3>📄 Como gerar PDF:</h3>
        <ol>
            <li>Pressione <strong>Ctrl+P</strong> (ou Cmd+P no Mac)</li>
            <li>Selecione <strong>"Salvar como PDF"</strong> como destino</li>
            <li>Configure <strong>margens</strong> para "Mínimas"</li>
            <li>Marque <strong>"Gráficos de fundo"</strong></li>
            <li>Clique em <strong>"Salvar"</strong></li>
        </ol>
        <p><strong>💡 Dica:</strong> Este aviso não aparecerá no PDF final.</p>
    </div>
    
    <div class="cover-page">
        <h1 style="font-size: 28pt; margin-bottom: 20px;">
            📊 Sistema de Relatórios 3CX
        </h1>
        <h2 style="color: #7f8c8d; font-size: 18pt; border: none;">
            Documentação Técnica Completa
        </h2>
        <div style="margin-top: 50px; color: #95a5a6;">
            <p><strong>Versão:</strong> 2.1.0</p>
            <p><strong>Data:</strong> Janeiro 2025</p>
            <p><strong>Equipe:</strong> Amvox Development Team</p>
        </div>
    </div>
    
    <div style="page-break-before: always;"></div>
    
    <div class="toc">
        <h2>📋 Índice</h2>
        <ol>
            <li><a href="#visao-geral">Visão Geral</a></li>
            <li><a href="#arquitetura">Arquitetura do Sistema</a></li>
            <li><a href="#endpoints">Endpoints da API</a></li>
            <li><a href="#tipos-relatorios">Tipos de Relatórios</a></li>
            <li><a href="#metricas">Métricas Avançadas</a></li>
            <li><a href="#modelos-dados">Modelos de Dados</a></li>
            <li><a href="#exemplos">Exemplos de Uso</a></li>
            <li><a href="#configuracao">Configuração</a></li>
            <li><a href="#troubleshooting">Troubleshooting</a></li>
        </ol>
    </div>

    <h1 id="visao-geral">🎯 Visão Geral</h1>
    
    <p>O Sistema de Relatórios 3CX é uma solução completa para análise de performance de call center, oferecendo métricas detalhadas, relatórios customizáveis e insights em tempo real.</p>
    
    <h3>✨ Principais Funcionalidades</h3>
    <ul>
        <li><strong>Relatórios de Distribuição</strong>: Análise de volume e distribuição de chamadas</li>
        <li><strong>Pesquisa de Satisfação</strong>: Métricas de satisfação do cliente</li>
        <li><strong>Métricas de Timing</strong>: ASA, AHT, Service Level, Abandonment Rate</li>
        <li><strong>Performance de Agentes</strong>: Produtividade individual e comparativa</li>
        <li><strong>Análise de SLA</strong>: Compliance e métricas de qualidade</li>
        <li><strong>Relatórios em PDF</strong>: Exportação profissional</li>
        <li><strong>Dados em Tempo Real</strong>: Monitoramento live</li>
    </ul>

    <h1 id="arquitetura">🏗️ Arquitetura do Sistema</h1>
    
    <pre>
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   3CX System    │
│   (React)       │◄──►│   (FastAPI)     │◄──►│   (PBX)         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   PostgreSQL    │
                       │   (Database)    │
                       └─────────────────┘
    </pre>
    
    <h3>🔧 Componentes Principais</h3>
    <ol>
        <li><strong>API de Relatórios</strong> (<code>/api/v1/reports/</code>)</li>
        <li><strong>Cliente 3CX</strong> (<code>ThreeCXClient</code>)</li>
        <li><strong>Calculador de Métricas</strong> (<code>MetricsCalculator</code>)</li>
        <li><strong>Gerador de PDF</strong> (<code>PDFGenerator</code>)</li>
        <li><strong>Serviços Mock</strong> (para desenvolvimento)</li>
    </ol>

    <h1 id="endpoints">🌐 Endpoints da API</h1>
    
    <h2>📊 Relatórios Básicos</h2>
    
    <div class="endpoint">
        <h4>1. Relatório de Distribuição</h4>
        <pre><code>POST /api/v1/reports/distribution
Content-Type: application/json

{
  "start_date": "2025-01-01",
  "end_date": "2025-01-31",
  "queues": [801, 802, 803]
}</code></pre>
        
        <p><strong>Resposta:</strong></p>
        <pre><code>{
  "data": {
    "total_calls": 1250,
    "answered_calls": 1180,
    "abandoned_calls": 70,
    "distribution_by_queue": [
      {
        "queue_id": 801,
        "queue_name": "Suporte Técnico",
        "calls": 450,
        "percentage": 36.0
      }
    ]
  }
}</code></pre>
    </div>
    
    <div class="endpoint">
        <h4>2. Pesquisa de Satisfação</h4>
        <pre><code>POST /api/v1/reports/satisfaction-survey
Content-Type: application/json

{
  "start_date": "2025-01-01",
  "end_date": "2025-01-31",
  "queues": [802]
}</code></pre>
    </div>

    <h2>📈 Métricas Avançadas</h2>
    
    <div class="endpoint">
        <h4>3. Métricas de Timing</h4>
        <pre><code>POST /api/v1/reports/timing-metrics
Content-Type: application/json

{
  "start_date": "2025-01-01",
  "end_date": "2025-01-31",
  "queue_ids": [801, 802],
  "service_level_threshold": 20
}</code></pre>
        
        <p><strong>Resposta:</strong></p>
        <pre><code>{
  "asa": 15.5,
  "aht": 285.2,
  "att": 256.8,
  "acw": 28.5,
  "service_level": 87.3,
  "abandonment_rate": 5.6,
  "total_calls": 1250,
  "answered_calls": 1180,
  "abandoned_calls": 70
}</code></pre>
    </div>

    <h1 id="tipos-relatorios">📊 Tipos de Relatórios</h1>
    
    <h2>1. 📈 Relatório de Distribuição</h2>
    <p><strong>Objetivo</strong>: Analisar volume e distribuição de chamadas por fila</p>
    
    <div class="metric">
        <h4>Métricas Incluídas:</h4>
        <ul>
            <li>Total de chamadas por período</li>
            <li>Distribuição por fila (quantidade e percentual)</li>
            <li>Picos de volume por hora/dia</li>
            <li>Comparativo entre períodos</li>
        </ul>
    </div>
    
    <div class="example">
        <h4>Casos de Uso:</h4>
        <ul>
            <li>Planejamento de capacidade</li>
            <li>Análise de demanda</li>
            <li>Otimização de recursos</li>
        </ul>
    </div>

    <h2>2. 😊 Pesquisa de Satisfação</h2>
    <p><strong>Objetivo</strong>: Medir satisfação do cliente</p>
    
    <div class="metric">
        <h4>Métricas Incluídas:</h4>
        <ul>
            <li>NPS (Net Promoter Score)</li>
            <li>CSAT (Customer Satisfaction)</li>
            <li>Distribuição de notas</li>
            <li>Comentários e feedback</li>
        </ul>
    </div>

    <h2>3. ⏱️ Métricas de Timing</h2>
    <p><strong>Objetivo</strong>: Analisar tempos de atendimento</p>
    
    <div class="metric">
        <h4>Métricas Incluídas:</h4>
        <ul>
            <li><strong>ASA</strong> (Average Speed of Answer): Tempo médio para atender</li>
            <li><strong>AHT</strong> (Average Handle Time): Tempo médio de atendimento</li>
            <li><strong>ATT</strong> (Average Talk Time): Tempo médio de conversa</li>
            <li><strong>ACW</strong> (After Call Work): Tempo pós-chamada</li>
            <li><strong>Service Level</strong>: % chamadas atendidas no prazo</li>
            <li><strong>Abandonment Rate</strong>: Taxa de abandono</li>
        </ul>
    </div>

    <h1 id="metricas">🔢 Modelos de Dados</h1>
    
    <h3>TimingMetrics</h3>
    <pre><code>{
  "asa": 15.5,              // Average Speed of Answer (segundos)
  "aht": 285.2,             // Average Handle Time (segundos)
  "att": 256.8,             // Average Talk Time (segundos)
  "acw": 28.5,              // After Call Work (segundos)
  "service_level": 87.3,    // Service Level (%)
  "abandonment_rate": 5.6,  // Taxa de abandono (%)
  "total_calls": 1250,      // Total de chamadas
  "answered_calls": 1180,   // Chamadas atendidas
  "abandoned_calls": 70     // Chamadas abandonadas
}</code></pre>

    <h3>AgentPerformance</h3>
    <pre><code>{
  "agent_id": "agent_001",
  "agent_name": "Ana Silva",
  "calls_handled": 145,
  "talk_time": 12450.0,
  "break_time": 3600.0,
  "login_time": 28800.0,
  "occupancy_rate": 75.5,
  "productivity_score": 8.7,
  "calls_per_hour": 5.2,
  "average_call_duration": 285.5,
  "first_call_resolution": 89.2
}</code></pre>

    <h1 id="exemplos">💡 Exemplos de Uso</h1>
    
    <div class="example">
        <h3>Exemplo 1: Análise Mensal Completa</h3>
        <pre><code># 1. Obter métricas de timing
timing_response = requests.post('/api/v1/reports/timing-metrics', json={
    "start_date": "2025-01-01",
    "end_date": "2025-01-31",
    "service_level_threshold": 20
})

# 2. Obter performance de agentes
agent_response = requests.post('/api/v1/reports/agent-performance', json={
    "start_date": "2025-01-01",
    "end_date": "2025-01-31"
})

# 3. Gerar PDF consolidado
pdf_response = requests.get('/api/v1/pdf-reports/monthly-summary', params={
    "start_date": "2025-01-01",
    "end_date": "2025-01-31"
})</code></pre>
    </div>

    <h1 id="configuracao">⚙️ Configuração</h1>
    
    <h3>Variáveis de Ambiente</h3>
    <pre><code># 3CX Configuration
THREECX_BASE_URL=https://pbx.example.com
THREECX_API_KEY=your-api-key
THREECX_TENANT_ID=your-tenant-id

# Database
DATABASE_URL=postgresql://user:pass@localhost/amvox

# Reports
REPORTS_CACHE_TTL=300
REPORTS_MAX_PERIOD_DAYS=365</code></pre>

    <h1 id="troubleshooting">🔍 Troubleshooting</h1>
    
    <h3>Problemas Comuns</h3>
    
    <div class="example">
        <h4>1. Erro de Autenticação 3CX</h4>
        <pre><code>HTTPException: 401 Unauthorized</code></pre>
        <p><strong>Solução</strong>: Verificar API_KEY e TENANT_ID</p>
    </div>
    
    <div class="example">
        <h4>2. Timeout na API</h4>
        <pre><code>HTTPException: 504 Gateway Timeout</code></pre>
        <p><strong>Solução</strong>: Reduzir período do relatório ou usar cache</p>
    </div>

    <hr style="margin: 50px 0;">
    
    <div style="text-align: center; color: #666; font-size: 12pt;">
        <p><strong>📅 Última Atualização</strong>: Janeiro 2025</p>
        <p><strong>📝 Versão</strong>: 2.1.0</p>
        <p><strong>👨‍💻 Mantido por</strong>: Equipe de Desenvolvimento Amvox</p>
    </div>
</body>
</html>
