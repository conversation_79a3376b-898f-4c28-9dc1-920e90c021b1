# 🚀 GUIA DE DEPLOY - AMVOX CALL CENTER ANALYTICS

## 📋 Pré-requisitos

### Servidor
- **SO**: Ubuntu 20.04+ / CentOS 7+ / Debian 10+
- **RAM**: <PERSON>ínimo 1GB, Recomendado 2GB+
- **Disco**: Mínimo 2GB livres
- **Rede**: Acesso à internet e ao servidor 3CX

### Software Necessário
```bash
# Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

## 🔧 Configuração Rápida

### 1. Clone o Repositório
```bash
<NAME_EMAIL>:aplopes-dev/amvox_relatorio.git
cd amvox_relatorio
```

### 2. Configure Variáveis de Ambiente
```bash
# Copie o arquivo de exemplo
cp .env.production .env

# Edite com suas configurações
nano .env
```

### 3. Configure as Variáveis OBRIGATÓRIAS
```env
# Substitua pelos dados reais do seu ambiente
THREECX_API_URL=http://IP-DO-SEU-3CX/gdacip/apijson.php
THREECX_USERNAME=usuario_com_acesso_relatorios
THREECX_PASSWORD=senha_do_usuario
SECRET_KEY=chave_secreta_super_forte_123456789
```

### 4. Execute o Deploy
```bash
# Torna o script executável e roda
chmod +x deploy.sh
./deploy.sh
```

## 🌐 Acesso ao Sistema

Após o deploy bem-sucedido:
- **Local**: http://localhost:8000
- **Rede**: http://IP-DO-SERVIDOR:8000
- **Health Check**: http://IP-DO-SERVIDOR:8000/health

## 📊 Variáveis de Ambiente Detalhadas

### Obrigatórias
| Variável | Descrição | Exemplo |
|----------|-----------|---------|
| `THREECX_API_URL` | URL da API do 3CX | `http://*************/gdacip/apijson.php` |
| `THREECX_USERNAME` | Usuário do 3CX | `admin` |
| `THREECX_PASSWORD` | Senha do 3CX | `minhasenha123` |
| `SECRET_KEY` | Chave de segurança | `chave-super-secreta-123` |

### Opcionais
| Variável | Padrão | Descrição |
|----------|--------|-----------|
| `DEBUG` | `False` | Modo debug |
| `LOG_LEVEL` | `INFO` | Nível de log |
| `PORT` | `8000` | Porta da aplicação |
| `API_TIMEOUT` | `30` | Timeout da API (segundos) |
| `CORS_ORIGINS` | `*` | Origens permitidas |

## 🔒 Configuração para Produção

### SSL/TLS com Let's Encrypt
```bash
# Instalar Certbot
sudo apt install certbot

# Gerar certificado
sudo certbot certonly --standalone -d seudominio.com

# Copiar certificados
sudo cp /etc/letsencrypt/live/seudominio.com/fullchain.pem nginx/ssl/
sudo cp /etc/letsencrypt/live/seudominio.com/privkey.pem nginx/ssl/

# Reiniciar com Nginx
docker-compose --profile production up -d
```

### Firewall
```bash
# Configurar UFW
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

## 🛠️ Comandos Úteis

### Gerenciamento
```bash
# Ver logs em tempo real
docker-compose logs -f

# Parar todos os serviços
docker-compose down

# Reiniciar aplicação
docker-compose restart amvox-analytics

# Atualizar sistema
git pull
./deploy.sh
```

### Monitoramento
```bash
# Status dos containers
docker-compose ps

# Uso de recursos
docker stats

# Health check manual
curl http://localhost:8000/health
```

### Backup
```bash
# Backup dos dados
tar -czf backup-$(date +%Y%m%d).tar.gz data/ logs/

# Backup do banco (se usando)
docker-compose exec postgres pg_dump -U user database > backup.sql
```

## 🚨 Troubleshooting

### Problemas Comuns

#### 1. Erro de Conexão 3CX
```bash
# Verificar conectividade
curl -X POST "http://SEU-3CX/gdacip/apijson.php"

# Verificar logs
docker-compose logs amvox-analytics | grep "3CX"
```

#### 2. Aplicação Não Inicia
```bash
# Verificar logs de erro
docker-compose logs amvox-analytics

# Verificar configurações
docker-compose config
```

#### 3. Porta em Uso
```bash
# Verificar porta
sudo netstat -tulpn | grep :8000

# Mudar porta no .env
PORT=8080
```

## 📞 Suporte

### Logs Importantes
- **Aplicação**: `logs/app.log`
- **Nginx**: `logs/nginx/`
- **Docker**: `docker-compose logs`

### Informações para Suporte
```bash
# Coletar informações do sistema
echo "=== SYSTEM INFO ===" > debug.txt
uname -a >> debug.txt
docker --version >> debug.txt
docker-compose --version >> debug.txt
echo "=== CONTAINERS ===" >> debug.txt
docker-compose ps >> debug.txt
echo "=== LOGS ===" >> debug.txt
docker-compose logs --tail=50 >> debug.txt
```

## 🔄 Atualizações

### Atualização Automática
O sistema inclui Watchtower para atualizações automáticas:
```bash
# Habilitar auto-update
docker-compose up -d watchtower
```

### Atualização Manual
```bash
# Parar sistema
docker-compose down

# Atualizar código
git pull

# Reconstruir e iniciar
./deploy.sh
```

---

**✅ Sistema pronto para produção com todas as funcionalidades implementadas!**
