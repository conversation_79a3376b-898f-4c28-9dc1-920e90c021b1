#!/usr/bin/env python3
"""
Script de teste para os novos endpoints de métricas avançadas
"""
import requests
import json
from datetime import date, timedelta

BASE_URL = "http://localhost:8000"

def test_endpoint(endpoint, description):
    """Testa um endpoint específico"""
    try:
        response = requests.get(f"{BASE_URL}{endpoint}", timeout=30)
        data = response.json()
        
        if data.get('success'):
            print(f"✅ {description}: OK")
            return True, data
        else:
            print(f"❌ {description}: Falha - {data.get('message', 'Erro desconhecido')}")
            return False, data
    except Exception as e:
        print(f"❌ {description}: Erro - {str(e)}")
        return False, None

def main():
    print("🔍 TESTANDO MÉTRICAS AVANÇADAS - SISTEMA AMVOX 3CX")
    print("=" * 60)
    
    # Parâmetros de teste
    today = date.today()
    yesterday = today - timedelta(days=1)
    week_ago = today - timedelta(days=7)
    
    params = f"start_date={today}&end_date={today}&queues=802,803"
    
    # Lista de endpoints para testar
    endpoints = [
        (f"/api/timing?{params}", "Métricas de Timing Avançadas"),
        (f"/api/advanced-metrics?{params}", "Métricas Abrangentes"),
        (f"/api/performance-dashboard?{params}", "Dashboard de Performance"),
        (f"/api/alerts-recommendations?{params}", "Alertas e Recomendações"),
        (f"/api/trends-analysis?current_start={yesterday}&current_end={today}&previous_start={week_ago}&previous_end={yesterday}&queues=802,803", "Análise de Tendências"),
    ]
    
    results = []
    detailed_results = {}
    
    # Testa cada endpoint
    for endpoint, description in endpoints:
        success, data = test_endpoint(endpoint, description)
        results.append(success)
        if success and data:
            detailed_results[description] = data
    
    print("\n" + "=" * 60)
    print(f"📊 RESULTADO: {sum(results)}/{len(results)} testes passaram")
    
    if sum(results) == len(results):
        print("🎉 Todos os endpoints de métricas avançadas funcionando!")
    elif sum(results) > len(results) // 2:
        print("⚠️  Maioria dos endpoints funcionando")
    else:
        print("🚨 Problemas críticos detectados")
    
    # Análise detalhada dos resultados
    print("\n🔍 ANÁLISE DETALHADA DOS RESULTADOS:")
    print("-" * 40)
    
    # Timing Metrics
    if "Métricas de Timing Avançadas" in detailed_results:
        timing_data = detailed_results["Métricas de Timing Avançadas"]
        advanced_metrics = timing_data.get("advanced_metrics", {})
        
        print(f"⏱️  TIMING METRICS:")
        print(f"   • ASA: {advanced_metrics.get('asa_formatted', 'N/A')}")
        print(f"   • AHT: {advanced_metrics.get('aht_formatted', 'N/A')}")
        print(f"   • Service Level: {advanced_metrics.get('service_level_percentage', 0):.1f}%")
        print(f"   • Taxa de Atendimento: {advanced_metrics.get('answer_rate', 0):.1f}%")
        print(f"   • Score de Eficiência: {advanced_metrics.get('efficiency_score', 0):.1f}")
    
    # Performance Dashboard
    if "Dashboard de Performance" in detailed_results:
        dashboard_data = detailed_results["Dashboard de Performance"]
        kpis = dashboard_data.get("data", {}).get("kpis", {})
        
        print(f"\n📊 DASHBOARD KPIs:")
        for kpi_name, kpi_data in kpis.items():
            status_icon = "✅" if kpi_data.get("status") == "good" else "⚠️"
            print(f"   {status_icon} {kpi_name.replace('_', ' ').title()}: {kpi_data.get('value', 0):.1f} (Meta: {kpi_data.get('target', 0)})")
    
    # Alerts and Recommendations
    if "Alertas e Recomendações" in detailed_results:
        alerts_data = detailed_results["Alertas e Recomendações"]
        summary = alerts_data.get("data", {}).get("summary", {})
        
        print(f"\n🚨 ALERTAS E RECOMENDAÇÕES:")
        print(f"   • Total de Alertas: {summary.get('total_alerts', 0)}")
        print(f"   • Alertas Críticos: {summary.get('critical_alerts', 0)}")
        print(f"   • Alertas de Aviso: {summary.get('warning_alerts', 0)}")
        print(f"   • Recomendações: {summary.get('total_recommendations', 0)}")
        print(f"   • Nível de Performance: {summary.get('performance_level', 'N/A')}")
        print(f"   • Saúde Geral: {summary.get('overall_health', 'N/A')}")
    
    # Advanced Metrics
    if "Métricas Abrangentes" in detailed_results:
        comprehensive_data = detailed_results["Métricas Abrangentes"]
        data = comprehensive_data.get("data", {})
        
        print(f"\n🎯 MÉTRICAS ABRANGENTES:")
        
        # Performance Classification
        perf_class = data.get("performance_classification", {})
        if perf_class:
            print(f"   • Classificação: {perf_class.get('level', 'N/A')} (Score: {perf_class.get('score', 0):.1f})")
            
            strengths = perf_class.get("strengths", [])
            if strengths:
                print(f"   • Pontos Fortes: {len(strengths)}")
                for strength in strengths[:3]:
                    print(f"     - {strength}")
            
            weaknesses = perf_class.get("weaknesses", [])
            if weaknesses:
                print(f"   • Pontos Fracos: {len(weaknesses)}")
                for weakness in weaknesses[:3]:
                    print(f"     - {weakness}")
        
        # Operational Insights
        insights = data.get("operational_insights", [])
        if insights:
            print(f"   • Insights Operacionais: {len(insights)}")
            for insight in insights[:3]:
                print(f"     - {insight}")
    
    # Trends Analysis
    if "Análise de Tendências" in detailed_results:
        trends_data = detailed_results["Análise de Tendências"]
        summary = trends_data.get("data", {}).get("summary", {})
        
        print(f"\n📈 ANÁLISE DE TENDÊNCIAS:")
        print(f"   • Métricas Analisadas: {summary.get('total_metrics_analyzed', 0)}")
        print(f"   • Métricas Melhorando: {summary.get('improving_metrics', 0)}")
        print(f"   • Métricas Declinando: {summary.get('declining_metrics', 0)}")
        print(f"   • Métricas Estáveis: {summary.get('stable_metrics', 0)}")
    
    print(f"\n🌐 Sistema disponível em: {BASE_URL}")
    print("📋 Novos endpoints de métricas avançadas implementados com sucesso!")
    
    # Recomendações finais
    print(f"\n💡 RECOMENDAÇÕES:")
    print("   • Use /api/advanced-metrics para análises completas")
    print("   • Use /api/performance-dashboard para KPIs em tempo real")
    print("   • Use /api/alerts-recommendations para ações prioritárias")
    print("   • Use /api/trends-analysis para comparações temporais")
    print("   • Integre estes endpoints no dashboard principal para máximo valor")

if __name__ == "__main__":
    main()
