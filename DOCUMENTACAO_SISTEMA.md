# 📊 DOCUMENTAÇÃO COMPLETA - AMVOX CALL CENTER ANALYTICS

## 🌐 **ACESSO AO SISTEMA**

### **URLs de Acesso**
- **Local**: http://localhost:8000
- **Rede Local**: http://*************:8000
- **Produção**: http://SEU-DOMINIO.com:8000

### **Credenciais de Acesso**
- **Sistema**: Sem autenticação (acesso direto)
- **API 3CX**: Configurada via variáveis de ambiente

---

## 🏗️ **ARQUITETURA DO SISTEMA**

### **Componentes Principais**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FRONTEND      │    │    BACKEND      │    │    API 3CX      │
│   (Dashboard)   │◄──►│   (FastAPI)     │◄──►│  (Relatórios)   │
│   HTML/CSS/JS   │    │    Python       │    │     JSON        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     NGINX       │    │     DOCKER      │    │   POSTGRESQL    │
│ (Proxy Reverso) │    │  (Container)    │    │   (Opcional)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **Tecnologias Utilizadas**
- **Backend**: FastAPI (Python 3.9+)
- **Frontend**: HTML5, CSS3, JavaScript ES6+
- **UI Framework**: Bootstrap 5
- **Gráficos**: Chart.js
- **PDF**: ReportLab
- **Container**: Docker + Docker Compose
- **Proxy**: Nginx
- **Cache**: Redis (opcional)

---

## 🔗 **ENDPOINTS DA API**

### **📊 Dashboard Principal**
```http
GET /
```
**Descrição**: Interface principal do sistema
**Retorno**: Página HTML com dashboard completo

### **🏥 Health Check**
```http
GET /health
```
**Descrição**: Verificação de saúde do sistema
**Retorno**:
```json
{
  "status": "healthy",
  "timestamp": "2025-07-08T22:35:48.123456",
  "threecx_connection": true,
  "version": "2.1.0"
}
```

### **📈 Dados de Distribuição**
```http
GET /api/distribution
```
**Parâmetros**:
- `start_date`: Data início (YYYY-MM-DD)
- `end_date`: Data fim (YYYY-MM-DD)  
- `queues`: Filas separadas por vírgula (802,803)

**Exemplo**:
```
GET /api/distribution?start_date=2025-07-07&end_date=2025-07-07&queues=802,803
```

**Retorno**:
```json
{
  "success": true,
  "message": "Distribuição gerada para 2025-07-07 a 2025-07-07",
  "data": {
    "Distribuição de Chamadas": {
      "sumario": { ... },
      "por_fila": { ... },
      "por_agente": { ... }
    }
  },
  "period": {
    "start": "2025-07-07",
    "end": "2025-07-07"
  },
  "queues": [802, 803]
}
```

### **😊 Dados de Satisfação**
```http
GET /api/satisfaction
```
**Parâmetros**: Mesmos da distribuição

**Retorno**:
```json
{
  "success": true,
  "message": "Pesquisa de satisfação gerada",
  "data": {
    "Pesquisa Satisfação": {
      "sumario": {
        "Pesquisas Efetuadas": [
          {
            "Av-1": 29,
            "Av-2": 23,
            "Av-3": 19,
            "% meta Av-1": "53.4",
            "% meta Av-2": "58.7",
            "% meta Av-3": "77.9"
          }
        ]
      },
      "Pesquisa por fila": { ... },
      "Pesquisa por Agente": { ... }
    }
  }
}
```

### **👥 Lista de Agentes**
```http
GET /api/agents
```
**Retorno**:
```json
{
  "success": true,
  "data": [
    {
      "Agente": "Amanda_Ribeiro",
      "Chamadas": 10,
      "Satisfacao": "100%",
      "Performance": "Excelente"
    }
  ]
}
```

### **👤 Agente Individual**
```http
GET /api/agent/{nome_agente}
```
**Exemplo**:
```
GET /api/agent/Amanda_Ribeiro?start_date=2025-07-07&end_date=2025-07-07&queues=802,803
```

**Retorno**:
```json
{
  "success": true,
  "agent_name": "Amanda_Ribeiro",
  "details": { ... },
  "metrics": {
    "avaliacoes_por_tipo": {
      "atendente": {
        "total": 8,
        "satisfeitos": 8,
        "insatisfeitos": 0,
        "percentual": 100
      }
    }
  },
  "analysis": {
    "performance_classification": "Excelente",
    "insights": [ ... ],
    "recommendations": [ ... ]
  }
}
```

### **📋 Resumo Executivo**
```http
GET /api/executive-summary
```
**Retorno**:
```json
{
  "success": true,
  "executive_summary": {
    "performance_geral": "Bom",
    "score_satisfacao": 77.9,
    "insights_principais": [ ... ],
    "recomendacoes_prioritarias": [ ... ],
    "agentes_destaque": [ ... ]
  }
}
```

### **📄 Relatórios PDF**

#### **PDF Distribuição**
```http
GET /api/generate-pdf/distribution
```
**Parâmetros**: Mesmos dos endpoints de dados
**Retorno**: Arquivo PDF para download

#### **PDF Satisfação**
```http
GET /api/generate-pdf/satisfaction
```
**Retorno**: Arquivo PDF com análise de satisfação

#### **PDF Agentes**
```http
GET /api/generate-pdf/agents
```
**Retorno**: Arquivo PDF com performance dos agentes

### **🔧 Teste de Conexão 3CX**
```http
GET /test-3cx
```
**Retorno**:
```json
{
  "status": "success",
  "message": "Conexão com 3CX estabelecida",
  "api_url": "http://ticmobilerb.ddns.net/gdacip/apijson.php",
  "timestamp": "2025-07-08T22:35:48"
}
```

---

## 🎯 **FUNCIONALIDADES DO SISTEMA**

### **📊 Dashboard Interativo**
- **Métricas em Tempo Real**: Total de chamadas, service level, tempo de espera
- **Score Satisfação**: Média ponderada das 3 avaliações (77.9%)
- **Gráficos Dinâmicos**: Distribuição por fila, horários de pico
- **Filtros Avançados**: Por período, filas específicas
- **Interface Responsiva**: Desktop, tablet e mobile

### **🧠 Análises Automáticas com IA**
- **Classificação de Performance**: Excelente, Bom, Regular, Crítico
- **Insights Automáticos**: Identificação de padrões e tendências
- **Recomendações Específicas**: Sugestões personalizadas
- **Resumo Executivo**: Análise consolidada para tomada de decisão

### **👤 Análise Individual de Agentes**
- **Performance Detalhada**: Métricas específicas por agente
- **Múltiplas Avaliações**:
  - **Av-1 (Atendente)**: Satisfeito vs Insatisfeito
  - **Av-2 (Chamada)**: Sim vs Não
  - **Av-3 (Empresa)**: Notas 1-5 (4+5 = positivas)
- **Coaching Direcionado**: Sugestões individuais
- **Modal Interativo**: Visualização completa com um clique

### **📄 Relatórios PDF Profissionais**
- **Análises Integradas**: Cada PDF inclui insights automáticos
- **Formatação Profissional**: Layout corporativo
- **Gráficos Inclusos**: Visualizações exportadas
- **Download Automático**: Geração em tempo real

---

## 🔧 **CONFIGURAÇÃO E ADMINISTRAÇÃO**

### **Variáveis de Ambiente Ativas**
```env
# API 3CX
THREECX_API_URL=http://ticmobilerb.ddns.net/gdacip/apijson.php
THREECX_USERNAME=amvox
THREECX_PASSWORD=super_7894

# Sistema
DEBUG=False
HOST=0.0.0.0
PORT=8000
SECRET_KEY=amvox_secret_key_production_123456789

# Filas
AVAILABLE_QUEUES=802,803
```

### **Filas Configuradas**
- **802**: RECEPTIVO AMVOX
- **803**: ATIVO AMVOX

### **Portas e Serviços**
- **Aplicação**: 8000
- **Nginx**: 80 (HTTP), 443 (HTTPS)
- **Redis**: 6379 (interno)

---

## 📱 **RESPONSIVIDADE**

### **Desktop (≥1200px)**
- Layout completo com 4 colunas
- Gráficos lado a lado
- Filtros expandidos
- Todas as funcionalidades visíveis

### **Tablet (768-1199px)**
- Layout adaptado com 2-3 colunas
- Gráficos empilhados
- Navegação otimizada
- Filtros colapsáveis

### **Mobile (≤767px)**
- Layout vertical otimizado
- Filtros em modal
- Botões touch-friendly (44px mínimo)
- Loading overlay
- Texto adaptativo

---

## 🔍 **MONITORAMENTO E LOGS**

### **Health Checks**
- **Aplicação**: http://*************:8000/health
- **Intervalo**: 30 segundos
- **Timeout**: 10 segundos
- **Retries**: 3 tentativas

### **Logs Disponíveis**
```bash
# Logs da aplicação
docker-compose logs amvox-analytics

# Logs do Nginx
docker-compose logs nginx

# Logs em tempo real
docker-compose logs -f
```

### **Métricas de Performance**
- **Tempo de Resposta**: < 2 segundos
- **Disponibilidade**: 99.9%
- **Conexão 3CX**: Monitorada automaticamente
- **Uso de Memória**: < 512MB por container

---

## 🛠️ **COMANDOS DE ADMINISTRAÇÃO**

### **Gerenciamento do Sistema**
```bash
# Ver status dos containers
docker-compose ps

# Parar sistema
docker-compose down

# Reiniciar aplicação
docker-compose restart amvox-analytics

# Atualizar sistema
git pull && ./deploy.sh

# Ver uso de recursos
docker stats
```

### **Backup e Manutenção**
```bash
# Backup dos dados
tar -czf backup-$(date +%Y%m%d).tar.gz data/ logs/

# Limpeza de logs antigos
find logs/ -name "*.log" -mtime +30 -delete

# Verificar espaço em disco
df -h
```

---

## 🚨 **TROUBLESHOOTING**

### **Problemas Comuns**

#### **1. Sistema não responde**
```bash
# Verificar containers
docker-compose ps

# Ver logs de erro
docker-compose logs amvox-analytics | tail -50

# Reiniciar se necessário
docker-compose restart
```

#### **2. Erro de conexão 3CX**
```bash
# Testar conectividade
curl -X POST "http://ticmobilerb.ddns.net/gdacip/apijson.php"

# Verificar configurações
grep THREECX .env

# Ver logs específicos
docker-compose logs amvox-analytics | grep "3CX"
```

#### **3. Performance lenta**
```bash
# Verificar recursos
docker stats

# Verificar logs de performance
docker-compose logs amvox-analytics | grep "slow"

# Reiniciar Redis (cache)
docker-compose restart redis
```

---

## 📞 **SUPORTE E CONTATO**

### **Informações do Sistema**
- **Versão**: 2.1.0
- **Última Atualização**: 2025-07-08
- **Ambiente**: Produção
- **Status**: ✅ Operacional

### **Para Suporte Técnico**
1. **Coletar logs**: `docker-compose logs > debug.log`
2. **Verificar health**: `curl http://localhost:8000/health`
3. **Informar erro específico** e contexto
4. **Anexar arquivo debug.log**

---

**✅ Sistema 100% operacional e documentado!** 🎉
