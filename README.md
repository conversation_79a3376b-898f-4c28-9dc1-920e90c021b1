# 📊 Sistema de Relatórios 3CX - Amvox

Sistema completo de análise de métricas de pós-venda integrado com 3CX, oferecendo relatórios detalhados, visualizações interativas e insights acionáveis para otimização do call center.

## 🎯 Funcionalidades Principais

### 📈 Relatórios Disponíveis
- **Distribuição de Chamadas**: Volume e distribuição por fila
- **Pesquisa de Satisfação**: NPS, CSAT e feedback dos clientes
- **Métricas de Timing**: ASA, AHT, Service Level, Taxa de Abandono
- **Performance de Agentes**: Produtividade individual e comparativa
- **Análise de SLA**: Compliance e métricas de qualidade
- **Relatórios Comparativos**: Análise entre períodos

### 📊 Visualizações
- Gráficos interativos com Chart.js
- Dashboards em tempo real
- Gauges de performance
- Tabelas detalhadas
- Exportação em PDF

### 🧠 Análises Automáticas com IA (NOVO!)
- **Insights Automáticos**: Identificação de padrões e tendências
- **Classificação de Performance**: Excelente, Bom, Regular, Crítico
- **Recomendações Específicas**: Sugestões personalizadas de melhorias
- **Resumo Executivo**: Análise consolidada para tomada de decisão
- **Coaching Direcionado**: Sugestões individuais para cada agente

### 📱 Interface Responsiva (NOVO!)
- **Desktop**: Layout completo com 4 colunas
- **Tablet**: Layout adaptado com 2-3 colunas
- **Mobile**: Layout vertical otimizado com filtros colapsáveis
- **Touch Devices**: Botões e controles otimizados para toque

### 🔧 Recursos Técnicos
- API REST completa
- Interface web responsiva
- Integração com 3CX via API
- Geração automática de PDFs
- Cálculo de métricas avançadas

## 🏗️ Arquitetura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   3CX System    │
│   (HTML/JS)     │◄──►│   (FastAPI)     │◄──►│   (PBX)         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Relatórios    │
                       │   (PDF/Charts)  │
                       └─────────────────┘
```

## 🚀 Instalação e Configuração

### Pré-requisitos
- Python 3.8+
- Acesso à API do 3CX

### 1. Clone o repositório
```bash
git clone <repository-url>
cd amvox_relatorio
```

### 2. Instale as dependências
```bash
pip install -r requirements.txt
```

### 3. Configure as variáveis de ambiente
```bash
cp .env.example .env
```

Edite o arquivo `.env` com suas configurações:
```env
# 3CX API Configuration
THREECX_API_URL=http://ticmobilerb.ddns.net/gdacip/apijson.php
THREECX_USER=seu_usuario
THREECX_PASSWORD=sua_senha

# Application Configuration
APP_HOST=0.0.0.0
APP_PORT=8000
DEBUG=True

# Queue Configuration
AVAILABLE_QUEUES=802,803
```

### 4. Execute a aplicação
```bash
python main.py
```

A aplicação estará disponível em: `http://localhost:8000`

## 📋 Endpoints da API

### Relatórios
- `POST /api/v1/reports/distribution` - Relatório de distribuição
- `POST /api/v1/reports/satisfaction` - Pesquisa de satisfação
- `POST /api/v1/reports/timing-metrics` - Métricas de timing
- `POST /api/v1/reports/agent-performance` - Performance de agentes
- `POST /api/v1/reports/sla-compliance` - Compliance de SLA
- `POST /api/v1/reports/comparative` - Relatório comparativo

### Gráficos
- `GET /api/charts/{chart_type}` - Gráficos em HTML

### PDF
- `POST /generate-pdf/{report_type}` - Geração de PDFs

### Sistema
- `GET /health` - Status do sistema
- `GET /api/v1/reports/health` - Health check da API

## 🎨 Interface Web

### Dashboard Principal
- Visão geral das métricas principais
- Gráficos em tempo real
- Ações rápidas para relatórios
- Status do sistema

### Páginas de Relatórios
- **`/reports/distribution`** - Análise de distribuição
- **`/reports/satisfaction`** - Pesquisa de satisfação
- **`/reports/timing`** - Métricas de timing
- **`/reports/agents`** - Performance de agentes
- **`/reports/comparative`** - Análise comparativa

## 📊 Métricas Calculadas

### Timing Metrics
- **ASA** (Average Speed of Answer): Tempo médio para atender
- **AHT** (Average Handle Time): Tempo médio de atendimento
- **ATT** (Average Talk Time): Tempo médio de conversa
- **ACW** (After Call Work): Tempo pós-chamada
- **Service Level**: % chamadas atendidas no prazo
- **Abandonment Rate**: Taxa de abandono

### Satisfação
- **NPS** (Net Promoter Score): -100 a +100
- **CSAT** (Customer Satisfaction): 0 a 100%
- **Distribuição de notas**: 1 a 10
- **Taxa de resposta**: % de clientes que responderam

### Performance de Agentes
- **Calls per Hour**: Chamadas por hora
- **Occupancy Rate**: Taxa de ocupação
- **Productivity Score**: Score de produtividade (0-10)
- **First Call Resolution**: Taxa de resolução na primeira chamada
- **Customer Satisfaction**: Satisfação por agente

## 🔧 Configurações Avançadas

### Filas Disponíveis
Configure as filas no arquivo `.env`:
```env
AVAILABLE_QUEUES=801,802,803,804
```

### Thresholds de SLA
```env
DEFAULT_SERVICE_LEVEL_THRESHOLD=20
```

### Cache de Relatórios
```env
REPORTS_CACHE_TTL=300
REPORTS_MAX_PERIOD_DAYS=365
```

## 📁 Estrutura do Projeto

```
amvox_relatorio/
├── src/
│   ├── api/                 # Endpoints da API
│   ├── clients/             # Cliente 3CX
│   ├── models/              # Modelos de dados
│   └── services/            # Serviços de negócio
├── templates/               # Templates HTML
├── static/                  # Arquivos estáticos
├── main.py                  # Aplicação principal
├── config.py                # Configurações
├── requirements.txt         # Dependências
└── README.md               # Documentação
```

## 🔍 Troubleshooting

### Erro de Conexão 3CX
```
HTTPException: 401 Unauthorized
```
**Solução**: Verificar `THREECX_USER` e `THREECX_PASSWORD` no `.env`

### Timeout na API
```
HTTPException: 504 Gateway Timeout
```
**Solução**: Reduzir período do relatório ou verificar conectividade

### Erro na Geração de PDF
**Solução**: Verificar se todas as dependências estão instaladas:
```bash
pip install reportlab weasyprint
```

## 🚀 Desenvolvimento

### Executar em modo desenvolvimento
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Executar testes
```bash
python -m pytest tests/
```

### Estrutura de desenvolvimento
1. **Backend**: FastAPI + Pydantic
2. **Frontend**: HTML + Bootstrap + Chart.js/Plotly
3. **Integração**: Cliente HTTP assíncrono
4. **Relatórios**: ReportLab + Plotly

## 📈 Roadmap

- [ ] Autenticação e autorização
- [ ] Cache Redis para performance
- [ ] Notificações automáticas
- [ ] Dashboards personalizáveis
- [ ] Integração com outros sistemas
- [ ] Mobile app
- [ ] Alertas em tempo real

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 📞 Suporte

Para suporte técnico, entre em contato:
- **Email**: <EMAIL>
- **Documentação**: Consulte o arquivo `3CX_Reports_Documentation.html`

---

**Desenvolvido com ❤️ pela equipe Amvox**
