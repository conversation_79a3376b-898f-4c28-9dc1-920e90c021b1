"""
Configurações do Sistema de Relatórios 3CX
"""
import os
from typing import List
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Configurações da aplicação"""
    
    # 3CX API Configuration
    threecx_api_url: str = Field(default="http://ticmobilerb.ddns.net/gdacip/apijson.php")
    threecx_user: str = Field(default="amvox")
    threecx_password: str = Field(default="super_7894")
    
    # Database Configuration
    database_url: str = Field(default="postgresql://user:password@localhost:5432/amvox_reports")
    
    # Application Configuration
    app_host: str = Field(default="0.0.0.0")
    app_port: int = Field(default=8000)
    debug: bool = Field(default=True)
    
    # Reports Configuration
    reports_cache_ttl: int = Field(default=300)
    reports_max_period_days: int = Field(default=365)
    default_service_level_threshold: int = Field(default=20)
    
    # PDF Configuration
    pdf_logo_path: str = Field(default="static/logo.png")
    pdf_company_name: str = Field(default="Amvox")
    pdf_company_address: str = Field(default="Endereço da Empresa")
    
    # Queue Configuration
    available_queues: str = Field(default="802,803")

    # Chatwoot Configuration
    chatwoot_db_host: str = Field(default="localhost")
    chatwoot_db_port: int = Field(default=3024)
    chatwoot_db_name: str = Field(default="chatwoot")
    chatwoot_db_user: str = Field(default="chatwoot")
    chatwoot_db_password: str = Field(default="chatwoot123")
    chatwoot_account_id: int = Field(default=1)

    @property
    def available_queues_list(self) -> List[int]:
        """Retorna lista de filas disponíveis"""
        return [int(q.strip()) for q in self.available_queues.split(",")]

    @property
    def chatwoot_database_url(self) -> str:
        """Retorna URL de conexão do banco Chatwoot"""
        return f"postgresql://{self.chatwoot_db_user}:{self.chatwoot_db_password}@{self.chatwoot_db_host}:{self.chatwoot_db_port}/{self.chatwoot_db_name}"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Instância global das configurações
settings = Settings()
