# 📱 Serviço de Métricas Chatwoot - WhatsApp

## 🎯 **Objetivo**

Este serviço foi desenvolvido para conectar ao banco de dados do Chatwoot e coletar métricas de avaliação e performance do atendimento via WhatsApp, criando tabelas, gráficos e relatórios interativos com filtros de data.

## ✨ **Funcionalidades**

### 📊 **Dashboard Principal**
- Visão geral das métricas principais
- Cards com indicadores-chave (KPIs)
- Gráficos de tendência e distribuição
- Filtros rápidos de data (hoje, 7 dias, 30 dias)

### 😊 **Relatório de Satisfação**
- Análise detalhada das avaliações dos clientes
- Taxa de satisfação por agente
- Distribuição de notas (1-5 estrelas)
- Gráficos interativos e tabelas detalhadas

### ⏱️ **Tempo de Resposta**
- Análise dos tempos de primeira resposta
- Performance por agente
- Identificação de gargalos
- Métricas de SLA

### 📈 **Volume de Conversas**
- Análise do volume diário de atendimentos
- Tendências e sazonalidade
- Distribuição por status (abertas/resolvidas/pendentes)
- Identificação de picos de demanda

### 🏆 **Performance Geral**
- Relatório consolidado por agente
- Ranking de performance
- Métricas combinadas
- Classificação visual por desempenho

## 🚀 **Como Usar**

### **1. Acesso via Menu**
No menu lateral do sistema, você encontrará a seção **CHATWOOT** com as seguintes opções:

- **Dashboard WhatsApp**: Visão geral
- **Satisfação WhatsApp**: Relatório de avaliações
- **Tempo de Resposta**: Análise de tempos
- **Volume WhatsApp**: Análise de volume
- **Performance WhatsApp**: Relatório consolidado

### **2. Filtros de Data**
Todos os relatórios possuem filtros de data com:
- **Data Inicial** e **Data Final**: Seleção manual
- **Botões Rápidos**: Hoje, 7 dias, 30 dias
- **Filtro por Agente**: Análise individual (onde aplicável)

### **3. Exportação de Dados**
- Exportação em formato JSON
- Dados estruturados para análise externa
- Histórico de exportações

## 🔧 **Configuração**

### **Variáveis de Ambiente**
```env
# Configurações do Banco Chatwoot
CHATWOOT_DB_HOST=localhost
CHATWOOT_DB_PORT=3024
CHATWOOT_DB_NAME=chatwoot
CHATWOOT_DB_USER=chatwoot
CHATWOOT_DB_PASSWORD=chatwoot123
CHATWOOT_ACCOUNT_ID=1
```

### **Instalação de Dependências**
```bash
pip install asyncpg>=0.28.0
```

## 📊 **Métricas Disponíveis**

### **Satisfação do Cliente**
- **Taxa de Satisfação**: Percentual de avaliações positivas (4-5 estrelas)
- **Avaliação Média**: Média ponderada das notas
- **Total de Avaliações**: Quantidade total de feedbacks
- **Distribuição**: Positivas, neutras e negativas

### **Tempo de Resposta**
- **Tempo Médio**: Média de primeira resposta em minutos
- **Tempo Mínimo/Máximo**: Extremos por agente
- **Total de Conversas**: Volume de atendimentos
- **Performance**: Classificação por faixas de tempo

### **Volume de Atendimento**
- **Total de Conversas**: Soma no período selecionado
- **Média Diária**: Volume médio por dia
- **Dia de Pico**: Data com maior volume
- **Status**: Distribuição por situação da conversa

### **Performance Geral**
- **Score Consolidado**: Combinação de métricas
- **Ranking**: Ordenação por performance
- **Classificação**: Excelente/Boa/Média/Ruim
- **Tendências**: Evolução ao longo do tempo

## 🎨 **Interface**

### **Design Responsivo**
- Adaptável para desktop, tablet e mobile
- Cards coloridos por categoria de métrica
- Gráficos interativos com Plotly.js
- Animações suaves e feedback visual

### **Cores por Categoria**
- 🟢 **Satisfação**: Verde (sucesso)
- 🔵 **Tempo de Resposta**: Azul (informação)
- 🟣 **Volume**: Rosa/Roxo (destaque)
- 🟦 **Performance**: Azul claro (profissional)

### **Elementos Visuais**
- **Cards com Gradientes**: Visual moderno e atrativo
- **Badges Coloridos**: Classificação rápida de performance
- **Gráficos Interativos**: Hover, zoom e filtros
- **Loading States**: Feedback durante carregamento

## 🔍 **Teste do Serviço**

Execute o script de teste para verificar se tudo está funcionando:

```bash
python test_chatwoot_service.py
```

O script testará:
- ✅ Configurações
- ✅ Conexão com o banco
- ✅ Queries básicas
- ✅ Queries de métricas
- ✅ Camada de serviços

## 📈 **APIs Disponíveis**

### **Endpoints Principais**
- `GET /api/v1/chatwoot/health` - Status da conexão
- `GET /api/v1/chatwoot/dashboard` - Dados do dashboard
- `GET /api/v1/chatwoot/satisfaction` - Métricas de satisfação
- `GET /api/v1/chatwoot/response-time` - Tempo de resposta
- `GET /api/v1/chatwoot/volume` - Volume de conversas
- `GET /api/v1/chatwoot/performance` - Performance geral
- `GET /api/v1/chatwoot/agents` - Lista de agentes

### **Parâmetros Comuns**
- `start_date`: Data inicial (YYYY-MM-DD)
- `end_date`: Data final (YYYY-MM-DD)
- `agent_id`: ID do agente (opcional)

## 🛠️ **Solução de Problemas**

### **Problemas Comuns**

#### **Erro de Conexão**
```
❌ Falha na conexão com Chatwoot
```
**Soluções**:
1. Verificar se o container do Chatwoot está rodando
2. Confirmar as configurações de rede (host/porta)
3. Validar credenciais de acesso

#### **Dados Não Encontrados**
```
Nenhum dado encontrado para o período selecionado
```
**Soluções**:
1. Expandir o período de busca
2. Verificar se há dados no Chatwoot para o período
3. Confirmar o account_id configurado

#### **Timeout de Query**
```
Timeout na execução da query
```
**Soluções**:
1. Reduzir o período de análise
2. Verificar performance do banco
3. Otimizar queries se necessário

### **Verificações Úteis**

```bash
# Verificar status do container Chatwoot
docker ps | grep chatwoot

# Testar conexão direta ao banco
psql -h localhost -p 3024 -U chatwoot -d chatwoot -c "SELECT COUNT(*) FROM conversations;"

# Verificar logs da aplicação
tail -f logs/app.log
```

## 📞 **Suporte**

Para dúvidas ou problemas:

1. **Verificar Logs**: Consulte os logs da aplicação
2. **Health Check**: Teste `/api/v1/chatwoot/health`
3. **Script de Teste**: Execute `test_chatwoot_service.py`
4. **Documentação**: Consulte `CHATWOOT_SERVICE_DOCUMENTATION.md`

## 🔮 **Próximas Funcionalidades**

- [ ] Relatórios agendados por email
- [ ] Alertas automáticos para métricas críticas
- [ ] Dashboard em tempo real
- [ ] Análise de sentimento
- [ ] Exportação em Excel/CSV
- [ ] Comparativos históricos
- [ ] Métricas de SLA avançadas

---

**🎉 O serviço está pronto para uso!** Acesse o menu do WhatsApp e explore todas as funcionalidades disponíveis.

**📝 Criado em:** 2025-01-30  
**👨‍💻 Desenvolvido por:** Augment Agent  
**📋 Status:** ✅ Implementado e Funcional
