#!/usr/bin/env python3
"""
Script de inicialização do Sistema de Relatórios 3CX
"""
import os
import sys
import subprocess
import webbrowser
from pathlib import Path


def print_banner():
    """Imprime banner de inicialização"""
    print("=" * 70)
    print("🚀 SISTEMA DE RELATÓRIOS 3CX - AMVOX")
    print("📊 Sistema completo de análise de métricas de pós-venda")
    print("=" * 70)


def check_python_version():
    """Verifica versão do Python"""
    print("🐍 Verificando versão do Python...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ é necessário!")
        print(f"   Versão atual: {sys.version}")
        sys.exit(1)
    
    print(f"✅ Python {sys.version.split()[0]} - OK")


def check_dependencies():
    """Verifica se as dependências estão instaladas"""
    print("\n📦 Verificando dependências...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'httpx',
        'plotly',
        'pandas',
        'reportlab'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - FALTANDO")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Pacotes faltando: {', '.join(missing_packages)}")
        print("💡 Execute: pip install -r requirements.txt")
        
        response = input("\n🤔 Deseja instalar automaticamente? (s/n): ")
        if response.lower() in ['s', 'sim', 'y', 'yes']:
            install_dependencies()
        else:
            print("❌ Instalação cancelada. Execute manualmente:")
            print("   pip install -r requirements.txt")
            sys.exit(1)


def install_dependencies():
    """Instala dependências automaticamente"""
    print("\n📥 Instalando dependências...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependências instaladas com sucesso!")
    except subprocess.CalledProcessError:
        print("❌ Erro ao instalar dependências")
        print("💡 Execute manualmente: pip install -r requirements.txt")
        sys.exit(1)


def check_env_file():
    """Verifica arquivo de configuração"""
    print("\n⚙️  Verificando configurações...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("📋 Criando arquivo .env a partir do exemplo...")
            env_file.write_text(env_example.read_text())
            print("✅ Arquivo .env criado!")
            print("⚠️  IMPORTANTE: Configure suas credenciais no arquivo .env")
        else:
            print("❌ Arquivo .env.example não encontrado!")
            sys.exit(1)
    else:
        print("✅ Arquivo .env encontrado")
    
    # Verifica configurações básicas
    env_content = env_file.read_text()
    
    required_vars = [
        'THREECX_API_URL',
        'THREECX_USER',
        'THREECX_PASSWORD'
    ]
    
    missing_vars = []
    for var in required_vars:
        if var not in env_content or f"{var}=" in env_content:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  Configure as variáveis no .env: {', '.join(missing_vars)}")


def create_directories():
    """Cria diretórios necessários"""
    print("\n📁 Criando diretórios...")
    
    directories = [
        "static",
        "temp",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ {directory}/")


def start_server():
    """Inicia o servidor"""
    print("\n🚀 Iniciando servidor...")
    print("📍 URL: http://localhost:8000")
    print("🛑 Para parar: Ctrl+C")
    print("-" * 50)
    
    try:
        # Abre navegador automaticamente
        webbrowser.open("http://localhost:8000")
        
        # Inicia servidor
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000",
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n\n🛑 Servidor parado pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro ao iniciar servidor: {e}")


def show_help():
    """Mostra informações de ajuda"""
    print("\n" + "=" * 70)
    print("📚 INFORMAÇÕES ÚTEIS")
    print("=" * 70)
    print("🌐 Interface Web: http://localhost:8000")
    print("📖 Documentação: 3CX_Reports_Documentation.html")
    print("🧪 Testes: python test_api.py")
    print("⚙️  Configuração: edite o arquivo .env")
    print("📊 Endpoints API: http://localhost:8000/docs")
    print("=" * 70)


def main():
    """Função principal"""
    print_banner()
    
    # Verificações iniciais
    check_python_version()
    check_dependencies()
    check_env_file()
    create_directories()
    
    # Mostra informações úteis
    show_help()
    
    # Pergunta se deve iniciar o servidor
    print("\n🤔 Deseja iniciar o servidor agora? (s/n): ", end="")
    response = input()
    
    if response.lower() in ['s', 'sim', 'y', 'yes', '']:
        start_server()
    else:
        print("\n✅ Configuração concluída!")
        print("💡 Para iniciar manualmente: python main.py")


if __name__ == "__main__":
    main()
