{% extends "base.html" %}

{% block title %}Dashboard - Sistema de Relatórios 3CX{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-4 text-primary">
                <i class="fas fa-tachometer-alt me-3"></i>
                Dashboard de Métricas de Pós-Venda
            </h1>
            <p class="lead text-muted">Visão geral das principais métricas do call center</p>
        </div>
    </div>

    <!-- Filtros -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-2"></i>
            Filtros de Período
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label for="start_date" class="form-label">Data Inicial</label>
                    <input type="date" class="form-control" id="start_date" value="{{ (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d') }}">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">Data Final</label>
                    <input type="date" class="form-control" id="end_date" value="{{ datetime.now().strftime('%Y-%m-%d') }}">
                </div>
                <div class="col-md-4">
                    <label for="queues" class="form-label">Filas (separadas por vírgula)</label>
                    <input type="text" class="form-control" id="queues" placeholder="802,803" value="{{ available_queues|join(',') }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button class="btn btn-primary w-100" onclick="loadDashboard()">
                        <i class="fas fa-sync-alt me-2"></i>
                        Atualizar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Cards de Métricas Principais -->
    <div class="row mb-4" id="metrics-cards">
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value" id="total-calls">-</div>
                    <div class="metric-label">Total de Chamadas</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-success" id="service-level">-</div>
                    <div class="metric-label">Service Level (%)</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-warning" id="avg-wait-time">-</div>
                    <div class="metric-label">Tempo Médio Espera (s)</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-info" id="satisfaction-score">-</div>
                    <div class="metric-label">NPS Score</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos Principais -->
    <div class="row mb-4">
        <!-- Distribuição por Fila -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-2"></i>
                    Distribuição de Chamadas por Fila
                </div>
                <div class="card-body">
                    <div id="distribution-chart" class="chart-container">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Carregando gráfico...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Service Level Gauge -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Service Level
                </div>
                <div class="card-body">
                    <div id="service-level-gauge" class="chart-container">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Carregando gauge...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Segunda linha de gráficos -->
    <div class="row mb-4">
        <!-- Métricas de Timing -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-stopwatch me-2"></i>
                    Métricas de Timing
                </div>
                <div class="card-body">
                    <div id="timing-chart" class="chart-container">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Carregando métricas...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Distribuição por Hora -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-clock me-2"></i>
                    Distribuição por Hora
                </div>
                <div class="card-body">
                    <div id="hourly-chart" class="chart-container">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Carregando distribuição...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ações Rápidas -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-bolt me-2"></i>
                    Ações Rápidas
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="/reports/distribution" class="btn btn-outline-primary w-100 mb-2">
                                <i class="fas fa-chart-pie me-2"></i>
                                Relatório Distribuição
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="/reports/satisfaction" class="btn btn-outline-success w-100 mb-2">
                                <i class="fas fa-smile me-2"></i>
                                Pesquisa Satisfação
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="/reports/timing" class="btn btn-outline-warning w-100 mb-2">
                                <i class="fas fa-stopwatch me-2"></i>
                                Métricas Timing
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="/reports/agents" class="btn btn-outline-info w-100 mb-2">
                                <i class="fas fa-users me-2"></i>
                                Performance Agentes
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status do Sistema -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-heartbeat me-2"></i>
                    Status do Sistema
                </div>
                <div class="card-body">
                    <div id="system-status">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Verificando status...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Carrega dashboard completo
    async function loadDashboard() {
        // Carrega métricas principais
        await loadMainMetrics();
        
        // Carrega gráficos
        await loadChart('distribution', 'distribution-chart');
        await loadChart('service_level_gauge', 'service-level-gauge');
        await loadChart('timing', 'timing-chart');
        await loadChart('hourly', 'hourly-chart');
        
        // Carrega status do sistema
        await loadSystemStatus();
    }
    
    // Carrega métricas principais
    async function loadMainMetrics() {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;
        const queues = document.getElementById('queues').value;
        
        try {
            const params = new URLSearchParams({
                start_date: startDate,
                end_date: endDate
            });
            
            if (queues) {
                params.append('queues', queues);
            }
            
            // Simula dados das métricas (em produção, viria da API)
            const totalCalls = Math.floor(Math.random() * 1000) + 500;
            const serviceLevel = (Math.random() * 20 + 80).toFixed(1);
            const avgWaitTime = (Math.random() * 30 + 15).toFixed(1);
            const npsScore = (Math.random() * 40 + 40).toFixed(1);
            
            document.getElementById('total-calls').textContent = totalCalls.toLocaleString();
            document.getElementById('service-level').textContent = serviceLevel + '%';
            document.getElementById('avg-wait-time').textContent = avgWaitTime + 's';
            document.getElementById('satisfaction-score').textContent = npsScore;
            
        } catch (error) {
            console.error('Erro ao carregar métricas:', error);
        }
    }
    
    // Carrega status do sistema
    async function loadSystemStatus() {
        try {
            const response = await fetch('/health');
            const data = await response.json();
            
            let statusHtml = '';
            if (data.status === 'healthy') {
                statusHtml = `
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>Sistema Operacional</strong><br>
                        Conexão 3CX: ${data['3cx_connection'] ? 'Ativa' : 'Inativa'}<br>
                        Última verificação: ${new Date(data.timestamp).toLocaleString('pt-BR')}
                    </div>
                `;
            } else {
                statusHtml = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Sistema com Problemas</strong><br>
                        Status: ${data.status}<br>
                        Conexão 3CX: ${data['3cx_connection'] ? 'Ativa' : 'Inativa'}
                    </div>
                `;
            }
            
            document.getElementById('system-status').innerHTML = statusHtml;
            
        } catch (error) {
            console.error('Erro ao carregar status:', error);
            document.getElementById('system-status').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>
                    <strong>Erro ao verificar status do sistema</strong>
                </div>
            `;
        }
    }
    
    // Carrega dashboard ao carregar a página
    document.addEventListener('DOMContentLoaded', function() {
        // Define datas padrão (últimos 7 dias)
        const today = new Date();
        const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        
        document.getElementById('end_date').value = today.toISOString().split('T')[0];
        document.getElementById('start_date').value = lastWeek.toISOString().split('T')[0];
        
        // Carrega dashboard
        loadDashboard();
        
        // Auto-refresh a cada 5 minutos
        setInterval(loadDashboard, 5 * 60 * 1000);
    });
</script>
{% endblock %}
