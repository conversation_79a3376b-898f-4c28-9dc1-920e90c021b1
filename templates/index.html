<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Relatórios - Amvox</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --accent-primary: #0d6efd;
            --accent-secondary: #6610f2;
            --success: #198754;
            --warning: #fd7e14;
            --danger: #dc3545;
            --info: #0dcaf0;
            --whatsapp: #25d366;
            --whatsapp-dark: #128c7e;
            --border-color: #dee2e6;
            --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        .hero-section {
            background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
            color: white;
            padding: 4rem 0;
            margin-bottom: 3rem;
        }

        .hero-section h1 {
            font-weight: 700;
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .hero-section p {
            font-size: 1.25rem;
            opacity: 0.9;
        }

        .system-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-sm);
            height: 100%;
        }

        .system-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-md);
            text-decoration: none;
        }

        .system-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            margin: 0 auto 1.5rem;
            color: white;
        }

        .system-icon.threecx {
            background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
        }

        .system-icon.whatsapp {
            background: linear-gradient(135deg, var(--whatsapp) 0%, var(--whatsapp-dark) 100%);
        }

        .system-card h3 {
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .system-card p {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            text-align: left;
        }

        .feature-list li {
            padding: 0.5rem 0;
            color: var(--text-secondary);
        }

        .feature-list li i {
            color: var(--success);
            margin-right: 0.5rem;
        }

        .btn-system {
            padding: 0.75rem 2rem;
            font-weight: 500;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-threecx {
            background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
            color: white;
        }

        .btn-threecx:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            color: white;
        }

        .btn-whatsapp {
            background: linear-gradient(135deg, var(--whatsapp) 0%, var(--whatsapp-dark) 100%);
            color: white;
        }

        .btn-whatsapp:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            color: white;
        }

        .stats-section {
            background: var(--bg-primary);
            border-radius: 16px;
            padding: 2rem;
            margin: 3rem 0;
            box-shadow: var(--shadow-sm);
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--accent-primary);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .footer {
            background: var(--text-primary);
            color: white;
            padding: 2rem 0;
            margin-top: 4rem;
        }

        @media (max-width: 768px) {
            .hero-section h1 {
                font-size: 2rem;
            }
            
            .hero-section p {
                font-size: 1rem;
            }
            
            .system-card {
                margin-bottom: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1>Sistema de Relatórios Amvox</h1>
                    <p>Análise completa de métricas de pós-venda e atendimento ao cliente</p>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="d-flex align-items-center justify-content-end">
                        <div id="systemStatus" class="me-3">
                            <small>Status dos Sistemas</small>
                        </div>
                        <div class="spinner-border spinner-border-sm" role="status" id="statusSpinner">
                            <span class="visually-hidden">Carregando...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Systems Cards -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <a href="/reports/distribution" class="text-decoration-none">
                    <div class="system-card">
                        <div class="system-icon threecx">
                            <i class="fas fa-phone-alt"></i>
                        </div>
                        <h3>Sistema 3CX</h3>
                        <p>Relatórios completos de call center com métricas avançadas de timing e performance</p>
                        
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> Distribuição de Chamadas</li>
                            <li><i class="fas fa-check"></i> Métricas de Timing (ASA, AHT, SLA)</li>
                            <li><i class="fas fa-check"></i> Performance de Agentes</li>
                            <li><i class="fas fa-check"></i> Análise de Filas</li>
                            <li><i class="fas fa-check"></i> Relatórios Comparativos</li>
                        </ul>
                        
                        <div class="mt-3">
                            <span class="btn btn-system btn-threecx">
                                <i class="fas fa-chart-line me-2"></i>Acessar Relatórios 3CX
                            </span>
                        </div>
                    </div>
                </a>
            </div>
            
            <div class="col-lg-6 mb-4">
                <a href="/chatwoot" class="text-decoration-none">
                    <div class="system-card">
                        <div class="system-icon whatsapp">
                            <i class="fab fa-whatsapp"></i>
                        </div>
                        <h3>WhatsApp Business</h3>
                        <p>Dashboard completo de métricas do Chatwoot com análise de satisfação e performance</p>
                        
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> Pesquisa de Satisfação (NPS/CSAT)</li>
                            <li><i class="fas fa-check"></i> Tempo de Resposta</li>
                            <li><i class="fas fa-check"></i> Volume de Conversas</li>
                            <li><i class="fas fa-check"></i> Performance de Agentes</li>
                            <li><i class="fas fa-check"></i> Análise de Tendências</li>
                        </ul>
                        
                        <div class="mt-3">
                            <span class="btn btn-system btn-whatsapp">
                                <i class="fab fa-whatsapp me-2"></i>Acessar Dashboard WhatsApp
                            </span>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Stats Section -->
        <div class="stats-section">
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number" id="totalReports">--</div>
                        <div class="stat-label">Relatórios Gerados</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number" id="totalCalls">--</div>
                        <div class="stat-label">Chamadas Analisadas</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number" id="totalConversations">--</div>
                        <div class="stat-label">Conversas WhatsApp</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number" id="avgSatisfaction">--</div>
                        <div class="stat-label">Satisfação Média</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Access -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Acesso Rápido</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Relatórios 3CX</h6>
                                <div class="d-flex flex-wrap gap-2 mb-3">
                                    <a href="/reports/distribution" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-chart-pie me-1"></i>Distribuição
                                    </a>
                                    <a href="/reports/timing" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-clock me-1"></i>Timing
                                    </a>
                                    <a href="/reports/agents" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-users me-1"></i>Agentes
                                    </a>
                                    <a href="/reports/comparative" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-chart-line me-1"></i>Comparativo
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Dashboard WhatsApp</h6>
                                <div class="d-flex flex-wrap gap-2 mb-3">
                                    <a href="/chatwoot/satisfaction" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-star me-1"></i>Satisfação
                                    </a>
                                    <a href="/chatwoot/response-time" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-clock me-1"></i>Tempo Resposta
                                    </a>
                                    <a href="/chatwoot/volume" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-comments me-1"></i>Volume
                                    </a>
                                    <a href="/chatwoot/performance" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-trophy me-1"></i>Performance
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>Sistema de Relatórios Amvox</h6>
                    <p class="mb-0">Versão 2.1.0 - Análise completa de métricas de pós-venda</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        <i class="fas fa-heart text-danger me-1"></i>
                        Desenvolvido para otimizar o atendimento ao cliente
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemsStatus();
            loadQuickStats();
        });

        async function checkSystemsStatus() {
            try {
                const response = await fetch('/health');
                const data = await response.json();

                const statusElement = document.getElementById('systemStatus');
                const spinnerElement = document.getElementById('statusSpinner');

                spinnerElement.style.display = 'none';

                if (data.status === 'healthy') {
                    statusElement.innerHTML = `
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-2">
                                <i class="fas fa-check-circle me-1"></i>3CX
                            </span>
                            <span class="badge bg-success">
                                <i class="fab fa-whatsapp me-1"></i>WhatsApp
                            </span>
                        </div>
                    `;
                } else {
                    statusElement.innerHTML = `
                        <div class="d-flex align-items-center">
                            <span class="badge ${data['3cx_connection'] ? 'bg-success' : 'bg-danger'} me-2">
                                <i class="fas ${data['3cx_connection'] ? 'fa-check-circle' : 'fa-times-circle'} me-1"></i>3CX
                            </span>
                            <span class="badge ${data['chatwoot_connection'] ? 'bg-success' : 'bg-danger'}">
                                <i class="fab fa-whatsapp me-1"></i>WhatsApp
                            </span>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Erro ao verificar status:', error);
                document.getElementById('statusSpinner').style.display = 'none';
                document.getElementById('systemStatus').innerHTML = `
                    <div class="d-flex align-items-center">
                        <span class="badge bg-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i>Verificando...
                        </span>
                    </div>
                `;
            }
        }

        async function loadQuickStats() {
            try {
                // Simular carregamento de estatísticas rápidas
                // Em uma implementação real, estes dados viriam de endpoints específicos

                // Simular delay de carregamento
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Dados simulados
                document.getElementById('totalReports').textContent = '1,247';
                document.getElementById('totalCalls').textContent = '45,892';
                document.getElementById('totalConversations').textContent = '12,456';
                document.getElementById('avgSatisfaction').textContent = '4.2/5';

                // Animar números
                animateNumbers();

            } catch (error) {
                console.error('Erro ao carregar estatísticas:', error);
                // Manter valores padrão em caso de erro
            }
        }

        function animateNumbers() {
            const statNumbers = document.querySelectorAll('.stat-number');

            statNumbers.forEach(element => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    element.style.transition = 'all 0.6s ease';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, Math.random() * 500);
            });
        }

        // Adicionar efeitos de hover nos cards
        document.querySelectorAll('.system-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
