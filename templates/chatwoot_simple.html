<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Date adapter for Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

    <style>
        :root {
            /* Light Theme - Adaptado para WhatsApp */
            --bg-primary: #ffffff;
            --bg-secondary: #f0f2f5;
            --bg-tertiary: #e9edef;
            --text-primary: #111b21;
            --text-secondary: #667781;
            --text-muted: #8696a0;
            --border-color: #e9edef;
            --accent-primary: #25d366;
            --accent-secondary: #128c7e;
            --success: #25d366;
            --warning: #ffb84d;
            --danger: #f15c6d;
            --info: #54b3d6;
            --sidebar-bg: #111b21;
            --sidebar-text: #aebac1;
            --sidebar-active: #25d366;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }

        [data-theme="dark"] {
            /* Dark Theme - WhatsApp Dark */
            --bg-primary: #111b21;
            --bg-secondary: #0b141a;
            --bg-tertiary: #202c33;
            --text-primary: #e9edef;
            --text-secondary: #aebac1;
            --text-muted: #8696a0;
            --border-color: #2a3942;
            --accent-primary: #25d366;
            --accent-secondary: #20b358;
            --success: #25d366;
            --warning: #ffb84d;
            --danger: #f15c6d;
            --info: #54b3d6;
            --sidebar-bg: #0b141a;
            --sidebar-text: #8696a0;
            --sidebar-active: #25d366;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            transition: all 0.3s ease;
            overflow-x: hidden;
        }

        /* Header Navigation */
        .main-header {
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 70px;
        }

        .header-nav {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-menu-item {
            position: relative;
        }

        .nav-menu-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .nav-menu-link:hover {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        .nav-menu-link.active {
            background: var(--accent-primary);
            color: white;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Main Content */
        .main-content {
            min-height: calc(100vh - 70px);
        }

        .theme-toggle {
            background: var(--bg-tertiary);
            border: none;
            color: var(--text-secondary);
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .theme-toggle:hover {
            background: var(--accent-primary);
            color: white;
        }

        .current-time {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
            padding: 0.5rem 1rem;
            background: var(--bg-tertiary);
            border-radius: 8px;
        }

        /* Content Area */
        .content-area {
            padding: 2rem;
        }

        /* Cards */
        .card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .card-header {
            background: transparent;
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            border-radius: 12px 12px 0 0 !important;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Metric Cards */
        .metric-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--accent-primary);
        }

        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .metric-icon {
            font-size: 2rem;
            color: var(--accent-primary);
            margin-bottom: 1rem;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .metric-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Tables */
        .table {
            color: var(--text-primary);
            margin-bottom: 0;
        }

        .table th {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            font-weight: 600;
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0.75rem;
        }

        .table td {
            border-bottom: 1px solid var(--border-color);
            padding: 0.75rem;
            vertical-align: middle;
        }

        .table-responsive {
            border-radius: 8px;
            border: 1px solid var(--border-color);
            max-height: 400px;
            overflow-y: auto;
        }

        /* Buttons */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 0.625rem 1.25rem;
            transition: all 0.2s ease;
            border: none;
        }

        .btn-primary {
            background: var(--accent-primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--accent-secondary);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-outline-primary {
            border: 1px solid var(--accent-primary);
            color: var(--accent-primary);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--accent-primary);
            color: white;
        }

        .btn-outline-secondary {
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            background: transparent;
        }

        .btn-outline-secondary:hover {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        /* Form Controls */
        .form-control {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            border-radius: 8px;
            padding: 0.625rem 1rem;
            transition: all 0.2s ease;
        }

        .form-control:focus {
            border-color: var(--accent-primary);
            box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25);
            background: var(--bg-primary);
            color: var(--text-primary);
        }

        .form-label {
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        /* Tabs */
        .nav-tabs {
            border-bottom: 1px solid var(--border-color);
        }

        .nav-tabs .nav-link {
            border: none;
            color: var(--text-secondary);
            font-weight: 500;
            padding: 1rem 1.5rem;
            border-radius: 0;
            transition: all 0.2s ease;
        }

        .nav-tabs .nav-link:hover {
            border-color: transparent;
            color: var(--text-primary);
            background: var(--bg-tertiary);
        }

        .nav-tabs .nav-link.active {
            color: var(--accent-primary);
            background: transparent;
            border-color: transparent transparent var(--accent-primary) transparent;
            border-bottom-width: 2px;
        }

        /* Chart Container */
        .chart-container {
            position: relative;
            height: 400px;
            padding: 1rem;
        }

        /* Loading States */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            z-index: 10;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--accent-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Badges */
        .badge {
            font-weight: 500;
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
        }

        .bg-success {
            background-color: var(--success) !important;
        }

        .bg-warning {
            background-color: var(--warning) !important;
        }

        .bg-danger {
            background-color: var(--danger) !important;
        }

        .bg-info {
            background-color: var(--info) !important;
        }

        /* Agent Performance Badges */
        .agent-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .badge-excellent {
            background: rgba(37, 211, 102, 0.1);
            color: var(--success);
            border: 1px solid rgba(37, 211, 102, 0.2);
        }

        .badge-good {
            background: rgba(84, 179, 214, 0.1);
            color: var(--info);
            border: 1px solid rgba(84, 179, 214, 0.2);
        }

        .badge-average {
            background: rgba(255, 184, 77, 0.1);
            color: var(--warning);
            border: 1px solid rgba(255, 184, 77, 0.2);
        }

        .badge-poor {
            background: rgba(241, 92, 109, 0.1);
            color: var(--danger);
            border: 1px solid rgba(241, 92, 109, 0.2);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-container {
                padding: 0 1rem;
            }

            .content-area {
                padding: 1rem;
            }

            .metric-value {
                font-size: 2rem;
            }

            .chart-container {
                height: 300px;
                padding: 0.5rem;
            }

            .nav-tabs .nav-link {
                padding: 0.75rem 1rem;
                font-size: 0.875rem;
            }
        }

        @media (max-width: 576px) {
            .header-container {
                flex-direction: column;
                height: auto;
                padding: 1rem;
                gap: 1rem;
            }

            .nav-menu {
                gap: 0.5rem;
            }

            .nav-menu-link {
                padding: 0.5rem 0.75rem;
                font-size: 0.875rem;
            }
        }
    </style>
</head>
<body>
    <!-- Main Header -->
    <header class="main-header">
        <div class="header-container">
            <nav class="header-nav">
                <ul class="nav-menu">
                    <li class="nav-menu-item">
                        <a href="/" class="nav-menu-link">
                            <i class="fas fa-phone"></i>
                            <span>Telefonia</span>
                        </a>
                    </li>
                    <li class="nav-menu-item">
                        <a href="/chatwoot" class="nav-menu-link active">
                            <i class="fab fa-whatsapp"></i>
                            <span>WhatsApp</span>
                        </a>
                    </li>
                    <li class="nav-menu-item">
                        <a href="#" class="nav-menu-link">
                            <i class="fas fa-chart-pie"></i>
                            <span>Consolidado</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="header-actions">
                <button class="theme-toggle" id="themeToggle" title="Alternar tema">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="current-time" id="current-time"></div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="main-content">
        <div class="content-area">
            <!-- Status de Conexão -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fab fa-whatsapp" style="font-size: 3rem; color: var(--accent-primary); margin-bottom: 1rem;"></i>
                            <h2 class="mb-3">Dashboard Chatwoot - WhatsApp</h2>
                            <p class="text-muted mb-3">Métricas e relatórios do atendimento via WhatsApp</p>
                            <div id="connectionStatus">
                                <span class="badge bg-secondary">
                                    <i class="fas fa-spinner fa-spin me-2"></i>Verificando conexão...
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Filtros -->
            <div class="row" id="filtersSection" style="display: none;">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-filter me-2"></i>
                                Filtros de Data
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="dateFilterForm" class="row align-items-end">
                                <div class="col-md-3">
                                    <label for="startDate" class="form-label">
                                        <i class="fas fa-calendar-alt me-2"></i>Data Inicial
                                    </label>
                                    <input type="date" class="form-control" id="startDate" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="endDate" class="form-label">
                                        <i class="fas fa-calendar-alt me-2"></i>Data Final
                                    </label>
                                    <input type="date" class="form-control" id="endDate" required>
                                </div>
                                <div class="col-md-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>Atualizar
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="setQuickDate('today')">
                                        Hoje
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-outline-secondary" onclick="setQuickDate('week')">
                                        7 dias
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="setQuickDate('month')">
                                        30 dias
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Loading -->
            <div class="text-center" id="loadingSpinner" style="display: none;">
                <div class="card">
                    <div class="card-body">
                        <div class="loading-spinner mx-auto mb-3"></div>
                        <p class="text-muted">Carregando métricas...</p>
                    </div>
                </div>
            </div>

            <!-- Métricas Principais -->
            <div class="row" id="metricsSection" style="display: none;">
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="metric-value" id="totalConversations">-</div>
                        <div class="metric-label">Conversas</div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="metric-value" id="resolvedConversations">-</div>
                        <div class="metric-label">Resolvidas</div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="metric-value" id="avgResponseTime">-</div>
                        <div class="metric-label">Tempo Médio</div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="metric-value" id="activeAgents">-</div>
                        <div class="metric-label">Agentes</div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="metric-value" id="totalMessages">-</div>
                        <div class="metric-label">Mensagens</div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div class="metric-card">
                        <div class="metric-icon">
                            <i class="fas fa-address-book"></i>
                        </div>
                        <div class="metric-value" id="totalContacts">-</div>
                        <div class="metric-label">Contatos</div>
                    </div>
                </div>
            </div>

        <!-- Abas de Navegação -->
        <div class="row" id="tabsSection" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-body p-0">
                        <ul class="nav nav-tabs nav-fill" id="dashboardTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                                    <i class="fas fa-chart-line me-2"></i>Visão Geral
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="agents-tab" data-bs-toggle="tab" data-bs-target="#agents" type="button" role="tab">
                                    <i class="fas fa-users me-2"></i>Agentes
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="messages-tab" data-bs-toggle="tab" data-bs-target="#messages" type="button" role="tab">
                                    <i class="fas fa-envelope me-2"></i>Mensagens
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="contacts-tab" data-bs-toggle="tab" data-bs-target="#contacts" type="button" role="tab">
                                    <i class="fas fa-address-book me-2"></i>Contatos
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="response-times-tab" data-bs-toggle="tab" data-bs-target="#response-times" type="button" role="tab">
                                    <i class="fas fa-stopwatch me-2"></i>Tempos
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="workload-tab" data-bs-toggle="tab" data-bs-target="#workload" type="button" role="tab">
                                    <i class="fas fa-tasks me-2"></i>Carga
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Conteúdo das Abas -->
        <div class="tab-content" id="dashboardTabsContent" style="display: none;">
            <!-- Aba Visão Geral -->
            <div class="tab-pane fade show active" id="overview" role="tabpanel">
                <div class="row">
                    <!-- Volume Diário -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    Volume Diário de Conversas
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="dailyVolumeChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Status das Conversas -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    Status das Conversas
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="statusChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Volume por Hora -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    Volume por Hora do Dia
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="hourlyChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Taxa de Resolução -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-percentage me-2"></i>
                                    Taxa de Resolução por Agente
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="resolutionChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aba Agentes -->
            <div class="tab-pane fade" id="agents" role="tabpanel">
                <div class="row">
                    <!-- Tabela de Performance dos Agentes -->
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i>
                                    Performance Detalhada dos Agentes
                                </h5>
                                <button class="btn btn-sm btn-outline-primary" onclick="exportAgentsData()">
                                    <i class="fas fa-download me-2"></i>Exportar CSV
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-container">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Agente</th>
                                                <th>Conversas</th>
                                                <th>Resolvidas</th>
                                                <th>Taxa Resolução</th>
                                                <th>Tempo Médio</th>
                                                <th>Mensagens</th>
                                                <th>Performance</th>
                                            </tr>
                                        </thead>
                                        <tbody id="agentsTableBody">
                                            <tr>
                                                <td colspan="7" class="text-center text-muted">
                                                    <i class="fas fa-spinner fa-spin me-2"></i>
                                                    Carregando dados dos agentes...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Carga de Trabalho -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    Distribuição de Carga
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="workloadChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Atividade por Dia da Semana -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-calendar-week me-2"></i>
                                    Atividade por Dia da Semana
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="weekdayChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aba Mensagens -->
            <div class="tab-pane fade" id="messages" role="tabpanel">
                <div class="row">
                    <!-- Métricas de Mensagens -->
                    <div class="col-lg-3 col-md-6">
                        <div class="card metric-card">
                            <div class="metric-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="metric-value" id="totalMessagesDetail">-</div>
                            <div class="metric-label">Total Mensagens</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card metric-card">
                            <div class="metric-icon">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="metric-value" id="incomingMessages">-</div>
                            <div class="metric-label">Recebidas</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card metric-card">
                            <div class="metric-icon">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="metric-value" id="outgoingMessages">-</div>
                            <div class="metric-label">Enviadas</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card metric-card">
                            <div class="metric-icon">
                                <i class="fas fa-ruler"></i>
                            </div>
                            <div class="metric-value" id="avgMessageLength">-</div>
                            <div class="metric-label">Tamanho Médio</div>
                        </div>
                    </div>

                    <!-- Volume de Mensagens por Hora -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-area me-2"></i>
                                    Volume de Mensagens por Hora
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="messagesHourlyChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tipos de Mensagem -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    Tipos de Mensagem
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="messageTypesChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Volume Diário de Mensagens -->
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    Volume Diário de Mensagens
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="messagesDailyChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aba Contatos -->
            <div class="tab-pane fade" id="contacts" role="tabpanel">
                <div class="row">
                    <!-- Métricas de Contatos -->
                    <div class="col-lg-3 col-md-6">
                        <div class="card metric-card">
                            <div class="metric-icon">
                                <i class="fas fa-address-book"></i>
                            </div>
                            <div class="metric-value" id="totalContactsDetail">-</div>
                            <div class="metric-label">Total Contatos</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card metric-card">
                            <div class="metric-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="metric-value" id="newContacts">-</div>
                            <div class="metric-label">Novos Contatos</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card metric-card">
                            <div class="metric-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="metric-value" id="contactsWithPhone">-</div>
                            <div class="metric-label">Com Telefone</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card metric-card">
                            <div class="metric-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="metric-value" id="contactsWithEmail">-</div>
                            <div class="metric-label">Com Email</div>
                        </div>
                    </div>

                    <!-- Novos Contatos por Dia -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    Novos Contatos por Dia
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="newContactsChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contatos Mais Ativos -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-star me-2"></i>
                                    Top 10 Contatos Ativos
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-container" style="max-height: 400px;">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Nome</th>
                                                <th>Conversas</th>
                                                <th>Mensagens</th>
                                            </tr>
                                        </thead>
                                        <tbody id="activeContactsTable">
                                            <tr>
                                                <td colspan="3" class="text-center text-muted">
                                                    <i class="fas fa-spinner fa-spin"></i>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aba Tempos de Resposta -->
            <div class="tab-pane fade" id="response-times" role="tabpanel">
                <div class="row">
                    <!-- Distribuição de Tempos -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    Distribuição de Tempos de Resposta
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="responseTimeDistributionChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance por Hora -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-clock me-2"></i>
                                    Tempo de Resposta por Hora
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="responseTimeHourlyChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tabela Detalhada de Tempos -->
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-table me-2"></i>
                                    Análise Detalhada por Agente
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-container">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Agente</th>
                                                <th>Tempo Médio</th>
                                                <th>Tempo Mínimo</th>
                                                <th>Tempo Máximo</th>
                                                <th>Mediana</th>
                                                <th>< 5min</th>
                                                <th>< 15min</th>
                                                <th>> 1h</th>
                                            </tr>
                                        </thead>
                                        <tbody id="responseTimesTable">
                                            <tr>
                                                <td colspan="8" class="text-center text-muted">
                                                    <i class="fas fa-spinner fa-spin me-2"></i>
                                                    Carregando dados...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aba Carga de Trabalho -->
            <div class="tab-pane fade" id="workload" role="tabpanel">
                <div class="row">
                    <!-- Tabela de Carga de Trabalho -->
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-tasks me-2"></i>
                                    Carga de Trabalho Detalhada
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-container">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Agente</th>
                                                <th>Email</th>
                                                <th>Conversas</th>
                                                <th>Abertas</th>
                                                <th>Resolvidas</th>
                                                <th>Mensagens</th>
                                                <th>Conv/Dia</th>
                                                <th>Msg/Conv</th>
                                                <th>Dias Ativos</th>
                                            </tr>
                                        </thead>
                                        <tbody id="workloadTable">
                                            <tr>
                                                <td colspan="9" class="text-center text-muted">
                                                    <i class="fas fa-spinner fa-spin me-2"></i>
                                                    Carregando dados...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Variáveis globais para os gráficos
        let dailyVolumeChart, statusChart, hourlyChart, resolutionChart;

        document.addEventListener('DOMContentLoaded', function() {
            initializeTheme();
            updateClock();
            setInterval(updateClock, 1000);
            checkConnection();
            setupDateFilters();
            setupEventListeners();
        });

        // Theme Management
        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            updateThemeIcon(savedTheme);
        }

        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcon(newTheme);
        }

        function updateThemeIcon(theme) {
            const themeIcon = document.querySelector('#themeToggle i');
            if (themeIcon) {
                themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }

        // Clock
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const dateString = now.toLocaleDateString('pt-BR', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            });

            const clockElement = document.getElementById('current-time');
            if (clockElement) {
                clockElement.textContent = `${timeString} - ${dateString}`;
            }
        }

        // Event Listeners
        document.getElementById('themeToggle')?.addEventListener('click', toggleTheme);

        function setupDateFilters() {
            // Definir datas padrão (últimos 7 dias)
            const today = new Date();
            const weekAgo = new Date(today);
            weekAgo.setDate(today.getDate() - 7);

            document.getElementById('startDate').value = weekAgo.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
        }

        function setupEventListeners() {
            document.getElementById('dateFilterForm').addEventListener('submit', function(e) {
                e.preventDefault();
                loadMetrics();
            });
        }

        async function checkConnection() {
            try {
                const response = await fetch('/api/v1/chatwoot/health');
                const data = await response.json();

                const statusElement = document.getElementById('connectionStatus');

                if (data.status === 'healthy') {
                    statusElement.innerHTML = `
                        <span class="badge bg-success">
                            <i class="fas fa-check-circle me-2"></i>Conectado ao Chatwoot
                        </span>
                    `;
                    document.getElementById('filtersSection').style.display = 'block';
                    loadMetrics(); // Carregar dados iniciais
                } else {
                    statusElement.innerHTML = `
                        <span class="badge bg-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>Erro na conexão
                        </span>
                    `;
                }
            } catch (error) {
                console.error('Erro ao verificar conexão:', error);
                document.getElementById('connectionStatus').innerHTML = `
                    <span class="badge bg-danger">
                        <i class="fas fa-times-circle me-2"></i>Falha na conexão
                    </span>
                `;
            }
        }

        async function loadMetrics() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                alert('Por favor, selecione as datas inicial e final');
                return;
            }

            showLoading(true);

            try {
                // Carregar todas as métricas em paralelo
                const [metricsResponse, messagesResponse, contactsResponse, responseTimesResponse, workloadResponse] = await Promise.all([
                    fetch(`/api/v1/chatwoot/metrics?start_date=${startDate}&end_date=${endDate}`),
                    fetch(`/api/v1/chatwoot/messages?start_date=${startDate}&end_date=${endDate}`),
                    fetch(`/api/v1/chatwoot/contacts?start_date=${startDate}&end_date=${endDate}`),
                    fetch(`/api/v1/chatwoot/response-times?start_date=${startDate}&end_date=${endDate}`),
                    fetch(`/api/v1/chatwoot/workload?start_date=${startDate}&end_date=${endDate}`)
                ]);

                const [metricsData, messagesData, contactsData, responseTimesData, workloadData] = await Promise.all([
                    metricsResponse.json(),
                    messagesResponse.json(),
                    contactsResponse.json(),
                    responseTimesResponse.json(),
                    workloadResponse.json()
                ]);

                if (metricsData.success) {
                    // Atualizar métricas principais
                    updateMainMetrics(metricsData.summary, messagesData.summary, contactsData.summary);

                    // Atualizar gráficos da visão geral
                    updateOverviewCharts(metricsData);

                    // Atualizar dados das abas
                    updateAgentsData(metricsData.agents_performance, workloadData);
                    updateMessagesData(messagesData);
                    updateContactsData(contactsData);
                    updateResponseTimesData(responseTimesData);
                    updateWorkloadData(workloadData);

                    // Carregar satisfação
                    loadSatisfactionData(startDate, endDate);

                    // Mostrar seções
                    document.getElementById('metricsSection').style.display = 'block';
                    document.getElementById('tabsSection').style.display = 'block';
                    document.getElementById('dashboardTabsContent').style.display = 'block';
                } else {
                    throw new Error('Erro na resposta da API');
                }

            } catch (error) {
                console.error('Erro ao carregar métricas:', error);
                alert('Erro ao carregar dados: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        function updateMainMetrics(summary, messagesSummary, contactsSummary) {
            document.getElementById('totalConversations').textContent = summary.total_conversations || 0;
            document.getElementById('resolvedConversations').textContent = summary.resolved_conversations || 0;
            document.getElementById('avgResponseTime').textContent = Math.round(summary.avg_first_response_time_minutes || 0) + 'min';
            document.getElementById('activeAgents').textContent = summary.active_agents || 0;
            document.getElementById('totalMessages').textContent = messagesSummary?.total_messages || 0;
            document.getElementById('totalContacts').textContent = contactsSummary?.new_contacts_period || 0;
        }

        function updateOverviewCharts(data) {
            // Gráfico de Volume Diário
            updateDailyVolumeChart(data.daily_volume);

            // Gráfico de Status
            updateStatusChart(data.summary);

            // Gráfico por Hora
            updateHourlyChart(data.hourly_volume);

            // Gráfico de Taxa de Resolução
            updateResolutionChart(data.agents_performance);
        }

        function updateAgentsData(agentsPerformance, workloadData) {
            updateAgentsTable(agentsPerformance);
            if (workloadData.success) {
                updateWorkloadChart(workloadData.load_distribution);
                updateWeekdayChart(workloadData.weekday_activity);
            }
        }

        function updateMessagesData(data) {
            if (!data.success) return;

            const summary = data.summary;
            document.getElementById('totalMessagesDetail').textContent = summary.total_messages || 0;
            document.getElementById('incomingMessages').textContent = summary.incoming_messages || 0;
            document.getElementById('outgoingMessages').textContent = summary.outgoing_messages || 0;
            document.getElementById('avgMessageLength').textContent = Math.round(summary.avg_message_length || 0);

            updateMessagesHourlyChart(data.hourly_volume);
            updateMessageTypesChart(summary);
            updateMessagesDailyChart(data.daily_volume);
        }

        function updateContactsData(data) {
            if (!data.success) return;

            const summary = data.summary;
            document.getElementById('totalContactsDetail').textContent = summary.total_contacts || 0;
            document.getElementById('newContacts').textContent = summary.new_contacts_period || 0;
            document.getElementById('contactsWithPhone').textContent = summary.contacts_with_phone || 0;
            document.getElementById('contactsWithEmail').textContent = summary.contacts_with_email || 0;

            updateNewContactsChart(data.daily_new_contacts);
            updateActiveContactsTable(data.most_active);
        }

        function updateResponseTimesData(data) {
            if (!data.success) return;

            updateResponseTimeDistributionChart(data.time_distribution);
            updateResponseTimeHourlyChart(data.hourly_performance);
            updateResponseTimesTable(data.agent_performance);
        }

        function updateWorkloadData(data) {
            if (!data.success) return;

            updateWorkloadTable(data.agent_workload);
        }

        function updateDailyVolumeChart(dailyData) {
            const ctx = document.getElementById('dailyVolumeChart').getContext('2d');

            if (dailyVolumeChart) {
                dailyVolumeChart.destroy();
            }

            const labels = dailyData.map(item => new Date(item.date).toLocaleDateString('pt-BR'));
            const totalData = dailyData.map(item => item.conversations);
            const resolvedData = dailyData.map(item => item.resolved);

            dailyVolumeChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Total de Conversas',
                        data: totalData,
                        borderColor: '#25D366',
                        backgroundColor: 'rgba(37, 211, 102, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Conversas Resolvidas',
                        data: resolvedData,
                        borderColor: '#128C7E',
                        backgroundColor: 'rgba(18, 140, 126, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function updateStatusChart(summary) {
            const ctx = document.getElementById('statusChart').getContext('2d');

            if (statusChart) {
                statusChart.destroy();
            }

            statusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Resolvidas', 'Abertas', 'Pendentes', 'Pausadas'],
                    datasets: [{
                        data: [
                            summary.resolved_conversations || 0,
                            summary.open_conversations || 0,
                            summary.pending_conversations || 0,
                            summary.snoozed_conversations || 0
                        ],
                        backgroundColor: [
                            '#28a745',
                            '#dc3545',
                            '#ffc107',
                            '#6c757d'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function updateHourlyChart(hourlyData) {
            const ctx = document.getElementById('hourlyChart').getContext('2d');

            if (hourlyChart) {
                hourlyChart.destroy();
            }

            // Criar array de 24 horas
            const hours = Array.from({length: 24}, (_, i) => i);
            const data = hours.map(hour => {
                const found = hourlyData.find(item => item.hour === hour);
                return found ? found.conversations : 0;
            });

            hourlyChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: hours.map(h => `${h}:00`),
                    datasets: [{
                        label: 'Conversas por Hora',
                        data: data,
                        backgroundColor: '#25D366',
                        borderColor: '#128C7E',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function updateResolutionChart(agentsData) {
            const ctx = document.getElementById('resolutionChart').getContext('2d');

            if (resolutionChart) {
                resolutionChart.destroy();
            }

            const labels = agentsData.map(agent => agent.agent_name || 'Sem nome');
            const resolutionRates = agentsData.map(agent => agent.resolution_rate || 0);

            resolutionChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Taxa de Resolução (%)',
                        data: resolutionRates,
                        backgroundColor: resolutionRates.map(rate => {
                            if (rate >= 80) return '#28a745';
                            if (rate >= 60) return '#ffc107';
                            return '#dc3545';
                        }),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }

        function updateAgentsTable(agentsData) {
            const tbody = document.getElementById('agentsTableBody');

            if (!agentsData || agentsData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            Nenhum dado de agente encontrado para o período selecionado
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = agentsData.map(agent => {
                const resolutionRate = agent.resolution_rate || 0;
                const responseTime = agent.avg_response_time_minutes || 0;
                const performanceBadge = getPerformanceBadge(resolutionRate, responseTime);

                return `
                    <tr>
                        <td>
                            <strong>${agent.agent_name || 'Sem nome'}</strong>
                        </td>
                        <td>${agent.total_conversations || 0}</td>
                        <td>${agent.resolved_conversations || 0}</td>
                        <td>
                            <span class="badge bg-${resolutionRate >= 80 ? 'success' : resolutionRate >= 60 ? 'warning' : 'danger'}">
                                ${resolutionRate.toFixed(1)}%
                            </span>
                        </td>
                        <td>${responseTime.toFixed(1)}min</td>
                        <td>-</td>
                        <td>${performanceBadge}</td>
                    </tr>
                `;
            }).join('');
        }

        function updateMessagesHourlyChart(hourlyData) {
            const ctx = document.getElementById('messagesHourlyChart').getContext('2d');

            if (window.messagesHourlyChart) {
                window.messagesHourlyChart.destroy();
            }

            const hours = Array.from({length: 24}, (_, i) => i);
            const totalData = hours.map(hour => {
                const found = hourlyData.find(item => item.hour === hour);
                return found ? found.total_messages : 0;
            });
            const incomingData = hours.map(hour => {
                const found = hourlyData.find(item => item.hour === hour);
                return found ? found.incoming : 0;
            });
            const outgoingData = hours.map(hour => {
                const found = hourlyData.find(item => item.hour === hour);
                return found ? found.outgoing : 0;
            });

            window.messagesHourlyChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: hours.map(h => `${h}:00`),
                    datasets: [{
                        label: 'Mensagens Recebidas',
                        data: incomingData,
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Mensagens Enviadas',
                        data: outgoingData,
                        borderColor: '#25D366',
                        backgroundColor: 'rgba(37, 211, 102, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function updateMessageTypesChart(summary) {
            const ctx = document.getElementById('messageTypesChart').getContext('2d');

            if (window.messageTypesChart) {
                window.messageTypesChart.destroy();
            }

            window.messageTypesChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Texto', 'Imagem', 'Áudio', 'Vídeo', 'Arquivo', 'Localização'],
                    datasets: [{
                        data: [
                            summary.text_messages || 0,
                            summary.image_messages || 0,
                            summary.audio_messages || 0,
                            summary.video_messages || 0,
                            summary.file_messages || 0,
                            summary.location_messages || 0
                        ],
                        backgroundColor: [
                            '#25D366',
                            '#128C7E',
                            '#34ce57',
                            '#075e54',
                            '#dcf8c6',
                            '#25d366'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function updateMessagesDailyChart(dailyData) {
            const ctx = document.getElementById('messagesDailyChart').getContext('2d');

            if (window.messagesDailyChart) {
                window.messagesDailyChart.destroy();
            }

            const labels = dailyData.map(item => new Date(item.date).toLocaleDateString('pt-BR'));
            const totalData = dailyData.map(item => item.total_messages);
            const incomingData = dailyData.map(item => item.incoming);
            const outgoingData = dailyData.map(item => item.outgoing);

            window.messagesDailyChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Recebidas',
                        data: incomingData,
                        backgroundColor: 'rgba(220, 53, 69, 0.7)',
                        borderColor: '#dc3545',
                        borderWidth: 1
                    }, {
                        label: 'Enviadas',
                        data: outgoingData,
                        backgroundColor: 'rgba(37, 211, 102, 0.7)',
                        borderColor: '#25D366',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            stacked: true
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function getPerformanceBadge(resolutionRate, responseTime) {
            let badgeClass = 'badge-poor';
            let badgeText = 'Precisa Melhorar';

            if (resolutionRate >= 80 && responseTime <= 10) {
                badgeClass = 'badge-excellent';
                badgeText = 'Excelente';
            } else if (resolutionRate >= 70 && responseTime <= 20) {
                badgeClass = 'badge-good';
                badgeText = 'Bom';
            } else if (resolutionRate >= 60 && responseTime <= 30) {
                badgeClass = 'badge-average';
                badgeText = 'Regular';
            }

            return `<span class="agent-badge ${badgeClass}">${badgeText}</span>`;
        }

        async function loadSatisfactionData(startDate, endDate) {
            try {
                const response = await fetch(`/api/v1/chatwoot/satisfaction?start_date=${startDate}&end_date=${endDate}`);
                const data = await response.json();

                if (data.success && data.data.length > 0) {
                    document.getElementById('satisfactionSection').style.display = 'block';
                    updateSatisfactionContent(data);
                } else {
                    document.getElementById('satisfactionSection').style.display = 'none';
                }
            } catch (error) {
                console.error('Erro ao carregar dados de satisfação:', error);
                document.getElementById('satisfactionSection').style.display = 'none';
            }
        }

        function updateSatisfactionContent(data) {
            const content = document.getElementById('satisfactionContent');
            const summary = data.summary;

            content.innerHTML = `
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h3 class="text-success">${summary.total_ratings}</h3>
                        <p class="text-muted">Total de Avaliações</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h3 class="text-primary">${summary.average_rating}</h3>
                        <p class="text-muted">Avaliação Média</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h3 class="text-warning">${summary.satisfaction_rate}%</h3>
                        <p class="text-muted">Taxa de Satisfação</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h3 class="text-info">${summary.total_agents}</h3>
                        <p class="text-muted">Agentes Avaliados</p>
                    </div>
                </div>
            `;
        }

        function setQuickDate(period) {
            const today = new Date();
            const endDate = today.toISOString().split('T')[0];
            let startDate;

            switch(period) {
                case 'today':
                    startDate = endDate;
                    break;
                case 'week':
                    const weekAgo = new Date(today);
                    weekAgo.setDate(today.getDate() - 7);
                    startDate = weekAgo.toISOString().split('T')[0];
                    break;
                case 'month':
                    const monthAgo = new Date(today);
                    monthAgo.setDate(today.getDate() - 30);
                    startDate = monthAgo.toISOString().split('T')[0];
                    break;
            }

            document.getElementById('startDate').value = startDate;
            document.getElementById('endDate').value = endDate;

            loadMetrics();
        }

        function showLoading(show) {
            const spinner = document.getElementById('loadingSpinner');
            const sections = ['metricsSection', 'chartsSection', 'agentsSection'];

            if (show) {
                spinner.style.display = 'block';
                sections.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) element.style.opacity = '0.5';
                });
            } else {
                spinner.style.display = 'none';
                sections.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) element.style.opacity = '1';
                });
            }
        }

        function updateNewContactsChart(dailyData) {
            const ctx = document.getElementById('newContactsChart').getContext('2d');

            if (window.newContactsChart) {
                window.newContactsChart.destroy();
            }

            const labels = dailyData.map(item => new Date(item.date).toLocaleDateString('pt-BR'));
            const data = dailyData.map(item => item.new_contacts);

            window.newContactsChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Novos Contatos',
                        data: data,
                        borderColor: '#25D366',
                        backgroundColor: 'rgba(37, 211, 102, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function updateActiveContactsTable(activeContacts) {
            const tbody = document.getElementById('activeContactsTable');

            if (!activeContacts || activeContacts.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="3" class="text-center text-muted">
                            Nenhum contato ativo encontrado
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = activeContacts.slice(0, 10).map(contact => `
                <tr>
                    <td>
                        <strong>${contact.name || 'Sem nome'}</strong>
                        <br>
                        <small class="text-muted">${contact.phone_number || contact.email || 'Sem contato'}</small>
                    </td>
                    <td>${contact.total_conversations || 0}</td>
                    <td>${contact.total_messages || 0}</td>
                </tr>
            `).join('');
        }

        function updateResponseTimeDistributionChart(distributionData) {
            const ctx = document.getElementById('responseTimeDistributionChart').getContext('2d');

            if (window.responseTimeDistributionChart) {
                window.responseTimeDistributionChart.destroy();
            }

            const labels = distributionData.map(item => item.time_range);
            const data = distributionData.map(item => item.conversation_count);

            window.responseTimeDistributionChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            '#28a745',
                            '#20c997',
                            '#17a2b8',
                            '#ffc107',
                            '#fd7e14',
                            '#dc3545'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function updateResponseTimeHourlyChart(hourlyData) {
            const ctx = document.getElementById('responseTimeHourlyChart').getContext('2d');

            if (window.responseTimeHourlyChart) {
                window.responseTimeHourlyChart.destroy();
            }

            const hours = Array.from({length: 24}, (_, i) => i);
            const data = hours.map(hour => {
                const found = hourlyData.find(item => item.hour === hour);
                return found ? (found.avg_response_time_minutes || 0) : 0;
            });

            window.responseTimeHourlyChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: hours.map(h => `${h}:00`),
                    datasets: [{
                        label: 'Tempo Médio (min)',
                        data: data,
                        borderColor: '#ffc107',
                        backgroundColor: 'rgba(255, 193, 7, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function updateResponseTimesTable(responseTimesData) {
            const tbody = document.getElementById('responseTimesTable');

            if (!responseTimesData || responseTimesData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center text-muted">
                            Nenhum dado de tempo de resposta encontrado
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = responseTimesData.map(agent => `
                <tr>
                    <td><strong>${agent.agent_name || 'Sem nome'}</strong></td>
                    <td>${(agent.avg_first_response_minutes || 0).toFixed(1)}min</td>
                    <td>${(agent.min_first_response_minutes || 0).toFixed(1)}min</td>
                    <td>${(agent.max_first_response_minutes || 0).toFixed(1)}min</td>
                    <td>${(agent.median_first_response_minutes || 0).toFixed(1)}min</td>
                    <td><span class="badge bg-success">${agent.responses_under_5min || 0}</span></td>
                    <td><span class="badge bg-warning">${agent.responses_under_15min || 0}</span></td>
                    <td><span class="badge bg-danger">${agent.responses_over_1hour || 0}</span></td>
                </tr>
            `).join('');
        }

        function updateWorkloadChart(loadDistribution) {
            const ctx = document.getElementById('workloadChart').getContext('2d');

            if (window.workloadChart) {
                window.workloadChart.destroy();
            }

            const labels = loadDistribution.map(item => item.load_range);
            const data = loadDistribution.map(item => item.agent_count);

            window.workloadChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Número de Agentes',
                        data: data,
                        backgroundColor: '#25D366',
                        borderColor: '#128C7E',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function updateWeekdayChart(weekdayData) {
            const ctx = document.getElementById('weekdayChart').getContext('2d');

            if (window.weekdayChart) {
                window.weekdayChart.destroy();
            }

            const labels = weekdayData.map(item => item.day_name);
            const conversationsData = weekdayData.map(item => item.total_conversations);
            const responseTimeData = weekdayData.map(item => item.avg_response_time_minutes || 0);

            window.weekdayChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Conversas',
                        data: conversationsData,
                        backgroundColor: 'rgba(37, 211, 102, 0.7)',
                        borderColor: '#25D366',
                        borderWidth: 1,
                        yAxisID: 'y'
                    }, {
                        label: 'Tempo Médio (min)',
                        data: responseTimeData,
                        type: 'line',
                        borderColor: '#ffc107',
                        backgroundColor: 'rgba(255, 193, 7, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            beginAtZero: true
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            beginAtZero: true,
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }

        function updateWorkloadTable(workloadData) {
            const tbody = document.getElementById('workloadTable');

            if (!workloadData || workloadData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center text-muted">
                            Nenhum dado de carga de trabalho encontrado
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = workloadData.map(agent => `
                <tr>
                    <td><strong>${agent.agent_name || 'Sem nome'}</strong></td>
                    <td><small>${agent.agent_email || 'Sem email'}</small></td>
                    <td>${agent.total_conversations || 0}</td>
                    <td><span class="badge bg-warning">${agent.open_conversations || 0}</span></td>
                    <td><span class="badge bg-success">${agent.resolved_conversations || 0}</span></td>
                    <td>${agent.total_messages_sent || 0}</td>
                    <td>${agent.conversations_per_day || 0}</td>
                    <td>${agent.messages_per_conversation || 0}</td>
                    <td>${agent.active_days || 0}</td>
                </tr>
            `).join('');
        }

        function exportAgentsData() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                alert('Por favor, selecione as datas para exportar');
                return;
            }

            // Criar dados para exportação
            const table = document.getElementById('agentsTableBody');
            const rows = table.querySelectorAll('tr');

            let csvContent = "data:text/csv;charset=utf-8,";
            csvContent += "Agente,Total Conversas,Resolvidas,Taxa de Resolução,Tempo Médio (min),Performance\n";

            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length > 1) {
                    const rowData = Array.from(cells).map(cell => {
                        return cell.textContent.trim().replace(/,/g, ';');
                    }).join(',');
                    csvContent += rowData + "\n";
                }
            });

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `chatwoot_agentes_${startDate}_${endDate}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>
