<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f0f2f5;
            --text-primary: #111b21;
            --text-secondary: #667781;
            --accent-primary: #25d366;
            --accent-secondary: #128c7e;
            --success: #25d366;
            --warning: #ffb84d;
            --danger: #f15c6d;
            --info: #54b3d6;
            --border-color: #e9edef;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        .header {
            background: linear-gradient(135deg, var(--info) 0%, var(--accent-secondary) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-weight: 700;
            margin: 0;
        }

        .card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .metric-card {
            text-align: center;
            padding: 2rem;
        }

        .metric-value {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 500;
        }

        .metric-unit {
            font-size: 1.5rem;
            color: var(--text-secondary);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--info) 0%, var(--accent-secondary) 100%);
            border: none;
            padding: 0.75rem 2rem;
            font-weight: 500;
            border-radius: 8px;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 2rem 0;
        }

        .filters-section {
            background: var(--bg-primary);
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
        }

        .sla-indicator {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .sla-excellent {
            background-color: rgba(37, 211, 102, 0.1);
            color: var(--success);
        }

        .sla-good {
            background-color: rgba(32, 201, 151, 0.1);
            color: #20c997;
        }

        .sla-warning {
            background-color: rgba(255, 184, 77, 0.1);
            color: var(--warning);
        }

        .sla-critical {
            background-color: rgba(241, 92, 109, 0.1);
            color: var(--danger);
        }

        .progress {
            height: 8px;
            border-radius: 4px;
        }

        .table th {
            border-top: none;
            font-weight: 600;
            color: var(--text-primary);
        }

        .badge {
            font-size: 0.8rem;
            padding: 0.5rem 0.75rem;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .spinner {
            width: 24px;
            height: 24px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--info);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .back-button {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .time-range-card {
            background: linear-gradient(135deg, rgba(84, 179, 214, 0.1) 0%, rgba(37, 211, 102, 0.1) 100%);
            border: 2px solid var(--info);
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }

        .time-range-title {
            color: var(--info);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .header {
                padding: 1.5rem 0;
            }
            
            .metric-value {
                font-size: 2rem;
            }
            
            .chart-container {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <a href="/chatwoot" class="back-button me-3">
                        <i class="fas fa-arrow-left me-2"></i>Voltar ao Dashboard
                    </a>
                    <h1><i class="fas fa-clock me-3"></i>Tempo de Resposta - WhatsApp</h1>
                    <p class="mb-0 mt-2 opacity-75">Análise detalhada dos tempos de resposta dos agentes</p>
                </div>
                <div class="col-md-4 text-end">
                    <div id="connectionStatus">
                        <div class="loading">
                            <div class="spinner"></div>
                            Verificando conexão...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="container">
        <div class="filters-section">
            <h5><i class="fas fa-filter me-2"></i>Filtros de Consulta</h5>
            <form id="filterForm" class="row g-3">
                <div class="col-md-4">
                    <label for="startDate" class="form-label">Data Inicial</label>
                    <input type="date" class="form-control" id="startDate" required>
                </div>
                <div class="col-md-4">
                    <label for="endDate" class="form-label">Data Final</label>
                    <input type="date" class="form-control" id="endDate" required>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-2"></i>Gerar Relatório
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="setQuickFilter(7)">
                        Últimos 7 dias
                    </button>
                </div>
            </form>
        </div>

        <!-- SLA Information -->
        <div class="time-range-card" id="slaInfo" style="display: none;">
            <div class="time-range-title">
                <i class="fas fa-info-circle me-2"></i>Metas de SLA - Tempo de Resposta
            </div>
            <div class="row">
                <div class="col-md-3">
                    <div class="sla-indicator sla-excellent">
                        <i class="fas fa-check-circle me-2"></i>Excelente: ≤ 2 min
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="sla-indicator sla-good">
                        <i class="fas fa-thumbs-up me-2"></i>Bom: 2-5 min
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="sla-indicator sla-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>Atenção: 5-10 min
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="sla-indicator sla-critical">
                        <i class="fas fa-times-circle me-2"></i>Crítico: > 10 min
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Metrics -->
        <div class="row mb-4" id="metricsSection" style="display: none;">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="metric-value text-info" id="avgResponseTime">--</div>
                    <div class="metric-unit">minutos</div>
                    <div class="metric-label">Tempo Médio de Resposta</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="metric-value text-success" id="medianResponseTime">--</div>
                    <div class="metric-unit">minutos</div>
                    <div class="metric-label">Tempo Mediano</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="metric-value text-warning" id="maxResponseTime">--</div>
                    <div class="metric-unit">minutos</div>
                    <div class="metric-label">Tempo Máximo</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="metric-value text-primary" id="slaCompliance">--</div>
                    <div class="metric-unit">%</div>
                    <div class="metric-label">SLA Compliance</div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row" id="chartsSection" style="display: none;">
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Tendência de Tempo de Resposta</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="trendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Distribuição por Faixa</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="distributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hourly Analysis -->
        <div class="row" id="hourlySection" style="display: none;">
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Análise por Hora do Dia</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="hourlyChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Agents Performance Table -->
        <div class="row" id="tableSection" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-users me-2"></i>Performance por Agente</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Agente</th>
                                        <th>Total Conversas</th>
                                        <th>Tempo Médio</th>
                                        <th>Tempo Mínimo</th>
                                        <th>Tempo Máximo</th>
                                        <th>SLA Compliance</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody id="agentsTableBody">
                                    <tr>
                                        <td colspan="7" class="text-center">
                                            <div class="loading">
                                                <div class="spinner"></div>
                                                Carregando dados...
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Options -->
        <div class="row mt-4" id="exportSection" style="display: none;">
            <div class="col-12 text-center">
                <button class="btn btn-outline-primary me-2" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf me-2"></i>Exportar PDF
                </button>
                <button class="btn btn-outline-success" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-2"></i>Exportar Excel
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let trendChart, distributionChart, hourlyChart;

        document.addEventListener('DOMContentLoaded', function() {
            setupDateFilters();
            checkConnection();
            setupEventListeners();
        });

        function setupDateFilters() {
            const today = new Date();
            const weekAgo = new Date(today);
            weekAgo.setDate(today.getDate() - 7);

            document.getElementById('startDate').value = weekAgo.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
        }

        function setupEventListeners() {
            document.getElementById('filterForm').addEventListener('submit', function(e) {
                e.preventDefault();
                loadResponseTimeData();
            });
        }

        function setQuickFilter(days) {
            const today = new Date();
            const pastDate = new Date(today);
            pastDate.setDate(today.getDate() - days);

            document.getElementById('startDate').value = pastDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
            loadResponseTimeData();
        }

        async function checkConnection() {
            try {
                const response = await fetch('/api/v1/chatwoot/health');
                const data = await response.json();

                const statusElement = document.getElementById('connectionStatus');

                if (data.status === 'healthy') {
                    statusElement.innerHTML = `
                        <span class="badge bg-success">
                            <i class="fas fa-check-circle me-2"></i>Conectado
                        </span>
                    `;
                    // Carregar dados iniciais
                    loadResponseTimeData();
                } else {
                    statusElement.innerHTML = `
                        <span class="badge bg-danger">
                            <i class="fas fa-times-circle me-2"></i>Erro na conexão
                        </span>
                    `;
                }
            } catch (error) {
                console.error('Erro ao verificar conexão:', error);
                document.getElementById('connectionStatus').innerHTML = `
                    <span class="badge bg-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Falha na conexão
                    </span>
                `;
            }
        }

        async function loadResponseTimeData() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                alert('Por favor, selecione as datas inicial e final.');
                return;
            }

            showLoading();

            try {
                const response = await fetch(`/api/v1/chatwoot/response-time?start_date=${startDate}&end_date=${endDate}`);
                const data = await response.json();

                if (data.success) {
                    updateMetrics(data.data);
                    updateCharts(data.data);
                    updateAgentsTable(data.data.agents || []);
                    showSections();
                } else {
                    alert('Erro ao carregar dados: ' + (data.message || 'Erro desconhecido'));
                }

                hideLoading();

            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                alert('Erro ao carregar dados. Tente novamente.');
                hideLoading();
            }
        }

        function updateMetrics(data) {
            const summary = data.summary || {};

            document.getElementById('avgResponseTime').textContent = summary.avg_first_response_time_minutes ?
                Math.round(summary.avg_first_response_time_minutes) : '0';
            document.getElementById('medianResponseTime').textContent = summary.median_response_time_minutes ?
                Math.round(summary.median_response_time_minutes) : '0';
            document.getElementById('maxResponseTime').textContent = summary.max_response_time_minutes ?
                Math.round(summary.max_response_time_minutes) : '0';
            document.getElementById('slaCompliance').textContent = summary.sla_compliance || '0';
        }

        function updateCharts(data) {
            updateTrendChart(data.daily_trend || []);
            updateDistributionChart(data.time_distribution || []);
            updateHourlyChart(data.hourly_analysis || []);
        }

        function updateTrendChart(trendData) {
            const ctx = document.getElementById('trendChart').getContext('2d');

            if (trendChart) {
                trendChart.destroy();
            }

            const labels = trendData.map(item => new Date(item.date).toLocaleDateString('pt-BR'));
            const avgTimes = trendData.map(item => item.avg_response_time_minutes);
            const medianTimes = trendData.map(item => item.median_response_time_minutes);

            trendChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Tempo Médio',
                            data: avgTimes,
                            borderColor: '#54b3d6',
                            backgroundColor: 'rgba(84, 179, 214, 0.1)',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.4
                        },
                        {
                            label: 'Tempo Mediano',
                            data: medianTimes,
                            borderColor: '#25d366',
                            backgroundColor: 'rgba(37, 211, 102, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Tempo (minutos)'
                            }
                        }
                    }
                }
            });
        }

        function updateDistributionChart(distributionData) {
            const ctx = document.getElementById('distributionChart').getContext('2d');

            if (distributionChart) {
                distributionChart.destroy();
            }

            const labels = ['≤ 2 min', '2-5 min', '5-10 min', '> 10 min'];
            const data = [
                distributionData.find(d => d.range === 'excellent')?.count || 0,
                distributionData.find(d => d.range === 'good')?.count || 0,
                distributionData.find(d => d.range === 'warning')?.count || 0,
                distributionData.find(d => d.range === 'critical')?.count || 0
            ];

            distributionChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            '#25d366',
                            '#20c997',
                            '#ffb84d',
                            '#f15c6d'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function updateHourlyChart(hourlyData) {
            const ctx = document.getElementById('hourlyChart').getContext('2d');

            if (hourlyChart) {
                hourlyChart.destroy();
            }

            const labels = hourlyData.map(item => `${item.hour}:00`);
            const data = hourlyData.map(item => item.avg_response_time_minutes);

            hourlyChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Tempo Médio de Resposta (min)',
                        data: data,
                        backgroundColor: data.map(value => {
                            if (value <= 2) return '#25d366';
                            if (value <= 5) return '#20c997';
                            if (value <= 10) return '#ffb84d';
                            return '#f15c6d';
                        }),
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Tempo (minutos)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Hora do Dia'
                            }
                        }
                    }
                }
            });
        }

        function updateAgentsTable(agents) {
            const tbody = document.getElementById('agentsTableBody');

            if (agents.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">Nenhum dado disponível para o período selecionado</td></tr>';
                return;
            }

            tbody.innerHTML = agents.map(agent => {
                const avgTime = Math.round(agent.avg_first_response_time_minutes || 0);
                const minTime = Math.round(agent.min_response_time_minutes || 0);
                const maxTime = Math.round(agent.max_response_time_minutes || 0);
                const slaCompliance = agent.sla_compliance || 0;
                const status = getSLAStatus(avgTime);

                return `
                    <tr>
                        <td><strong>${agent.agent_name}</strong></td>
                        <td>${agent.total_conversations}</td>
                        <td>
                            <span class="badge ${getTimeBadgeColor(avgTime)}">${avgTime} min</span>
                        </td>
                        <td>${minTime} min</td>
                        <td>${maxTime} min</td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar ${getSLAProgressColor(slaCompliance)}"
                                     style="width: ${slaCompliance}%">
                                    ${slaCompliance}%
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="sla-indicator ${status.class}">
                                <i class="${status.icon} me-2"></i>${status.text}
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function getSLAStatus(avgTime) {
            if (avgTime <= 2) {
                return { class: 'sla-excellent', icon: 'fas fa-check-circle', text: 'Excelente' };
            } else if (avgTime <= 5) {
                return { class: 'sla-good', icon: 'fas fa-thumbs-up', text: 'Bom' };
            } else if (avgTime <= 10) {
                return { class: 'sla-warning', icon: 'fas fa-exclamation-triangle', text: 'Atenção' };
            } else {
                return { class: 'sla-critical', icon: 'fas fa-times-circle', text: 'Crítico' };
            }
        }

        function getTimeBadgeColor(time) {
            if (time <= 2) return 'bg-success';
            if (time <= 5) return 'bg-info';
            if (time <= 10) return 'bg-warning';
            return 'bg-danger';
        }

        function getSLAProgressColor(percentage) {
            if (percentage >= 90) return 'bg-success';
            if (percentage >= 70) return 'bg-warning';
            return 'bg-danger';
        }

        function showSections() {
            document.getElementById('slaInfo').style.display = 'block';
            document.getElementById('metricsSection').style.display = 'block';
            document.getElementById('chartsSection').style.display = 'block';
            document.getElementById('hourlySection').style.display = 'block';
            document.getElementById('tableSection').style.display = 'block';
            document.getElementById('exportSection').style.display = 'block';
        }

        function showLoading() {
            const loadingElements = document.querySelectorAll('.loading');
            loadingElements.forEach(el => el.style.display = 'flex');
        }

        function hideLoading() {
            const loadingElements = document.querySelectorAll('.loading');
            loadingElements.forEach(el => el.style.display = 'none');
        }

        function exportToPDF() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/generate-pdf/chatwoot-response-time';

            const startInput = document.createElement('input');
            startInput.type = 'hidden';
            startInput.name = 'start_date';
            startInput.value = startDate;

            const endInput = document.createElement('input');
            endInput.type = 'hidden';
            endInput.name = 'end_date';
            endInput.value = endDate;

            form.appendChild(startInput);
            form.appendChild(endInput);
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function exportToExcel() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            window.open(`/api/v1/chatwoot/response-time/export?start_date=${startDate}&end_date=${endDate}&format=excel`, '_blank');
        }
    </script>
</body>
</html>
