<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f0f2f5;
            --text-primary: #111b21;
            --text-secondary: #667781;
            --accent-primary: #25d366;
            --accent-secondary: #128c7e;
            --success: #25d366;
            --warning: #ffb84d;
            --danger: #f15c6d;
            --info: #54b3d6;
            --border-color: #e9edef;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        .header {
            background: linear-gradient(135deg, #6f42c1 0%, var(--accent-secondary) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-weight: 700;
            margin: 0;
        }

        .card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .metric-card {
            text-align: center;
            padding: 2rem;
        }

        .metric-value {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 500;
        }

        .btn-primary {
            background: linear-gradient(135deg, #6f42c1 0%, var(--accent-secondary) 100%);
            border: none;
            padding: 0.75rem 2rem;
            font-weight: 500;
            border-radius: 8px;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 2rem 0;
        }

        .filters-section {
            background: var(--bg-primary);
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
        }

        .performance-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .performance-excellent {
            background-color: rgba(37, 211, 102, 0.1);
            color: var(--success);
        }

        .performance-good {
            background-color: rgba(32, 201, 151, 0.1);
            color: #20c997;
        }

        .performance-average {
            background-color: rgba(255, 184, 77, 0.1);
            color: var(--warning);
        }

        .performance-poor {
            background-color: rgba(241, 92, 109, 0.1);
            color: var(--danger);
        }

        .agent-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .agent-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .agent-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .progress {
            height: 8px;
            border-radius: 4px;
        }

        .table th {
            border-top: none;
            font-weight: 600;
            color: var(--text-primary);
        }

        .badge {
            font-size: 0.8rem;
            padding: 0.5rem 0.75rem;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .spinner {
            width: 24px;
            height: 24px;
            border: 3px solid var(--border-color);
            border-top: 3px solid #6f42c1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .back-button {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .ranking-position {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: white;
        }

        .ranking-1 { background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%); color: #333; }
        .ranking-2 { background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%); color: #333; }
        .ranking-3 { background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%); }
        .ranking-other { background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%); }

        @media (max-width: 768px) {
            .header {
                padding: 1.5rem 0;
            }
            
            .metric-value {
                font-size: 2rem;
            }
            
            .chart-container {
                height: 300px;
            }
            
            .agent-card {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <a href="/chatwoot" class="back-button me-3">
                        <i class="fas fa-arrow-left me-2"></i>Voltar ao Dashboard
                    </a>
                    <h1><i class="fas fa-users me-3"></i>Performance Geral - WhatsApp</h1>
                    <p class="mb-0 mt-2 opacity-75">Análise completa da performance dos agentes</p>
                </div>
                <div class="col-md-4 text-end">
                    <div id="connectionStatus">
                        <div class="loading">
                            <div class="spinner"></div>
                            Verificando conexão...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="container">
        <div class="filters-section">
            <h5><i class="fas fa-filter me-2"></i>Filtros de Consulta</h5>
            <form id="filterForm" class="row g-3">
                <div class="col-md-4">
                    <label for="startDate" class="form-label">Data Inicial</label>
                    <input type="date" class="form-control" id="startDate" required>
                </div>
                <div class="col-md-4">
                    <label for="endDate" class="form-label">Data Final</label>
                    <input type="date" class="form-control" id="endDate" required>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-2"></i>Gerar Relatório
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="setQuickFilter(30)">
                        Últimos 30 dias
                    </button>
                </div>
            </form>
        </div>

        <!-- Main Metrics -->
        <div class="row mb-4" id="metricsSection" style="display: none;">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="metric-value text-primary" id="totalAgents">--</div>
                    <div class="metric-label">Total de Agentes</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="metric-value text-success" id="activeAgents">--</div>
                    <div class="metric-label">Agentes Ativos</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="metric-value text-warning" id="avgPerformanceScore">--</div>
                    <div class="metric-label">Score Médio</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="metric-value text-info" id="topPerformerScore">--</div>
                    <div class="metric-label">Melhor Performance</div>
                </div>
            </div>
        </div>

        <!-- Top Performers -->
        <div class="row mb-4" id="topPerformersSection" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Top 3 Performers</h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="topPerformersContent">
                            <!-- Conteúdo será preenchido dinamicamente -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row" id="chartsSection" style="display: none;">
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Performance por Agente</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="performanceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Distribuição de Performance</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="distributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Metrics Charts -->
        <div class="row" id="detailChartsSection" style="display: none;">
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Tempo de Resposta vs Satisfação</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="responseVsSatisfactionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Workload vs Performance</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="workloadChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Agents Ranking Table -->
        <div class="row" id="tableSection" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-list-ol me-2"></i>Ranking Completo de Agentes</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Posição</th>
                                        <th>Agente</th>
                                        <th>Conversas</th>
                                        <th>Tempo Resposta</th>
                                        <th>Taxa Resolução</th>
                                        <th>Satisfação</th>
                                        <th>Score Final</th>
                                        <th>Performance</th>
                                    </tr>
                                </thead>
                                <tbody id="agentsRankingTableBody">
                                    <tr>
                                        <td colspan="8" class="text-center">
                                            <div class="loading">
                                                <div class="spinner"></div>
                                                Carregando dados...
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Options -->
        <div class="row mt-4" id="exportSection" style="display: none;">
            <div class="col-12 text-center">
                <button class="btn btn-outline-primary me-2" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf me-2"></i>Exportar PDF
                </button>
                <button class="btn btn-outline-success" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-2"></i>Exportar Excel
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let performanceChart, distributionChart, responseVsSatisfactionChart, workloadChart;

        document.addEventListener('DOMContentLoaded', function() {
            setupDateFilters();
            checkConnection();
            setupEventListeners();
        });

        function setupDateFilters() {
            const today = new Date();
            const monthAgo = new Date(today);
            monthAgo.setDate(today.getDate() - 30);

            document.getElementById('startDate').value = monthAgo.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
        }

        function setupEventListeners() {
            document.getElementById('filterForm').addEventListener('submit', function(e) {
                e.preventDefault();
                loadPerformanceData();
            });
        }

        function setQuickFilter(days) {
            const today = new Date();
            const pastDate = new Date(today);
            pastDate.setDate(today.getDate() - days);

            document.getElementById('startDate').value = pastDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
            loadPerformanceData();
        }

        async function checkConnection() {
            try {
                const response = await fetch('/api/v1/chatwoot/health');
                const data = await response.json();

                const statusElement = document.getElementById('connectionStatus');

                if (data.status === 'healthy') {
                    statusElement.innerHTML = `
                        <span class="badge bg-success">
                            <i class="fas fa-check-circle me-2"></i>Conectado
                        </span>
                    `;
                    // Carregar dados iniciais
                    loadPerformanceData();
                } else {
                    statusElement.innerHTML = `
                        <span class="badge bg-danger">
                            <i class="fas fa-times-circle me-2"></i>Erro na conexão
                        </span>
                    `;
                }
            } catch (error) {
                console.error('Erro ao verificar conexão:', error);
                document.getElementById('connectionStatus').innerHTML = `
                    <span class="badge bg-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Falha na conexão
                    </span>
                `;
            }
        }

        async function loadPerformanceData() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                alert('Por favor, selecione as datas inicial e final.');
                return;
            }

            showLoading();

            try {
                const response = await fetch(`/api/v1/chatwoot/performance?start_date=${startDate}&end_date=${endDate}`);
                const data = await response.json();

                if (data.success) {
                    updateMetrics(data.data);
                    updateTopPerformers(data.data.agents || []);
                    updateCharts(data.data);
                    updateAgentsRankingTable(data.data.agents || []);
                    showSections();
                } else {
                    alert('Erro ao carregar dados: ' + (data.message || 'Erro desconhecido'));
                }

                hideLoading();

            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                alert('Erro ao carregar dados. Tente novamente.');
                hideLoading();
            }
        }

        function updateMetrics(data) {
            const summary = data.summary || {};

            document.getElementById('totalAgents').textContent = summary.total_agents || '0';
            document.getElementById('activeAgents').textContent = summary.active_agents || '0';
            document.getElementById('avgPerformanceScore').textContent = summary.avg_performance_score ?
                parseFloat(summary.avg_performance_score).toFixed(1) : '0.0';
            document.getElementById('topPerformerScore').textContent = summary.top_performer_score ?
                parseFloat(summary.top_performer_score).toFixed(1) : '0.0';
        }

        function updateTopPerformers(agents) {
            const topPerformersContent = document.getElementById('topPerformersContent');
            const topThree = agents.slice(0, 3);

            if (topThree.length === 0) {
                topPerformersContent.innerHTML = '<div class="col-12 text-center text-muted">Nenhum dado disponível</div>';
                return;
            }

            topPerformersContent.innerHTML = topThree.map((agent, index) => `
                <div class="col-md-4">
                    <div class="agent-card text-center">
                        <div class="d-flex align-items-center justify-content-center mb-3">
                            <div class="ranking-position ranking-${index + 1} me-3">
                                ${index + 1}
                            </div>
                            <div class="agent-avatar">
                                ${agent.agent_name.charAt(0).toUpperCase()}
                            </div>
                        </div>
                        <h6 class="fw-bold">${agent.agent_name}</h6>
                        <div class="row text-center mt-3">
                            <div class="col-6">
                                <small class="text-muted">Score</small>
                                <div class="fw-bold text-primary">${parseFloat(agent.performance_score).toFixed(1)}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Conversas</small>
                                <div class="fw-bold text-success">${agent.total_conversations}</div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="performance-badge ${getPerformanceClass(agent.performance_score)}">
                                <i class="${getPerformanceIcon(agent.performance_score)} me-2"></i>
                                ${getPerformanceLabel(agent.performance_score)}
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function updateCharts(data) {
            updatePerformanceChart(data.agents || []);
            updateDistributionChart(data.performance_distribution || {});
            updateResponseVsSatisfactionChart(data.agents || []);
            updateWorkloadChart(data.agents || []);
        }

        function updatePerformanceChart(agents) {
            const ctx = document.getElementById('performanceChart').getContext('2d');

            if (performanceChart) {
                performanceChart.destroy();
            }

            const labels = agents.map(agent => agent.agent_name);
            const scores = agents.map(agent => agent.performance_score);

            performanceChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Score de Performance',
                        data: scores,
                        backgroundColor: scores.map(score => {
                            if (score >= 8.5) return '#25d366';
                            if (score >= 7.0) return '#20c997';
                            if (score >= 5.5) return '#ffb84d';
                            return '#f15c6d';
                        }),
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 10,
                            title: {
                                display: true,
                                text: 'Score (0-10)'
                            }
                        }
                    }
                }
            });
        }

        function updateDistributionChart(distribution) {
            const ctx = document.getElementById('distributionChart').getContext('2d');

            if (distributionChart) {
                distributionChart.destroy();
            }

            distributionChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Excelente (8.5+)', 'Bom (7.0-8.4)', 'Regular (5.5-6.9)', 'Ruim (<5.5)'],
                    datasets: [{
                        data: [
                            distribution.excellent || 0,
                            distribution.good || 0,
                            distribution.average || 0,
                            distribution.poor || 0
                        ],
                        backgroundColor: [
                            '#25d366',
                            '#20c997',
                            '#ffb84d',
                            '#f15c6d'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function updateResponseVsSatisfactionChart(agents) {
            const ctx = document.getElementById('responseVsSatisfactionChart').getContext('2d');

            if (responseVsSatisfactionChart) {
                responseVsSatisfactionChart.destroy();
            }

            responseVsSatisfactionChart = new Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'Agentes',
                        data: agents.map(agent => ({
                            x: agent.avg_response_time_minutes || 0,
                            y: agent.satisfaction_rating || 0
                        })),
                        backgroundColor: agents.map(agent => {
                            const score = agent.performance_score;
                            if (score >= 8.5) return '#25d366';
                            if (score >= 7.0) return '#20c997';
                            if (score >= 5.5) return '#ffb84d';
                            return '#f15c6d';
                        }),
                        borderColor: '#ffffff',
                        borderWidth: 2,
                        pointRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const agent = agents[context.dataIndex];
                                    return `${agent.agent_name}: ${context.parsed.x}min, ${context.parsed.y}/5`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Tempo de Resposta (min)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Satisfação (1-5)'
                            },
                            min: 0,
                            max: 5
                        }
                    }
                }
            });
        }

        function updateWorkloadChart(agents) {
            const ctx = document.getElementById('workloadChart').getContext('2d');

            if (workloadChart) {
                workloadChart.destroy();
            }

            const labels = agents.map(agent => agent.agent_name);
            const conversations = agents.map(agent => agent.total_conversations);
            const scores = agents.map(agent => agent.performance_score);

            workloadChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Conversas',
                            data: conversations,
                            borderColor: '#54b3d6',
                            backgroundColor: 'rgba(84, 179, 214, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Performance Score',
                            data: scores,
                            borderColor: '#25d366',
                            backgroundColor: 'rgba(37, 211, 102, 0.1)',
                            borderWidth: 3,
                            fill: false,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Número de Conversas'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Score de Performance'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                            min: 0,
                            max: 10
                        }
                    }
                }
            });
        }

        function updateAgentsRankingTable(agents) {
            const tbody = document.getElementById('agentsRankingTableBody');

            if (agents.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">Nenhum dado disponível para o período selecionado</td></tr>';
                return;
            }

            tbody.innerHTML = agents.map((agent, index) => {
                const responseTime = Math.round(agent.avg_response_time_minutes || 0);
                const resolutionRate = agent.resolution_rate || 0;
                const satisfaction = parseFloat(agent.satisfaction_rating || 0).toFixed(1);
                const score = parseFloat(agent.performance_score || 0).toFixed(1);

                return `
                    <tr>
                        <td>
                            <div class="ranking-position ranking-${index < 3 ? index + 1 : 'other'}">
                                ${index + 1}
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="agent-avatar me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                    ${agent.agent_name.charAt(0).toUpperCase()}
                                </div>
                                <strong>${agent.agent_name}</strong>
                            </div>
                        </td>
                        <td>${agent.total_conversations}</td>
                        <td>
                            <span class="badge ${getTimeBadgeColor(responseTime)}">${responseTime} min</span>
                        </td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar ${getResolutionProgressColor(resolutionRate)}"
                                     style="width: ${resolutionRate}%">
                                    ${resolutionRate}%
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="me-2">${satisfaction}/5</span>
                            ${generateStars(satisfaction)}
                        </td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar ${getScoreProgressColor(score)}"
                                     style="width: ${score * 10}%">
                                    ${score}
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="performance-badge ${getPerformanceClass(score)}">
                                <i class="${getPerformanceIcon(score)} me-2"></i>
                                ${getPerformanceLabel(score)}
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Funções auxiliares
        function getPerformanceClass(score) {
            if (score >= 8.5) return 'performance-excellent';
            if (score >= 7.0) return 'performance-good';
            if (score >= 5.5) return 'performance-average';
            return 'performance-poor';
        }

        function getPerformanceIcon(score) {
            if (score >= 8.5) return 'fas fa-star';
            if (score >= 7.0) return 'fas fa-thumbs-up';
            if (score >= 5.5) return 'fas fa-meh';
            return 'fas fa-thumbs-down';
        }

        function getPerformanceLabel(score) {
            if (score >= 8.5) return 'Excelente';
            if (score >= 7.0) return 'Bom';
            if (score >= 5.5) return 'Regular';
            return 'Ruim';
        }

        function getTimeBadgeColor(time) {
            if (time <= 2) return 'bg-success';
            if (time <= 5) return 'bg-info';
            if (time <= 10) return 'bg-warning';
            return 'bg-danger';
        }

        function getResolutionProgressColor(percentage) {
            if (percentage >= 90) return 'bg-success';
            if (percentage >= 70) return 'bg-warning';
            return 'bg-danger';
        }

        function getScoreProgressColor(score) {
            if (score >= 8.5) return 'bg-success';
            if (score >= 7.0) return 'bg-info';
            if (score >= 5.5) return 'bg-warning';
            return 'bg-danger';
        }

        function generateStars(rating) {
            const fullStars = Math.floor(rating);
            const hasHalfStar = rating % 1 >= 0.5;
            let starsHtml = '';

            for (let i = 0; i < 5; i++) {
                if (i < fullStars) {
                    starsHtml += '<i class="fas fa-star text-warning"></i>';
                } else if (i === fullStars && hasHalfStar) {
                    starsHtml += '<i class="fas fa-star-half-alt text-warning"></i>';
                } else {
                    starsHtml += '<i class="far fa-star text-muted"></i>';
                }
            }

            return starsHtml;
        }

        function showSections() {
            document.getElementById('metricsSection').style.display = 'block';
            document.getElementById('topPerformersSection').style.display = 'block';
            document.getElementById('chartsSection').style.display = 'block';
            document.getElementById('detailChartsSection').style.display = 'block';
            document.getElementById('tableSection').style.display = 'block';
            document.getElementById('exportSection').style.display = 'block';
        }

        function showLoading() {
            const loadingElements = document.querySelectorAll('.loading');
            loadingElements.forEach(el => el.style.display = 'flex');
        }

        function hideLoading() {
            const loadingElements = document.querySelectorAll('.loading');
            loadingElements.forEach(el => el.style.display = 'none');
        }

        function exportToPDF() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/generate-pdf/chatwoot-performance';

            const startInput = document.createElement('input');
            startInput.type = 'hidden';
            startInput.name = 'start_date';
            startInput.value = startDate;

            const endInput = document.createElement('input');
            endInput.type = 'hidden';
            endInput.name = 'end_date';
            endInput.value = endDate;

            form.appendChild(startInput);
            form.appendChild(endInput);
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function exportToExcel() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            window.open(`/api/v1/chatwoot/performance/export?start_date=${startDate}&end_date=${endDate}&format=excel`, '_blank');
        }
    </script>
</body>
</html>
