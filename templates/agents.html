{% extends "base.html" %}

{% block title %}Performance de Agentes - Sistema de Relatórios 3CX{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-5 text-primary">
                <i class="fas fa-users me-3"></i>
                Performance de Agentes
            </h1>
            <p class="lead text-muted">Análise detalhada da performance individual dos agentes</p>
        </div>
    </div>

    <!-- Filtros e Ações -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-2"></i>
            Filtros e Ações
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label for="start_date" class="form-label">Data Inicial</label>
                    <input type="date" class="form-control" id="start_date">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">Data Final</label>
                    <input type="date" class="form-control" id="end_date">
                </div>
                <div class="col-md-3">
                    <label for="queues" class="form-label">Filas</label>
                    <input type="text" class="form-control" id="queues" placeholder="802,803" value="{{ available_queues|join(',') }}">
                </div>
                <div class="col-md-3 d-flex align-items-end gap-2">
                    <button class="btn btn-primary" onclick="loadAgentsReport()">
                        <i class="fas fa-sync-alt me-2"></i>
                        Gerar
                    </button>
                    <button class="btn btn-success" onclick="generatePDF('agents')">
                        <i class="fas fa-file-pdf me-2"></i>
                        PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Resumo Executivo -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value" id="total-agents">-</div>
                    <div class="metric-label">Total de Agentes</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-success" id="active-agents">-</div>
                    <div class="metric-label">Agentes Ativos</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-info" id="avg-performance">-</div>
                    <div class="metric-label">Performance Média</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-warning" id="top-performer">-</div>
                    <div class="metric-label">Melhor Performance</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos -->
    <div class="row mb-4">
        <!-- Gráfico de Performance -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-2"></i>
                    Performance por Agente
                </div>
                <div class="card-body">
                    <div id="agents-performance-chart" class="chart-container">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Carregando gráfico...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ranking -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-trophy me-2"></i>
                    Ranking de Agentes
                </div>
                <div class="card-body">
                    <div id="agents-ranking">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Carregando ranking...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabela Detalhada -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-table me-2"></i>
                    Detalhes por Agente
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="agents-table">
                            <thead>
                                <tr>
                                    <th>Agente</th>
                                    <th>Chamadas Atendidas</th>
                                    <th>Tempo Médio</th>
                                    <th>Taxa de Atendimento</th>
                                    <th>Performance</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="5" class="text-center">
                                        <div class="loading">
                                            <i class="fas fa-spinner"></i><br>
                                            Carregando dados...
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Insights -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-lightbulb me-2"></i>
                    Insights e Recomendações
                </div>
                <div class="card-body">
                    <div id="insights-content">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Gerando insights...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let agentsChart;

    function loadAgentsReport() {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;
        const queues = document.getElementById('queues').value;

        if (!startDate || !endDate) {
            alert('Por favor, selecione as datas inicial e final.');
            return;
        }

        showLoading();

        // Simular carregamento de dados
        setTimeout(() => {
            updateMetrics();
            updateChart();
            updateTable();
            updateInsights();
            hideLoading();
        }, 2000);
    }

    function updateMetrics() {
        document.getElementById('total-agents').textContent = '8';
        document.getElementById('active-agents').textContent = '6';
        document.getElementById('avg-performance').textContent = '85%';
        document.getElementById('top-performer').textContent = '95%';
    }

    function updateChart() {
        // Implementar gráfico de performance
        document.getElementById('agents-performance-chart').innerHTML = '<p>Gráfico de performance dos agentes</p>';
    }

    function updateTable() {
        // Implementar tabela de agentes
        const tbody = document.querySelector('#agents-table tbody');
        tbody.innerHTML = `
            <tr>
                <td>Agente 1</td>
                <td>150</td>
                <td>3:45</td>
                <td>92%</td>
                <td><span class="badge bg-success">Excelente</span></td>
            </tr>
            <tr>
                <td>Agente 2</td>
                <td>120</td>
                <td>4:12</td>
                <td>88%</td>
                <td><span class="badge bg-primary">Bom</span></td>
            </tr>
        `;
    }

    function updateInsights() {
        document.getElementById('insights-content').innerHTML = `
            <div class="alert alert-info">
                <strong>Insight:</strong> Os agentes estão performando bem, com média de 85% de eficiência.
            </div>
        `;
    }

    function showLoading() {
        document.querySelectorAll('.loading').forEach(el => el.style.display = 'block');
    }

    function hideLoading() {
        document.querySelectorAll('.loading').forEach(el => el.style.display = 'none');
    }

    function generatePDF(type) {
        window.open(`/generate-pdf/${type}`, '_blank');
    }

    // Inicialização
    document.addEventListener('DOMContentLoaded', function() {
        const today = new Date();
        const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

        document.getElementById('end_date').value = today.toISOString().split('T')[0];
        document.getElementById('start_date').value = lastWeek.toISOString().split('T')[0];
    });
</script>
{% endblock %}