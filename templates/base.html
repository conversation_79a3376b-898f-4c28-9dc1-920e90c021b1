<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Sistema de Relatórios 3CX{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --dark-color: #34495e;
            --light-color: #ecf0f1;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: var(--primary-color) !important;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--dark-color), var(--primary-color));
            min-height: 100vh;
            padding-top: 20px;
        }
        
        .sidebar .nav-link {
            color: white;
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(5px);
        }
        
        .sidebar .nav-link.active {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: bold;
        }
        
        .metric-card {
            text-align: center;
            padding: 20px;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .metric-label {
            color: var(--dark-color);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: var(--dark-color);
        }
        
        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid var(--light-color);
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, var(--dark-color), var(--primary-color));
            color: white;
            border: none;
            font-weight: bold;
        }
        
        .footer {
            background-color: var(--dark-color);
            color: white;
            text-align: center;
            padding: 20px 0;
            margin-top: 50px;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>
                Sistema de Relatórios 3CX - Amvox
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="fas fa-clock me-1"></i>
                    <span id="current-time"></span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link" href="/">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Dashboard
                    </a>
                    <a class="nav-link" href="/reports/distribution">
                        <i class="fas fa-chart-pie me-2"></i>
                        Distribuição
                    </a>
                    <a class="nav-link" href="/reports/satisfaction">
                        <i class="fas fa-smile me-2"></i>
                        Satisfação
                    </a>
                    <a class="nav-link" href="/reports/timing">
                        <i class="fas fa-stopwatch me-2"></i>
                        Timing
                    </a>
                    <a class="nav-link" href="/reports/agents">
                        <i class="fas fa-users me-2"></i>
                        Agentes
                    </a>
                    <a class="nav-link" href="/reports/comparative">
                        <i class="fas fa-chart-line me-2"></i>
                        Comparativo
                    </a>
                    <hr class="text-white">
                    <a class="nav-link" href="/api/v1/reports/health">
                        <i class="fas fa-heartbeat me-2"></i>
                        Status Sistema
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 main-content">
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="mb-0">
                © 2025 Amvox - Sistema de Relatórios 3CX v2.1.0 | 
                <i class="fas fa-code me-1"></i>
                Desenvolvido com FastAPI e Bootstrap
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Atualiza relógio
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('pt-BR');
            document.getElementById('current-time').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);
        
        // Marca link ativo na sidebar
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });
        
        // Função para mostrar loading
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = '<div class="loading"><i class="fas fa-spinner"></i><br>Carregando...</div>';
            }
        }
        
        // Função para mostrar erro
        function showError(elementId, message) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>${message}</div>`;
            }
        }
        
        // Função para gerar PDF
        async function generatePDF(reportType) {
            const startDate = document.getElementById('start_date').value;
            const endDate = document.getElementById('end_date').value;
            const queues = document.getElementById('queues') ? document.getElementById('queues').value : '';
            
            if (!startDate || !endDate) {
                alert('Por favor, selecione as datas de início e fim.');
                return;
            }
            
            const formData = new FormData();
            formData.append('start_date', startDate);
            formData.append('end_date', endDate);
            formData.append('queues', queues);
            
            try {
                const response = await fetch(`/generate-pdf/${reportType}`, {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = `relatorio_${reportType}_${startDate}_${endDate}.pdf`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                } else {
                    alert('Erro ao gerar PDF. Tente novamente.');
                }
            } catch (error) {
                console.error('Erro:', error);
                alert('Erro ao gerar PDF. Tente novamente.');
            }
        }
        
        // Função para carregar gráfico
        async function loadChart(chartType, containerId) {
            const startDate = document.getElementById('start_date').value;
            const endDate = document.getElementById('end_date').value;
            const queues = document.getElementById('queues') ? document.getElementById('queues').value : '';
            
            if (!startDate || !endDate) {
                showError(containerId, 'Por favor, selecione as datas de início e fim.');
                return;
            }
            
            showLoading(containerId);
            
            try {
                const params = new URLSearchParams({
                    start_date: startDate,
                    end_date: endDate
                });
                
                if (queues) {
                    params.append('queues', queues);
                }
                
                const response = await fetch(`/api/charts/${chartType}?${params}`);
                
                if (response.ok) {
                    const html = await response.text();
                    document.getElementById(containerId).innerHTML = html;
                } else {
                    showError(containerId, 'Erro ao carregar gráfico.');
                }
            } catch (error) {
                console.error('Erro:', error);
                showError(containerId, 'Erro ao carregar gráfico.');
            }
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
