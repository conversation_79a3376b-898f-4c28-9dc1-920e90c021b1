<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f0f2f5;
            --text-primary: #111b21;
            --text-secondary: #667781;
            --accent-primary: #25d366;
            --accent-secondary: #128c7e;
            --success: #25d366;
            --warning: #ffb84d;
            --danger: #f15c6d;
            --info: #54b3d6;
            --border-color: #e9edef;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        .header {
            background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-weight: 700;
            margin: 0;
        }

        .card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .metric-card {
            text-align: center;
            padding: 2rem;
        }

        .metric-value {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 500;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
            border: none;
            padding: 0.75rem 2rem;
            font-weight: 500;
            border-radius: 8px;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 2rem 0;
        }

        .filters-section {
            background: var(--bg-primary);
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
        }

        .rating-stars {
            color: #ffc107;
            font-size: 1.2rem;
        }

        .progress {
            height: 8px;
            border-radius: 4px;
        }

        .table th {
            border-top: none;
            font-weight: 600;
            color: var(--text-primary);
        }

        .badge {
            font-size: 0.8rem;
            padding: 0.5rem 0.75rem;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .spinner {
            width: 24px;
            height: 24px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--accent-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .back-button {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        @media (max-width: 768px) {
            .header {
                padding: 1.5rem 0;
            }
            
            .metric-value {
                font-size: 2rem;
            }
            
            .chart-container {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <a href="/chatwoot" class="back-button me-3">
                        <i class="fas fa-arrow-left me-2"></i>Voltar ao Dashboard
                    </a>
                    <h1><i class="fas fa-star me-3"></i>Relatório de Satisfação - WhatsApp</h1>
                    <p class="mb-0 mt-2 opacity-75">Análise detalhada das avaliações dos clientes</p>
                </div>
                <div class="col-md-4 text-end">
                    <div id="connectionStatus">
                        <div class="loading">
                            <div class="spinner"></div>
                            Verificando conexão...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="container">
        <div class="filters-section">
            <h5><i class="fas fa-filter me-2"></i>Filtros de Consulta</h5>
            <form id="filterForm" class="row g-3">
                <div class="col-md-4">
                    <label for="startDate" class="form-label">Data Inicial</label>
                    <input type="date" class="form-control" id="startDate" required>
                </div>
                <div class="col-md-4">
                    <label for="endDate" class="form-label">Data Final</label>
                    <input type="date" class="form-control" id="endDate" required>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-2"></i>Gerar Relatório
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="setQuickFilter(30)">
                        Últimos 30 dias
                    </button>
                </div>
            </form>
        </div>

        <!-- Main Metrics -->
        <div class="row mb-4" id="metricsSection" style="display: none;">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="metric-value text-primary" id="totalRatings">--</div>
                    <div class="metric-label">Total de Avaliações</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="metric-value text-warning" id="averageRating">--</div>
                    <div class="metric-label">Avaliação Média</div>
                    <div class="rating-stars mt-2" id="ratingStars"></div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="metric-value text-success" id="satisfactionRate">--</div>
                    <div class="metric-label">Taxa de Satisfação</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="metric-value text-info" id="npsScore">--</div>
                    <div class="metric-label">NPS Score</div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row" id="chartsSection" style="display: none;">
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Distribuição de Notas</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="ratingsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Satisfação vs Insatisfação</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="satisfactionPieChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Agents Performance Table -->
        <div class="row" id="tableSection" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-users me-2"></i>Performance por Agente</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Agente</th>
                                        <th>Total Avaliações</th>
                                        <th>Nota Média</th>
                                        <th>Positivas (4-5)</th>
                                        <th>Negativas (1-2)</th>
                                        <th>Taxa de Satisfação</th>
                                        <th>NPS Individual</th>
                                    </tr>
                                </thead>
                                <tbody id="agentsTableBody">
                                    <tr>
                                        <td colspan="7" class="text-center">
                                            <div class="loading">
                                                <div class="spinner"></div>
                                                Carregando dados...
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Options -->
        <div class="row mt-4" id="exportSection" style="display: none;">
            <div class="col-12 text-center">
                <button class="btn btn-outline-primary me-2" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf me-2"></i>Exportar PDF
                </button>
                <button class="btn btn-outline-success" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-2"></i>Exportar Excel
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let ratingsChart, satisfactionPieChart;

        document.addEventListener('DOMContentLoaded', function() {
            setupDateFilters();
            checkConnection();
            setupEventListeners();
        });

        function setupDateFilters() {
            const today = new Date();
            const monthAgo = new Date(today);
            monthAgo.setDate(today.getDate() - 30);

            document.getElementById('startDate').value = monthAgo.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
        }

        function setupEventListeners() {
            document.getElementById('filterForm').addEventListener('submit', function(e) {
                e.preventDefault();
                loadSatisfactionData();
            });
        }

        function setQuickFilter(days) {
            const today = new Date();
            const pastDate = new Date(today);
            pastDate.setDate(today.getDate() - days);

            document.getElementById('startDate').value = pastDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
            loadSatisfactionData();
        }

        async function checkConnection() {
            try {
                const response = await fetch('/api/v1/chatwoot/health');
                const data = await response.json();

                const statusElement = document.getElementById('connectionStatus');

                if (data.status === 'healthy') {
                    statusElement.innerHTML = `
                        <span class="badge bg-success">
                            <i class="fas fa-check-circle me-2"></i>Conectado
                        </span>
                    `;
                    // Carregar dados iniciais
                    loadSatisfactionData();
                } else {
                    statusElement.innerHTML = `
                        <span class="badge bg-danger">
                            <i class="fas fa-times-circle me-2"></i>Erro na conexão
                        </span>
                    `;
                }
            } catch (error) {
                console.error('Erro ao verificar conexão:', error);
                document.getElementById('connectionStatus').innerHTML = `
                    <span class="badge bg-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Falha na conexão
                    </span>
                `;
            }
        }

        async function loadSatisfactionData() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                alert('Por favor, selecione as datas inicial e final.');
                return;
            }

            showLoading();

            try {
                const response = await fetch(`/api/v1/chatwoot/satisfaction?start_date=${startDate}&end_date=${endDate}`);
                const data = await response.json();

                if (data.success) {
                    updateMetrics(data.data);
                    updateCharts(data.data);
                    updateAgentsTable(data.data.agents || []);
                    showSections();
                } else {
                    alert('Erro ao carregar dados: ' + (data.message || 'Erro desconhecido'));
                }

                hideLoading();

            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                alert('Erro ao carregar dados. Tente novamente.');
                hideLoading();
            }
        }

        function updateMetrics(data) {
            const summary = data.summary || {};

            document.getElementById('totalRatings').textContent = summary.total_ratings || '0';
            document.getElementById('averageRating').textContent = summary.average_rating ?
                parseFloat(summary.average_rating).toFixed(1) : '0.0';
            document.getElementById('satisfactionRate').textContent = summary.satisfaction_rate ?
                `${summary.satisfaction_rate}%` : '0%';
            document.getElementById('npsScore').textContent = summary.nps_score || '0';

            // Atualizar estrelas
            updateRatingStars(summary.average_rating || 0);
        }

        function updateRatingStars(rating) {
            const starsContainer = document.getElementById('ratingStars');
            const fullStars = Math.floor(rating);
            const hasHalfStar = rating % 1 >= 0.5;

            let starsHtml = '';

            for (let i = 0; i < 5; i++) {
                if (i < fullStars) {
                    starsHtml += '<i class="fas fa-star"></i>';
                } else if (i === fullStars && hasHalfStar) {
                    starsHtml += '<i class="fas fa-star-half-alt"></i>';
                } else {
                    starsHtml += '<i class="far fa-star"></i>';
                }
            }

            starsContainer.innerHTML = starsHtml;
        }

        function updateCharts(data) {
            updateRatingsChart(data.ratings_distribution || []);
            updateSatisfactionPieChart(data.summary || {});
        }

        function updateRatingsChart(ratingsData) {
            const ctx = document.getElementById('ratingsChart').getContext('2d');

            if (ratingsChart) {
                ratingsChart.destroy();
            }

            const labels = ['1 Estrela', '2 Estrelas', '3 Estrelas', '4 Estrelas', '5 Estrelas'];
            const data = [
                ratingsData.find(r => r.rating === 1)?.count || 0,
                ratingsData.find(r => r.rating === 2)?.count || 0,
                ratingsData.find(r => r.rating === 3)?.count || 0,
                ratingsData.find(r => r.rating === 4)?.count || 0,
                ratingsData.find(r => r.rating === 5)?.count || 0
            ];

            ratingsChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Quantidade de Avaliações',
                        data: data,
                        backgroundColor: [
                            '#f15c6d',
                            '#fd7e14',
                            '#ffc107',
                            '#20c997',
                            '#25d366'
                        ],
                        borderRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        }

        function updateSatisfactionPieChart(summary) {
            const ctx = document.getElementById('satisfactionPieChart').getContext('2d');

            if (satisfactionPieChart) {
                satisfactionPieChart.destroy();
            }

            const positive = summary.positive_ratings || 0;
            const negative = summary.negative_ratings || 0;
            const neutral = (summary.total_ratings || 0) - positive - negative;

            satisfactionPieChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Satisfeitos (4-5)', 'Neutros (3)', 'Insatisfeitos (1-2)'],
                    datasets: [{
                        data: [positive, neutral, negative],
                        backgroundColor: [
                            '#25d366',
                            '#ffc107',
                            '#f15c6d'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function updateAgentsTable(agents) {
            const tbody = document.getElementById('agentsTableBody');

            if (agents.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">Nenhum dado disponível para o período selecionado</td></tr>';
                return;
            }

            tbody.innerHTML = agents.map(agent => {
                const satisfactionRate = agent.satisfaction_percentage || 0;
                const npsScore = calculateNPS(agent.positive_ratings || 0, agent.negative_ratings || 0, agent.total_ratings || 0);

                return `
                    <tr>
                        <td><strong>${agent.agent_name}</strong></td>
                        <td>${agent.total_ratings}</td>
                        <td>
                            <span class="me-2">${parseFloat(agent.average_rating).toFixed(1)}</span>
                            ${generateStars(agent.average_rating)}
                        </td>
                        <td><span class="badge bg-success">${agent.positive_ratings}</span></td>
                        <td><span class="badge bg-danger">${agent.negative_ratings}</span></td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar ${getProgressBarColor(satisfactionRate)}"
                                     style="width: ${satisfactionRate}%">
                                    ${satisfactionRate}%
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge ${getNPSBadgeColor(npsScore)}">${npsScore}</span>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function generateStars(rating) {
            const fullStars = Math.floor(rating);
            const hasHalfStar = rating % 1 >= 0.5;
            let starsHtml = '';

            for (let i = 0; i < 5; i++) {
                if (i < fullStars) {
                    starsHtml += '<i class="fas fa-star text-warning"></i>';
                } else if (i === fullStars && hasHalfStar) {
                    starsHtml += '<i class="fas fa-star-half-alt text-warning"></i>';
                } else {
                    starsHtml += '<i class="far fa-star text-muted"></i>';
                }
            }

            return starsHtml;
        }

        function calculateNPS(promoters, detractors, total) {
            if (total === 0) return 0;
            return Math.round(((promoters - detractors) / total) * 100);
        }

        function getProgressBarColor(percentage) {
            if (percentage >= 80) return 'bg-success';
            if (percentage >= 60) return 'bg-warning';
            return 'bg-danger';
        }

        function getNPSBadgeColor(nps) {
            if (nps >= 50) return 'bg-success';
            if (nps >= 0) return 'bg-warning';
            return 'bg-danger';
        }

        function showSections() {
            document.getElementById('metricsSection').style.display = 'block';
            document.getElementById('chartsSection').style.display = 'block';
            document.getElementById('tableSection').style.display = 'block';
            document.getElementById('exportSection').style.display = 'block';
        }

        function showLoading() {
            const loadingElements = document.querySelectorAll('.loading');
            loadingElements.forEach(el => el.style.display = 'flex');
        }

        function hideLoading() {
            const loadingElements = document.querySelectorAll('.loading');
            loadingElements.forEach(el => el.style.display = 'none');
        }

        function exportToPDF() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/generate-pdf/chatwoot-satisfaction';

            const startInput = document.createElement('input');
            startInput.type = 'hidden';
            startInput.name = 'start_date';
            startInput.value = startDate;

            const endInput = document.createElement('input');
            endInput.type = 'hidden';
            endInput.name = 'end_date';
            endInput.value = endDate;

            form.appendChild(startInput);
            form.appendChild(endInput);
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function exportToExcel() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            window.open(`/api/v1/chatwoot/satisfaction/export?start_date=${startDate}&end_date=${endDate}&format=excel`, '_blank');
        }
    </script>
</body>
</html>
