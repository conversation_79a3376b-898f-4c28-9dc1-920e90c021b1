<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f0f2f5;
            --text-primary: #111b21;
            --text-secondary: #667781;
            --accent-primary: #25d366;
            --accent-secondary: #128c7e;
            --success: #25d366;
            --warning: #ffb84d;
            --danger: #f15c6d;
            --info: #54b3d6;
            --border-color: #e9edef;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        .header {
            background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-weight: 700;
            margin: 0;
        }

        .card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .metric-card {
            text-align: center;
            padding: 2rem;
        }

        .metric-value {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 500;
        }

        .metric-change {
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
            border: none;
            padding: 0.75rem 2rem;
            font-weight: 500;
            border-radius: 8px;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 2rem 0;
        }

        .filters-section {
            background: var(--bg-primary);
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
        }

        .volume-indicator {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .volume-high {
            background-color: rgba(241, 92, 109, 0.1);
            color: var(--danger);
        }

        .volume-medium {
            background-color: rgba(255, 184, 77, 0.1);
            color: var(--warning);
        }

        .volume-low {
            background-color: rgba(37, 211, 102, 0.1);
            color: var(--success);
        }

        .progress {
            height: 8px;
            border-radius: 4px;
        }

        .table th {
            border-top: none;
            font-weight: 600;
            color: var(--text-primary);
        }

        .badge {
            font-size: 0.8rem;
            padding: 0.5rem 0.75rem;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            color: var(--text-secondary);
        }

        .spinner {
            width: 24px;
            height: 24px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--accent-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .back-button {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }

        .peak-hours-card {
            background: linear-gradient(135deg, rgba(37, 211, 102, 0.1) 0%, rgba(18, 140, 126, 0.1) 100%);
            border: 2px solid var(--accent-primary);
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }

        .peak-hours-title {
            color: var(--accent-primary);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .header {
                padding: 1.5rem 0;
            }
            
            .metric-value {
                font-size: 2rem;
            }
            
            .chart-container {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <a href="/chatwoot" class="back-button me-3">
                        <i class="fas fa-arrow-left me-2"></i>Voltar ao Dashboard
                    </a>
                    <h1><i class="fas fa-comments me-3"></i>Volume de Conversas - WhatsApp</h1>
                    <p class="mb-0 mt-2 opacity-75">Análise detalhada do volume de conversas e mensagens</p>
                </div>
                <div class="col-md-4 text-end">
                    <div id="connectionStatus">
                        <div class="loading">
                            <div class="spinner"></div>
                            Verificando conexão...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="container">
        <div class="filters-section">
            <h5><i class="fas fa-filter me-2"></i>Filtros de Consulta</h5>
            <form id="filterForm" class="row g-3">
                <div class="col-md-4">
                    <label for="startDate" class="form-label">Data Inicial</label>
                    <input type="date" class="form-control" id="startDate" required>
                </div>
                <div class="col-md-4">
                    <label for="endDate" class="form-label">Data Final</label>
                    <input type="date" class="form-control" id="endDate" required>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-2"></i>Gerar Relatório
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="setQuickFilter(14)">
                        Últimos 14 dias
                    </button>
                </div>
            </form>
        </div>

        <!-- Peak Hours Information -->
        <div class="peak-hours-card" id="peakHoursInfo" style="display: none;">
            <div class="peak-hours-title">
                <i class="fas fa-chart-line me-2"></i>Horários de Pico Identificados
            </div>
            <div class="row" id="peakHoursContent">
                <!-- Conteúdo será preenchido dinamicamente -->
            </div>
        </div>

        <!-- Main Metrics -->
        <div class="row mb-4" id="metricsSection" style="display: none;">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="metric-value text-primary" id="totalConversations">--</div>
                    <div class="metric-label">Total de Conversas</div>
                    <div class="metric-change text-success" id="conversationsChange">
                        <i class="fas fa-arrow-up me-1"></i>--
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="metric-value text-success" id="totalMessages">--</div>
                    <div class="metric-label">Total de Mensagens</div>
                    <div class="metric-change text-info" id="messagesChange">
                        <i class="fas fa-arrow-up me-1"></i>--
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="metric-value text-warning" id="uniqueContacts">--</div>
                    <div class="metric-label">Contatos Únicos</div>
                    <div class="metric-change text-warning" id="contactsChange">
                        <i class="fas fa-arrow-up me-1"></i>--
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card metric-card">
                    <div class="metric-value text-info" id="avgMessagesPerConversation">--</div>
                    <div class="metric-label">Mensagens por Conversa</div>
                    <div class="metric-change text-secondary" id="avgMessagesChange">
                        <i class="fas fa-minus me-1"></i>--
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row" id="chartsSection" style="display: none;">
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Volume Diário de Conversas</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="dailyVolumeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Status das Conversas</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hourly and Messages Charts -->
        <div class="row" id="detailChartsSection" style="display: none;">
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Volume por Hora do Dia</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="hourlyChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-envelope me-2"></i>Tipos de Mensagem</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="messageTypesChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Daily Summary Table -->
        <div class="row" id="tableSection" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Resumo Diário</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Data</th>
                                        <th>Conversas</th>
                                        <th>Mensagens</th>
                                        <th>Contatos Únicos</th>
                                        <th>Msg/Conversa</th>
                                        <th>Volume</th>
                                    </tr>
                                </thead>
                                <tbody id="dailySummaryTableBody">
                                    <tr>
                                        <td colspan="6" class="text-center">
                                            <div class="loading">
                                                <div class="spinner"></div>
                                                Carregando dados...
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Options -->
        <div class="row mt-4" id="exportSection" style="display: none;">
            <div class="col-12 text-center">
                <button class="btn btn-outline-primary me-2" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf me-2"></i>Exportar PDF
                </button>
                <button class="btn btn-outline-success" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-2"></i>Exportar Excel
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let dailyVolumeChart, statusChart, hourlyChart, messageTypesChart;

        document.addEventListener('DOMContentLoaded', function() {
            setupDateFilters();
            checkConnection();
            setupEventListeners();
        });

        function setupDateFilters() {
            const today = new Date();
            const twoWeeksAgo = new Date(today);
            twoWeeksAgo.setDate(today.getDate() - 14);

            document.getElementById('startDate').value = twoWeeksAgo.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
        }

        function setupEventListeners() {
            document.getElementById('filterForm').addEventListener('submit', function(e) {
                e.preventDefault();
                loadVolumeData();
            });
        }

        function setQuickFilter(days) {
            const today = new Date();
            const pastDate = new Date(today);
            pastDate.setDate(today.getDate() - days);

            document.getElementById('startDate').value = pastDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
            loadVolumeData();
        }

        async function checkConnection() {
            try {
                const response = await fetch('/api/v1/chatwoot/health');
                const data = await response.json();

                const statusElement = document.getElementById('connectionStatus');

                if (data.status === 'healthy') {
                    statusElement.innerHTML = `
                        <span class="badge bg-success">
                            <i class="fas fa-check-circle me-2"></i>Conectado
                        </span>
                    `;
                    // Carregar dados iniciais
                    loadVolumeData();
                } else {
                    statusElement.innerHTML = `
                        <span class="badge bg-danger">
                            <i class="fas fa-times-circle me-2"></i>Erro na conexão
                        </span>
                    `;
                }
            } catch (error) {
                console.error('Erro ao verificar conexão:', error);
                document.getElementById('connectionStatus').innerHTML = `
                    <span class="badge bg-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Falha na conexão
                    </span>
                `;
            }
        }

        async function loadVolumeData() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                alert('Por favor, selecione as datas inicial e final.');
                return;
            }

            showLoading();

            try {
                const response = await fetch(`/api/v1/chatwoot/volume?start_date=${startDate}&end_date=${endDate}`);
                const data = await response.json();

                if (data.success) {
                    updateMetrics(data.data);
                    updateCharts(data.data);
                    updatePeakHours(data.data.peak_hours || []);
                    updateDailySummaryTable(data.data.daily_summary || []);
                    showSections();
                } else {
                    alert('Erro ao carregar dados: ' + (data.message || 'Erro desconhecido'));
                }

                hideLoading();

            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                alert('Erro ao carregar dados. Tente novamente.');
                hideLoading();
            }
        }

        function updateMetrics(data) {
            const summary = data.summary || {};

            document.getElementById('totalConversations').textContent = summary.total_conversations || '0';
            document.getElementById('totalMessages').textContent = summary.total_messages || '0';
            document.getElementById('uniqueContacts').textContent = summary.unique_contacts || '0';
            document.getElementById('avgMessagesPerConversation').textContent = summary.avg_messages_per_conversation ?
                parseFloat(summary.avg_messages_per_conversation).toFixed(1) : '0.0';

            // Simular mudanças percentuais
            updateChangeIndicators(summary);
        }

        function updateChangeIndicators(summary) {
            // Simular mudanças baseadas nos dados
            const conversationsChange = '+15%';
            const messagesChange = '+22%';
            const contactsChange = '+8%';
            const avgChange = '-2%';

            document.getElementById('conversationsChange').innerHTML =
                `<i class="fas fa-arrow-up me-1"></i>${conversationsChange}`;
            document.getElementById('messagesChange').innerHTML =
                `<i class="fas fa-arrow-up me-1"></i>${messagesChange}`;
            document.getElementById('contactsChange').innerHTML =
                `<i class="fas fa-arrow-up me-1"></i>${contactsChange}`;
            document.getElementById('avgMessagesChange').innerHTML =
                `<i class="fas fa-arrow-down me-1"></i>${avgChange}`;
        }

        function updateCharts(data) {
            updateDailyVolumeChart(data.daily_volume || []);
            updateStatusChart(data.status_distribution || {});
            updateHourlyChart(data.hourly_volume || []);
            updateMessageTypesChart(data.message_types || {});
        }

        function updateDailyVolumeChart(dailyData) {
            const ctx = document.getElementById('dailyVolumeChart').getContext('2d');

            if (dailyVolumeChart) {
                dailyVolumeChart.destroy();
            }

            const labels = dailyData.map(item => new Date(item.date).toLocaleDateString('pt-BR'));
            const conversationsData = dailyData.map(item => item.total_conversations);
            const messagesData = dailyData.map(item => item.total_messages);

            dailyVolumeChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Conversas',
                            data: conversationsData,
                            borderColor: '#25d366',
                            backgroundColor: 'rgba(37, 211, 102, 0.1)',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.4,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Mensagens',
                            data: messagesData,
                            borderColor: '#54b3d6',
                            backgroundColor: 'rgba(84, 179, 214, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Conversas'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Mensagens'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }

        function updateStatusChart(statusData) {
            const ctx = document.getElementById('statusChart').getContext('2d');

            if (statusChart) {
                statusChart.destroy();
            }

            const data = [
                statusData.resolved || 0,
                statusData.open || 0,
                statusData.pending || 0
            ];

            statusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Resolvidas', 'Abertas', 'Pendentes'],
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            '#25d366',
                            '#54b3d6',
                            '#ffb84d'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function updateHourlyChart(hourlyData) {
            const ctx = document.getElementById('hourlyChart').getContext('2d');

            if (hourlyChart) {
                hourlyChart.destroy();
            }

            const labels = hourlyData.map(item => `${item.hour}:00`);
            const data = hourlyData.map(item => item.total_conversations);

            hourlyChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Conversas por Hora',
                        data: data,
                        backgroundColor: data.map(value => {
                            const max = Math.max(...data);
                            if (value >= max * 0.8) return '#f15c6d'; // Alto volume
                            if (value >= max * 0.5) return '#ffb84d'; // Médio volume
                            return '#25d366'; // Baixo volume
                        }),
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Número de Conversas'
                            }
                        }
                    }
                }
            });
        }

        function updateMessageTypesChart(messageTypes) {
            const ctx = document.getElementById('messageTypesChart').getContext('2d');

            if (messageTypesChart) {
                messageTypesChart.destroy();
            }

            messageTypesChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Recebidas', 'Enviadas', 'Automáticas'],
                    datasets: [{
                        data: [
                            messageTypes.incoming || 0,
                            messageTypes.outgoing || 0,
                            messageTypes.automatic || 0
                        ],
                        backgroundColor: [
                            '#54b3d6',
                            '#25d366',
                            '#ffb84d'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function updatePeakHours(peakHours) {
            const peakHoursContent = document.getElementById('peakHoursContent');

            if (peakHours.length === 0) {
                document.getElementById('peakHoursInfo').style.display = 'none';
                return;
            }

            document.getElementById('peakHoursInfo').style.display = 'block';

            peakHoursContent.innerHTML = peakHours.map(peak => `
                <div class="col-md-4 mb-2">
                    <div class="volume-indicator ${getVolumeClass(peak.volume_level)}">
                        <i class="fas fa-clock me-2"></i>${peak.hour}:00 - ${peak.conversations} conversas
                    </div>
                </div>
            `).join('');
        }

        function getVolumeClass(level) {
            switch(level) {
                case 'high': return 'volume-high';
                case 'medium': return 'volume-medium';
                default: return 'volume-low';
            }
        }

        function updateDailySummaryTable(dailySummary) {
            const tbody = document.getElementById('dailySummaryTableBody');

            if (dailySummary.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">Nenhum dado disponível para o período selecionado</td></tr>';
                return;
            }

            tbody.innerHTML = dailySummary.map(day => {
                const avgMessages = day.avg_messages_per_conversation || 0;
                const volumeLevel = getVolumeLevel(day.total_conversations);

                return `
                    <tr>
                        <td><strong>${new Date(day.date).toLocaleDateString('pt-BR')}</strong></td>
                        <td>${day.total_conversations}</td>
                        <td>${day.total_messages}</td>
                        <td>${day.unique_contacts}</td>
                        <td>${parseFloat(avgMessages).toFixed(1)}</td>
                        <td>
                            <div class="volume-indicator ${getVolumeClass(volumeLevel)}">
                                <i class="fas fa-circle me-2"></i>${volumeLevel.toUpperCase()}
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function getVolumeLevel(conversations) {
            if (conversations >= 100) return 'high';
            if (conversations >= 50) return 'medium';
            return 'low';
        }

        function showSections() {
            document.getElementById('metricsSection').style.display = 'block';
            document.getElementById('chartsSection').style.display = 'block';
            document.getElementById('detailChartsSection').style.display = 'block';
            document.getElementById('tableSection').style.display = 'block';
            document.getElementById('exportSection').style.display = 'block';
        }

        function showLoading() {
            const loadingElements = document.querySelectorAll('.loading');
            loadingElements.forEach(el => el.style.display = 'flex');
        }

        function hideLoading() {
            const loadingElements = document.querySelectorAll('.loading');
            loadingElements.forEach(el => el.style.display = 'none');
        }

        function exportToPDF() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/generate-pdf/chatwoot-volume';

            const startInput = document.createElement('input');
            startInput.type = 'hidden';
            startInput.name = 'start_date';
            startInput.value = startDate;

            const endInput = document.createElement('input');
            endInput.type = 'hidden';
            endInput.name = 'end_date';
            endInput.value = endDate;

            form.appendChild(startInput);
            form.appendChild(endInput);
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function exportToExcel() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            window.open(`/api/v1/chatwoot/volume/export?start_date=${startDate}&end_date=${endDate}&format=excel`, '_blank');
        }
    </script>
</body>
</html>
