{% extends "base.html" %}

{% block title %}Pesquisa de Satisfação - Sistema de Relatórios 3CX{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-5 text-primary">
                <i class="fas fa-smile me-3"></i>
                Pesquisa de Satisfação do Cliente
            </h1>
            <p class="lead text-muted">Análise detalhada da satisfação e feedback dos clientes</p>
        </div>
    </div>

    <!-- Filtros e Ações -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-2"></i>
            Filtros e Ações
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label for="start_date" class="form-label">Data Inicial</label>
                    <input type="date" class="form-control" id="start_date">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">Data Final</label>
                    <input type="date" class="form-control" id="end_date">
                </div>
                <div class="col-md-3">
                    <label for="queues" class="form-label">Filas</label>
                    <input type="text" class="form-control" id="queues" placeholder="802,803" value="{{ available_queues|join(',') }}">
                </div>
                <div class="col-md-3 d-flex align-items-end gap-2">
                    <button class="btn btn-primary" onclick="loadSatisfactionReport()">
                        <i class="fas fa-sync-alt me-2"></i>
                        Gerar
                    </button>
                    <button class="btn btn-success" onclick="generatePDF('satisfaction')">
                        <i class="fas fa-file-pdf me-2"></i>
                        PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Métricas Principais -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-primary" id="nps-score">-</div>
                    <div class="metric-label">NPS Score</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-success" id="csat-score">-</div>
                    <div class="metric-label">CSAT (%)</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-info" id="total-responses">-</div>
                    <div class="metric-label">Total Respostas</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-warning" id="response-rate">-</div>
                    <div class="metric-label">Taxa Resposta (%)</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos de Satisfação -->
    <div class="row mb-4">
        <!-- Distribuição NPS -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-2"></i>
                    Distribuição NPS
                </div>
                <div class="card-body">
                    <div id="nps-distribution-chart" class="chart-container">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Selecione um período para gerar o gráfico
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Distribuição de Notas -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-star me-2"></i>
                    Distribuição de Notas (1-10)
                </div>
                <div class="card-body">
                    <div id="rating-distribution-chart" class="chart-container">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Selecione um período para gerar o gráfico
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Satisfação por Fila -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-table me-2"></i>
                    Satisfação por Fila
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="satisfaction-by-queue-table">
                            <thead>
                                <tr>
                                    <th>Fila</th>
                                    <th>Nome</th>
                                    <th>NPS</th>
                                    <th>CSAT (%)</th>
                                    <th>Respostas</th>
                                    <th>Promotores</th>
                                    <th>Neutros</th>
                                    <th>Detratores</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="8" class="text-center text-muted">
                                        Selecione um período e clique em "Gerar" para visualizar os dados
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Comentários dos Clientes -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-comments me-2"></i>
                    Comentários dos Clientes
                </div>
                <div class="card-body">
                    <div id="customer-comments">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Gere um relatório para visualizar os comentários dos clientes.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Análise de Tendências -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-chart-line me-2"></i>
                    Análise de Tendências e Insights
                </div>
                <div class="card-body">
                    <div id="satisfaction-insights">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Gere um relatório para visualizar insights automáticos baseados nos dados de satisfação.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Carrega relatório de satisfação
    async function loadSatisfactionReport() {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;
        const queues = document.getElementById('queues').value;
        
        if (!startDate || !endDate) {
            alert('Por favor, selecione as datas de início e fim.');
            return;
        }
        
        // Carrega métricas principais
        await loadSatisfactionMetrics();
        
        // Carrega gráficos
        await loadNPSDistributionChart();
        await loadRatingDistributionChart();
        
        // Carrega tabela por fila
        await loadSatisfactionByQueueTable();
        
        // Carrega comentários
        await loadCustomerComments();
        
        // Gera insights
        generateSatisfactionInsights();
    }
    
    // Carrega métricas de satisfação
    async function loadSatisfactionMetrics() {
        try {
            // Simula dados de satisfação
            const npsScore = (Math.random() * 60 + 20).toFixed(1);
            const csatScore = (Math.random() * 20 + 75).toFixed(1);
            const totalResponses = Math.floor(Math.random() * 200) + 50;
            const responseRate = (Math.random() * 30 + 15).toFixed(1);
            
            document.getElementById('nps-score').textContent = npsScore;
            document.getElementById('csat-score').textContent = csatScore + '%';
            document.getElementById('total-responses').textContent = totalResponses.toLocaleString();
            document.getElementById('response-rate').textContent = responseRate + '%';
            
        } catch (error) {
            console.error('Erro ao carregar métricas de satisfação:', error);
        }
    }
    
    // Carrega gráfico de distribuição NPS
    async function loadNPSDistributionChart() {
        // Simula dados NPS
        const promoters = Math.floor(Math.random() * 50) + 30;
        const passives = Math.floor(Math.random() * 30) + 15;
        const detractors = Math.floor(Math.random() * 20) + 5;
        
        const chartHtml = `
            <canvas id="npsChart" width="400" height="200"></canvas>
            <script>
                const npsCtx = document.getElementById('npsChart').getContext('2d');
                new Chart(npsCtx, {
                    type: 'bar',
                    data: {
                        labels: ['Detratores (0-6)', 'Neutros (7-8)', 'Promotores (9-10)'],
                        datasets: [{
                            label: 'Número de Respostas',
                            data: [${detractors}, ${passives}, ${promoters}],
                            backgroundColor: [
                                'rgba(231, 76, 60, 0.8)',
                                'rgba(243, 156, 18, 0.8)',
                                'rgba(46, 204, 113, 0.8)'
                            ],
                            borderColor: [
                                'rgba(231, 76, 60, 1)',
                                'rgba(243, 156, 18, 1)',
                                'rgba(46, 204, 113, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            </script>
        `;
        
        document.getElementById('nps-distribution-chart').innerHTML = chartHtml;
    }
    
    // Carrega gráfico de distribuição de notas
    async function loadRatingDistributionChart() {
        // Simula distribuição de notas 1-10
        const ratings = Array.from({length: 10}, (_, i) => Math.floor(Math.random() * 15) + 2);
        
        const chartHtml = `
            <canvas id="ratingChart" width="400" height="200"></canvas>
            <script>
                const ratingCtx = document.getElementById('ratingChart').getContext('2d');
                new Chart(ratingCtx, {
                    type: 'line',
                    data: {
                        labels: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'],
                        datasets: [{
                            label: 'Número de Avaliações',
                            data: ${JSON.stringify(ratings)},
                            borderColor: 'rgba(52, 152, 219, 1)',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            </script>
        `;
        
        document.getElementById('rating-distribution-chart').innerHTML = chartHtml;
    }
    
    // Carrega tabela de satisfação por fila
    async function loadSatisfactionByQueueTable() {
        const queues = document.getElementById('queues').value.split(',');
        
        let tableHtml = '';
        
        queues.forEach(queue => {
            const queueId = queue.trim();
            const nps = (Math.random() * 60 + 20).toFixed(1);
            const csat = (Math.random() * 20 + 75).toFixed(1);
            const responses = Math.floor(Math.random() * 50) + 10;
            const promoters = Math.floor(responses * 0.6);
            const passives = Math.floor(responses * 0.25);
            const detractors = responses - promoters - passives;
            
            tableHtml += `
                <tr>
                    <td><span class="badge bg-primary">${queueId}</span></td>
                    <td>Fila ${queueId}</td>
                    <td><span class="badge bg-${nps > 50 ? 'success' : nps > 0 ? 'warning' : 'danger'}">${nps}</span></td>
                    <td>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar bg-success" style="width: ${csat}%">
                                ${csat}%
                            </div>
                        </div>
                    </td>
                    <td>${responses}</td>
                    <td><span class="text-success">${promoters}</span></td>
                    <td><span class="text-warning">${passives}</span></td>
                    <td><span class="text-danger">${detractors}</span></td>
                </tr>
            `;
        });
        
        document.querySelector('#satisfaction-by-queue-table tbody').innerHTML = tableHtml;
    }
    
    // Carrega comentários dos clientes
    async function loadCustomerComments() {
        const comments = [
            { rating: 10, comment: "Atendimento excelente! Muito satisfeito com a resolução do problema.", sentiment: "positive" },
            { rating: 8, comment: "Bom atendimento, mas demorou um pouco para ser atendido.", sentiment: "neutral" },
            { rating: 9, comment: "Agente muito educado e prestativo. Resolveu rapidamente.", sentiment: "positive" },
            { rating: 6, comment: "Atendimento ok, mas poderia ser mais rápido.", sentiment: "neutral" },
            { rating: 4, comment: "Demorou muito para resolver meu problema.", sentiment: "negative" }
        ];
        
        let commentsHtml = '';
        
        comments.forEach((comment, index) => {
            const badgeClass = comment.sentiment === 'positive' ? 'success' : 
                             comment.sentiment === 'negative' ? 'danger' : 'warning';
            
            commentsHtml += `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="card-title">
                                    Cliente ${index + 1}
                                    <span class="badge bg-${badgeClass} ms-2">Nota: ${comment.rating}/10</span>
                                </h6>
                                <p class="card-text">"${comment.comment}"</p>
                            </div>
                            <small class="text-muted">Hoje</small>
                        </div>
                    </div>
                </div>
            `;
        });
        
        document.getElementById('customer-comments').innerHTML = commentsHtml;
    }
    
    // Gera insights de satisfação
    function generateSatisfactionInsights() {
        const insights = [
            {
                type: 'success',
                icon: 'fas fa-thumbs-up',
                title: 'NPS Positivo',
                text: 'Score NPS acima de 50 indica alta satisfação dos clientes.'
            },
            {
                type: 'info',
                icon: 'fas fa-chart-line',
                title: 'Tendência',
                text: 'Maioria das avaliações concentrada nas notas 8-10, indicando boa qualidade do atendimento.'
            },
            {
                type: 'warning',
                icon: 'fas fa-exclamation-triangle',
                title: 'Atenção',
                text: 'Taxa de resposta pode ser melhorada com campanhas de engajamento.'
            },
            {
                type: 'primary',
                icon: 'fas fa-lightbulb',
                title: 'Recomendação',
                text: 'Implementar follow-up automático para aumentar a taxa de resposta das pesquisas.'
            }
        ];
        
        let insightsHtml = '';
        insights.forEach(insight => {
            insightsHtml += `
                <div class="alert alert-${insight.type}">
                    <i class="${insight.icon} me-2"></i>
                    <strong>${insight.title}:</strong> ${insight.text}
                </div>
            `;
        });
        
        document.getElementById('satisfaction-insights').innerHTML = insightsHtml;
    }
    
    // Inicialização
    document.addEventListener('DOMContentLoaded', function() {
        // Define datas padrão (últimos 30 dias)
        const today = new Date();
        const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        
        document.getElementById('end_date').value = today.toISOString().split('T')[0];
        document.getElementById('start_date').value = lastMonth.toISOString().split('T')[0];
    });
</script>
{% endblock %}
