{% extends "base.html" %}

{% block title %}Relatório de Distribuição - Sistema de Relatórios 3CX{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-5 text-primary">
                <i class="fas fa-chart-pie me-3"></i>
                Relatório de Distribuição de Chamadas
            </h1>
            <p class="lead text-muted">Análise detalhada do volume e distribuição de chamadas por fila</p>
        </div>
    </div>

    <!-- Filtros e Ações -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-2"></i>
            Filtros e Ações
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label for="start_date" class="form-label">Data Inicial</label>
                    <input type="date" class="form-control" id="start_date">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">Data Final</label>
                    <input type="date" class="form-control" id="end_date">
                </div>
                <div class="col-md-3">
                    <label for="queues" class="form-label">Filas</label>
                    <input type="text" class="form-control" id="queues" placeholder="802,803" value="{{ available_queues|join(',') }}">
                </div>
                <div class="col-md-3 d-flex align-items-end gap-2">
                    <button class="btn btn-primary" onclick="loadDistributionReport()">
                        <i class="fas fa-sync-alt me-2"></i>
                        Gerar
                    </button>
                    <button class="btn btn-success" onclick="generatePDF('distribution')">
                        <i class="fas fa-file-pdf me-2"></i>
                        PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Resumo Executivo -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value" id="total-calls">-</div>
                    <div class="metric-label">Total de Chamadas</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-success" id="answered-calls">-</div>
                    <div class="metric-label">Chamadas Atendidas</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-danger" id="abandoned-calls">-</div>
                    <div class="metric-label">Chamadas Abandonadas</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-info" id="answer-rate">-</div>
                    <div class="metric-label">Taxa de Atendimento (%)</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos -->
    <div class="row mb-4">
        <!-- Gráfico de Pizza -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-2"></i>
                    Distribuição por Fila
                </div>
                <div class="card-body">
                    <div id="distribution-pie-chart" class="chart-container">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Selecione um período para gerar o gráfico
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gráfico de Barras -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-2"></i>
                    Volume por Fila
                </div>
                <div class="card-body">
                    <div id="distribution-bar-chart" class="chart-container">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Selecione um período para gerar o gráfico
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabela Detalhada -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-table me-2"></i>
                    Detalhamento por Fila
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="distribution-table">
                            <thead>
                                <tr>
                                    <th>Fila</th>
                                    <th>Nome</th>
                                    <th>Total Chamadas</th>
                                    <th>Atendidas</th>
                                    <th>Abandonadas</th>
                                    <th>Taxa Atendimento</th>
                                    <th>Percentual do Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        Selecione um período e clique em "Gerar" para visualizar os dados
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Análise por Horário -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-clock me-2"></i>
                    Distribuição por Horário
                </div>
                <div class="card-body">
                    <div id="hourly-distribution-chart" class="chart-container">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Selecione um período para gerar a análise horária
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Insights e Recomendações -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-lightbulb me-2"></i>
                    Insights e Recomendações
                </div>
                <div class="card-body">
                    <div id="insights-content">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Gere um relatório para visualizar insights automáticos baseados nos dados.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Carrega relatório de distribuição
    async function loadDistributionReport() {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;
        const queues = document.getElementById('queues').value;
        
        if (!startDate || !endDate) {
            alert('Por favor, selecione as datas de início e fim.');
            return;
        }
        
        // Carrega métricas resumo
        await loadDistributionMetrics();
        
        // Carrega gráficos
        await loadChart('distribution', 'distribution-pie-chart');
        await loadDistributionBarChart();
        await loadChart('hourly', 'hourly-distribution-chart');
        
        // Carrega tabela
        await loadDistributionTable();
        
        // Gera insights
        generateInsights();
    }
    
    // Carrega métricas de distribuição
    async function loadDistributionMetrics() {
        try {
            // Simula dados (em produção viria da API)
            const totalCalls = Math.floor(Math.random() * 1000) + 500;
            const answeredCalls = Math.floor(totalCalls * (0.9 + Math.random() * 0.08));
            const abandonedCalls = totalCalls - answeredCalls;
            const answerRate = ((answeredCalls / totalCalls) * 100).toFixed(1);
            
            document.getElementById('total-calls').textContent = totalCalls.toLocaleString();
            document.getElementById('answered-calls').textContent = answeredCalls.toLocaleString();
            document.getElementById('abandoned-calls').textContent = abandonedCalls.toLocaleString();
            document.getElementById('answer-rate').textContent = answerRate + '%';
            
        } catch (error) {
            console.error('Erro ao carregar métricas:', error);
        }
    }
    
    // Carrega gráfico de barras de distribuição
    async function loadDistributionBarChart() {
        const queues = document.getElementById('queues').value.split(',');
        
        // Simula dados para gráfico de barras
        const data = queues.map(queue => ({
            queue: `Fila ${queue.trim()}`,
            calls: Math.floor(Math.random() * 200) + 50,
            answered: Math.floor(Math.random() * 180) + 40,
            abandoned: Math.floor(Math.random() * 20) + 5
        }));
        
        const chartHtml = `
            <canvas id="barChart" width="400" height="200"></canvas>
            <script>
                const ctx = document.getElementById('barChart').getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ${JSON.stringify(data.map(d => d.queue))},
                        datasets: [{
                            label: 'Atendidas',
                            data: ${JSON.stringify(data.map(d => d.answered))},
                            backgroundColor: 'rgba(46, 204, 113, 0.8)',
                            borderColor: 'rgba(46, 204, 113, 1)',
                            borderWidth: 1
                        }, {
                            label: 'Abandonadas',
                            data: ${JSON.stringify(data.map(d => d.abandoned))},
                            backgroundColor: 'rgba(231, 76, 60, 0.8)',
                            borderColor: 'rgba(231, 76, 60, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            </script>
        `;
        
        document.getElementById('distribution-bar-chart').innerHTML = chartHtml;
    }
    
    // Carrega tabela de distribuição
    async function loadDistributionTable() {
        const queues = document.getElementById('queues').value.split(',');
        
        let tableHtml = '';
        let totalCalls = 0;
        
        const data = queues.map(queue => {
            const calls = Math.floor(Math.random() * 200) + 50;
            const answered = Math.floor(calls * (0.9 + Math.random() * 0.08));
            const abandoned = calls - answered;
            const answerRate = ((answered / calls) * 100).toFixed(1);
            
            totalCalls += calls;
            
            return {
                queue: queue.trim(),
                name: `Fila ${queue.trim()}`,
                calls,
                answered,
                abandoned,
                answerRate
            };
        });
        
        data.forEach(row => {
            const percentage = ((row.calls / totalCalls) * 100).toFixed(1);
            tableHtml += `
                <tr>
                    <td><span class="badge bg-primary">${row.queue}</span></td>
                    <td>${row.name}</td>
                    <td>${row.calls.toLocaleString()}</td>
                    <td><span class="text-success">${row.answered.toLocaleString()}</span></td>
                    <td><span class="text-danger">${row.abandoned.toLocaleString()}</span></td>
                    <td>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar bg-success" style="width: ${row.answerRate}%">
                                ${row.answerRate}%
                            </div>
                        </div>
                    </td>
                    <td>${percentage}%</td>
                </tr>
            `;
        });
        
        document.querySelector('#distribution-table tbody').innerHTML = tableHtml;
    }
    
    // Gera insights automáticos
    function generateInsights() {
        const insights = [
            {
                type: 'success',
                icon: 'fas fa-check-circle',
                title: 'Performance Positiva',
                text: 'Taxa de atendimento acima de 90% indica boa performance operacional.'
            },
            {
                type: 'warning',
                icon: 'fas fa-exclamation-triangle',
                title: 'Oportunidade de Melhoria',
                text: 'Considere redistribuir chamadas para equilibrar a carga entre filas.'
            },
            {
                type: 'info',
                icon: 'fas fa-info-circle',
                title: 'Recomendação',
                text: 'Monitore os horários de pico para otimizar a alocação de recursos.'
            }
        ];
        
        let insightsHtml = '';
        insights.forEach(insight => {
            insightsHtml += `
                <div class="alert alert-${insight.type}">
                    <i class="${insight.icon} me-2"></i>
                    <strong>${insight.title}:</strong> ${insight.text}
                </div>
            `;
        });
        
        document.getElementById('insights-content').innerHTML = insightsHtml;
    }
    
    // Inicialização
    document.addEventListener('DOMContentLoaded', function() {
        // Define datas padrão (últimos 7 dias)
        const today = new Date();
        const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        
        document.getElementById('end_date').value = today.toISOString().split('T')[0];
        document.getElementById('start_date').value = lastWeek.toISOString().split('T')[0];
    });
</script>
{% endblock %}
