{% extends "base.html" %}

{% block title %}Relatório Comparativo - Sistema de Relatórios 3CX{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-5 text-primary">
                <i class="fas fa-chart-line me-3"></i>
                Relatório Comparativo
            </h1>
            <p class="lead text-muted">Comparação de métricas entre diferentes períodos</p>
        </div>
    </div>

    <!-- Filtros e Ações -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-2"></i>
            Filtros e Ações
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-2">
                    <label for="period1_start" class="form-label">Período 1 - Início</label>
                    <input type="date" class="form-control" id="period1_start">
                </div>
                <div class="col-md-2">
                    <label for="period1_end" class="form-label">Período 1 - Fim</label>
                    <input type="date" class="form-control" id="period1_end">
                </div>
                <div class="col-md-2">
                    <label for="period2_start" class="form-label">Período 2 - Início</label>
                    <input type="date" class="form-control" id="period2_start">
                </div>
                <div class="col-md-2">
                    <label for="period2_end" class="form-label">Período 2 - Fim</label>
                    <input type="date" class="form-control" id="period2_end">
                </div>
                <div class="col-md-2">
                    <label for="queues" class="form-label">Filas</label>
                    <input type="text" class="form-control" id="queues" placeholder="802,803" value="{{ available_queues|join(',') }}">
                </div>
                <div class="col-md-2 d-flex align-items-end gap-2">
                    <button class="btn btn-primary" onclick="loadComparativeReport()">
                        <i class="fas fa-sync-alt me-2"></i>
                        Comparar
                    </button>
                    <button class="btn btn-success" onclick="generatePDF('comparative')">
                        <i class="fas fa-file-pdf me-2"></i>
                        PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Resumo Comparativo -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value" id="calls-comparison">-</div>
                    <div class="metric-label">Variação de Chamadas</div>
                    <div class="metric-trend" id="calls-trend"></div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-success" id="asa-comparison">-</div>
                    <div class="metric-label">Variação ASA</div>
                    <div class="metric-trend" id="asa-trend"></div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-info" id="aht-comparison">-</div>
                    <div class="metric-label">Variação AHT</div>
                    <div class="metric-trend" id="aht-trend"></div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-warning" id="sl-comparison">-</div>
                    <div class="metric-label">Variação Service Level</div>
                    <div class="metric-trend" id="sl-trend"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos Comparativos -->
    <div class="row mb-4">
        <!-- Gráfico de Volume -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-2"></i>
                    Comparação de Volume
                </div>
                <div class="card-body">
                    <div id="volume-comparison-chart" class="chart-container">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Carregando gráfico...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gráfico de Timing -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-clock me-2"></i>
                    Comparação de Timing
                </div>
                <div class="card-body">
                    <div id="timing-comparison-chart" class="chart-container">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Carregando gráfico...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabela Comparativa -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-table me-2"></i>
                    Comparação Detalhada
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="comparison-table">
                            <thead>
                                <tr>
                                    <th>Métrica</th>
                                    <th>Período 1</th>
                                    <th>Período 2</th>
                                    <th>Variação</th>
                                    <th>Tendência</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="5" class="text-center">
                                        <div class="loading">
                                            <i class="fas fa-spinner"></i><br>
                                            Carregando dados...
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Análise e Insights -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-lightbulb me-2"></i>
                    Análise Comparativa e Insights
                </div>
                <div class="card-body">
                    <div id="comparative-insights">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Gerando análise...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let volumeChart, timingChart;

    function loadComparativeReport() {
        const period1Start = document.getElementById('period1_start').value;
        const period1End = document.getElementById('period1_end').value;
        const period2Start = document.getElementById('period2_start').value;
        const period2End = document.getElementById('period2_end').value;
        const queues = document.getElementById('queues').value;

        if (!period1Start || !period1End || !period2Start || !period2End) {
            alert('Por favor, selecione todas as datas dos períodos.');
            return;
        }

        showLoading();

        // Simular carregamento de dados
        setTimeout(() => {
            updateComparativeMetrics();
            updateComparativeCharts();
            updateComparativeTable();
            updateComparativeInsights();
            hideLoading();
        }, 2000);
    }

    function updateComparativeMetrics() {
        document.getElementById('calls-comparison').textContent = '+15%';
        document.getElementById('calls-trend').innerHTML = '<i class="fas fa-arrow-up text-success"></i>';

        document.getElementById('asa-comparison').textContent = '-8%';
        document.getElementById('asa-trend').innerHTML = '<i class="fas fa-arrow-down text-success"></i>';

        document.getElementById('aht-comparison').textContent = '+3%';
        document.getElementById('aht-trend').innerHTML = '<i class="fas fa-arrow-up text-warning"></i>';

        document.getElementById('sl-comparison').textContent = '+12%';
        document.getElementById('sl-trend').innerHTML = '<i class="fas fa-arrow-up text-success"></i>';
    }

    function updateComparativeCharts() {
        // Implementar gráficos comparativos
        document.getElementById('volume-comparison-chart').innerHTML = '<p>Gráfico de comparação de volume</p>';
        document.getElementById('timing-comparison-chart').innerHTML = '<p>Gráfico de comparação de timing</p>';
    }

    function updateComparativeTable() {
        // Implementar tabela comparativa
        const tbody = document.querySelector('#comparison-table tbody');
        tbody.innerHTML = `
            <tr>
                <td>Total de Chamadas</td>
                <td>1,250</td>
                <td>1,438</td>
                <td class="text-success">+15%</td>
                <td><i class="fas fa-arrow-up text-success"></i></td>
            </tr>
            <tr>
                <td>ASA (segundos)</td>
                <td>25</td>
                <td>23</td>
                <td class="text-success">-8%</td>
                <td><i class="fas fa-arrow-down text-success"></i></td>
            </tr>
            <tr>
                <td>AHT (minutos)</td>
                <td>4:30</td>
                <td>4:38</td>
                <td class="text-warning">+3%</td>
                <td><i class="fas fa-arrow-up text-warning"></i></td>
            </tr>
            <tr>
                <td>Service Level (%)</td>
                <td>82%</td>
                <td>92%</td>
                <td class="text-success">+12%</td>
                <td><i class="fas fa-arrow-up text-success"></i></td>
            </tr>
        `;
    }

    function updateComparativeInsights() {
        document.getElementById('comparative-insights').innerHTML = `
            <div class="alert alert-success">
                <strong>Melhoria Significativa:</strong> O Service Level aumentou 12% no segundo período.
            </div>
            <div class="alert alert-info">
                <strong>Observação:</strong> O volume de chamadas aumentou 15%, mas o ASA melhorou.
            </div>
            <div class="alert alert-warning">
                <strong>Atenção:</strong> O AHT teve um pequeno aumento de 3%.
            </div>
        `;
    }

    function showLoading() {
        document.querySelectorAll('.loading').forEach(el => el.style.display = 'block');
    }

    function hideLoading() {
        document.querySelectorAll('.loading').forEach(el => el.style.display = 'none');
    }

    function generatePDF(type) {
        window.open(`/generate-pdf/${type}`, '_blank');
    }

    // Inicialização
    document.addEventListener('DOMContentLoaded', function() {
        const today = new Date();
        const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const twoWeeksAgo = new Date(today.getTime() - 14 * 24 * 60 * 60 * 1000);
        const threeWeeksAgo = new Date(today.getTime() - 21 * 24 * 60 * 60 * 1000);

        // Período 1 (3 semanas atrás até 2 semanas atrás)
        document.getElementById('period1_start').value = threeWeeksAgo.toISOString().split('T')[0];
        document.getElementById('period1_end').value = twoWeeksAgo.toISOString().split('T')[0];

        // Período 2 (semana passada até hoje)
        document.getElementById('period2_start').value = lastWeek.toISOString().split('T')[0];
        document.getElementById('period2_end').value = today.toISOString().split('T')[0];
    });
</script>
{% endblock %}