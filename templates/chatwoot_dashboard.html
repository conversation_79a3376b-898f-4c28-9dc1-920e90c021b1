<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Date adapter for Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

    <style>
        :root {
            /* Light Theme - Adaptado para WhatsApp */
            --bg-primary: #ffffff;
            --bg-secondary: #f0f2f5;
            --bg-tertiary: #e9edef;
            --text-primary: #111b21;
            --text-secondary: #667781;
            --text-muted: #8696a0;
            --border-color: #e9edef;
            --accent-primary: #25d366;
            --accent-secondary: #128c7e;
            --success: #25d366;
            --warning: #ffb84d;
            --danger: #f15c6d;
            --info: #54b3d6;
            --sidebar-bg: #111b21;
            --sidebar-text: #aebac1;
            --sidebar-active: #25d366;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }

        [data-theme="dark"] {
            /* Dark Theme - WhatsApp Dark */
            --bg-primary: #111b21;
            --bg-secondary: #0b141a;
            --bg-tertiary: #202c33;
            --text-primary: #e9edef;
            --text-secondary: #aebac1;
            --text-muted: #8696a0;
            --border-color: #2a3942;
            --accent-primary: #25d366;
            --accent-secondary: #20b358;
            --success: #25d366;
            --warning: #ffb84d;
            --danger: #f15c6d;
            --info: #54b3d6;
            --sidebar-bg: #0b141a;
            --sidebar-text: #8696a0;
            --sidebar-active: #25d366;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: var(--shadow-md);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header h1 {
            font-weight: 700;
            font-size: 1.75rem;
            margin: 0;
        }

        .header .subtitle {
            opacity: 0.9;
            font-size: 0.95rem;
            margin-top: 0.25rem;
        }

        /* Navigation */
        .nav-tabs {
            background-color: var(--bg-primary);
            border-bottom: 2px solid var(--border-color);
            padding: 0 1rem;
            margin-bottom: 2rem;
        }

        .nav-tabs .nav-link {
            color: var(--text-secondary);
            border: none;
            padding: 1rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-tabs .nav-link:hover {
            color: var(--accent-primary);
            background-color: transparent;
        }

        .nav-tabs .nav-link.active {
            color: var(--accent-primary);
            background-color: transparent;
            border-bottom: 3px solid var(--accent-primary);
        }

        /* Cards */
        .metric-card {
            background: var(--bg-primary);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            height: 100%;
        }

        .metric-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .metric-card .metric-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .metric-card .metric-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .metric-card .metric-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .metric-card .metric-change {
            font-size: 0.85rem;
            margin-top: 0.5rem;
        }

        /* Chart containers */
        .chart-container {
            background: var(--bg-primary);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .chart-container h5 {
            margin-bottom: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .chart-wrapper {
            position: relative;
            height: 300px;
        }

        /* Filters */
        .filters-card {
            background: var(--bg-primary);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
            border: none;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Status indicators */
        .status-indicator {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .status-connected {
            background-color: rgba(37, 211, 102, 0.1);
            color: var(--success);
        }

        .status-error {
            background-color: rgba(241, 92, 109, 0.1);
            color: var(--danger);
        }

        /* Loading states */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            color: var(--text-secondary);
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-top: 2px solid var(--accent-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 1.5rem;
            }
            
            .nav-tabs .nav-link {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }
            
            .metric-card {
                padding: 1rem;
                margin-bottom: 1rem;
            }
            
            .chart-wrapper {
                height: 250px;
            }
        }

        /* Theme toggle */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-md);
        }

        .theme-toggle:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <!-- Theme Toggle -->
    <div class="theme-toggle" id="themeToggle" title="Alternar tema">
        <i class="fas fa-moon" id="themeIcon"></i>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fab fa-whatsapp me-2"></i>Dashboard WhatsApp</h1>
                    <div class="subtitle">Métricas e Relatórios do Chatwoot</div>
                </div>
                <div class="col-md-6 text-end">
                    <div id="connectionStatus" class="status-indicator">
                        <div class="spinner"></div>
                        Verificando conexão...
                    </div>
                    <div class="mt-2">
                        <small id="lastUpdate">Última atualização: --:--</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="container-fluid mt-4" id="filtersSection" style="display: none;">
        <div class="filters-card">
            <h5><i class="fas fa-filter me-2"></i>Filtros de Data</h5>
            <form id="dateFilterForm" class="row g-3">
                <div class="col-md-4">
                    <label for="startDate" class="form-label">Data Inicial</label>
                    <input type="date" class="form-control" id="startDate" required>
                </div>
                <div class="col-md-4">
                    <label for="endDate" class="form-label">Data Final</label>
                    <input type="date" class="form-control" id="endDate" required>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-2"></i>Aplicar Filtros
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="quickFilter7days">
                        Últimos 7 dias
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="container-fluid">
        <ul class="nav nav-tabs" id="dashboardTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                    <i class="fas fa-chart-line me-2"></i>Visão Geral
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="satisfaction-tab" data-bs-toggle="tab" data-bs-target="#satisfaction" type="button" role="tab">
                    <i class="fas fa-star me-2"></i>Satisfação
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="response-time-tab" data-bs-toggle="tab" data-bs-target="#response-time" type="button" role="tab">
                    <i class="fas fa-clock me-2"></i>Tempo de Resposta
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="volume-tab" data-bs-toggle="tab" data-bs-target="#volume" type="button" role="tab">
                    <i class="fas fa-comments me-2"></i>Volume
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="agents-tab" data-bs-toggle="tab" data-bs-target="#agents" type="button" role="tab">
                    <i class="fas fa-users me-2"></i>Agentes
                </button>
            </li>
        </ul>
    </div>

    <!-- Tab Content -->
    <div class="container-fluid">
        <div class="tab-content" id="dashboardTabContent">
            <!-- Overview Tab -->
            <div class="tab-pane fade show active" id="overview" role="tabpanel">
                <!-- Main Metrics -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(37, 211, 102, 0.1); color: var(--success);">
                                <i class="fas fa-comments"></i>
                            </div>
                            <div class="metric-value" id="totalConversations">--</div>
                            <div class="metric-label">Total de Conversas</div>
                            <div class="metric-change text-success" id="conversationsChange">
                                <i class="fas fa-arrow-up me-1"></i>--
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(84, 179, 214, 0.1); color: var(--info);">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="metric-value" id="avgResponseTime">--</div>
                            <div class="metric-label">Tempo Médio de Resposta</div>
                            <div class="metric-change text-info" id="responseTimeChange">
                                <i class="fas fa-arrow-down me-1"></i>--
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(255, 184, 77, 0.1); color: var(--warning);">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="metric-value" id="avgSatisfaction">--</div>
                            <div class="metric-label">Satisfação Média</div>
                            <div class="metric-change text-warning" id="satisfactionChange">
                                <i class="fas fa-arrow-up me-1"></i>--
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(241, 92, 109, 0.1); color: var(--danger);">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="metric-value" id="resolutionRate">--</div>
                            <div class="metric-label">Taxa de Resolução</div>
                            <div class="metric-change text-success" id="resolutionChange">
                                <i class="fas fa-arrow-up me-1"></i>--
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="row">
                    <div class="col-lg-8 mb-4">
                        <div class="chart-container">
                            <h5>Volume de Conversas por Dia</h5>
                            <div class="chart-wrapper">
                                <canvas id="dailyVolumeChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 mb-4">
                        <div class="chart-container">
                            <h5>Status das Conversas</h5>
                            <div class="chart-wrapper">
                                <canvas id="statusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-6 mb-4">
                        <div class="chart-container">
                            <h5>Volume por Hora do Dia</h5>
                            <div class="chart-wrapper">
                                <canvas id="hourlyChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 mb-4">
                        <div class="chart-container">
                            <h5>Taxa de Resolução por Dia</h5>
                            <div class="chart-wrapper">
                                <canvas id="resolutionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Satisfaction Tab -->
            <div class="tab-pane fade" id="satisfaction" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(255, 184, 77, 0.1); color: var(--warning);">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="metric-value" id="totalRatings">--</div>
                            <div class="metric-label">Total de Avaliações</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(37, 211, 102, 0.1); color: var(--success);">
                                <i class="fas fa-thumbs-up"></i>
                            </div>
                            <div class="metric-value" id="positiveRatings">--</div>
                            <div class="metric-label">Avaliações Positivas</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(241, 92, 109, 0.1); color: var(--danger);">
                                <i class="fas fa-thumbs-down"></i>
                            </div>
                            <div class="metric-value" id="negativeRatings">--</div>
                            <div class="metric-label">Avaliações Negativas</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(84, 179, 214, 0.1); color: var(--info);">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="metric-value" id="satisfactionPercentage">--</div>
                            <div class="metric-label">Taxa de Satisfação</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8 mb-4">
                        <div class="chart-container">
                            <h5>Distribuição de Notas</h5>
                            <div class="chart-wrapper">
                                <canvas id="ratingsDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 mb-4">
                        <div class="chart-container">
                            <h5>NPS Score</h5>
                            <div class="chart-wrapper">
                                <canvas id="npsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="chart-container">
                            <h5>Performance de Satisfação por Agente</h5>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Agente</th>
                                            <th>Total Avaliações</th>
                                            <th>Nota Média</th>
                                            <th>Positivas</th>
                                            <th>Negativas</th>
                                            <th>Taxa de Satisfação</th>
                                        </tr>
                                    </thead>
                                    <tbody id="satisfactionAgentsTable">
                                        <tr>
                                            <td colspan="6" class="text-center">
                                                <div class="loading">
                                                    <div class="spinner"></div>
                                                    Carregando dados...
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Response Time Tab -->
            <div class="tab-pane fade" id="response-time" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(84, 179, 214, 0.1); color: var(--info);">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="metric-value" id="avgFirstResponse">--</div>
                            <div class="metric-label">Tempo Médio 1ª Resposta</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(37, 211, 102, 0.1); color: var(--success);">
                                <i class="fas fa-tachometer-alt"></i>
                            </div>
                            <div class="metric-value" id="medianResponseTime">--</div>
                            <div class="metric-label">Tempo Mediano</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(255, 184, 77, 0.1); color: var(--warning);">
                                <i class="fas fa-hourglass-half"></i>
                            </div>
                            <div class="metric-value" id="maxResponseTime">--</div>
                            <div class="metric-label">Tempo Máximo</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(241, 92, 109, 0.1); color: var(--danger);">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="metric-value" id="slaCompliance">--</div>
                            <div class="metric-label">SLA Compliance</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8 mb-4">
                        <div class="chart-container">
                            <h5>Tempo de Resposta por Dia</h5>
                            <div class="chart-wrapper">
                                <canvas id="responseTimeTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 mb-4">
                        <div class="chart-container">
                            <h5>Distribuição de Tempos</h5>
                            <div class="chart-wrapper">
                                <canvas id="responseTimeDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="chart-container">
                            <h5>Performance de Tempo de Resposta por Agente</h5>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Agente</th>
                                            <th>Total Conversas</th>
                                            <th>Tempo Médio (min)</th>
                                            <th>Tempo Mínimo (min)</th>
                                            <th>Tempo Máximo (min)</th>
                                            <th>SLA Compliance</th>
                                        </tr>
                                    </thead>
                                    <tbody id="responseTimeAgentsTable">
                                        <tr>
                                            <td colspan="6" class="text-center">
                                                <div class="loading">
                                                    <div class="spinner"></div>
                                                    Carregando dados...
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Volume Tab -->
            <div class="tab-pane fade" id="volume" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(37, 211, 102, 0.1); color: var(--success);">
                                <i class="fas fa-comments"></i>
                            </div>
                            <div class="metric-value" id="totalMessages">--</div>
                            <div class="metric-label">Total de Mensagens</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(84, 179, 214, 0.1); color: var(--info);">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="metric-value" id="incomingMessages">--</div>
                            <div class="metric-label">Mensagens Recebidas</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(255, 184, 77, 0.1); color: var(--warning);">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="metric-value" id="outgoingMessages">--</div>
                            <div class="metric-label">Mensagens Enviadas</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(241, 92, 109, 0.1); color: var(--danger);">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="metric-value" id="uniqueContacts">--</div>
                            <div class="metric-label">Contatos Únicos</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8 mb-4">
                        <div class="chart-container">
                            <h5>Volume de Mensagens por Hora</h5>
                            <div class="chart-wrapper">
                                <canvas id="messagesHourlyChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 mb-4">
                        <div class="chart-container">
                            <h5>Tipos de Mensagem</h5>
                            <div class="chart-wrapper">
                                <canvas id="messageTypesChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="chart-container">
                            <h5>Volume Diário de Mensagens</h5>
                            <div class="chart-wrapper">
                                <canvas id="messagesDailyChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Agents Tab -->
            <div class="tab-pane fade" id="agents" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(37, 211, 102, 0.1); color: var(--success);">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="metric-value" id="totalAgents">--</div>
                            <div class="metric-label">Total de Agentes</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(84, 179, 214, 0.1); color: var(--info);">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="metric-value" id="activeAgents">--</div>
                            <div class="metric-label">Agentes Ativos</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(255, 184, 77, 0.1); color: var(--warning);">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="metric-value" id="avgConversationsPerAgent">--</div>
                            <div class="metric-label">Conversas por Agente</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="metric-card">
                            <div class="metric-icon" style="background-color: rgba(241, 92, 109, 0.1); color: var(--danger);">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="metric-value" id="topAgentScore">--</div>
                            <div class="metric-label">Melhor Performance</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8 mb-4">
                        <div class="chart-container">
                            <h5>Performance dos Agentes</h5>
                            <div class="chart-wrapper">
                                <canvas id="agentsPerformanceChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 mb-4">
                        <div class="chart-container">
                            <h5>Distribuição de Workload</h5>
                            <div class="chart-wrapper">
                                <canvas id="workloadDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="chart-container">
                            <h5>Ranking de Agentes</h5>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Posição</th>
                                            <th>Agente</th>
                                            <th>Conversas</th>
                                            <th>Tempo Médio Resposta</th>
                                            <th>Taxa Resolução</th>
                                            <th>Satisfação</th>
                                            <th>Score</th>
                                        </tr>
                                    </thead>
                                    <tbody id="agentsRankingTable">
                                        <tr>
                                            <td colspan="7" class="text-center">
                                                <div class="loading">
                                                    <div class="spinner"></div>
                                                    Carregando dados...
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Variáveis globais para os gráficos
        let dailyVolumeChart, statusChart, hourlyChart, resolutionChart;
        let ratingsDistributionChart, npsChart, responseTimeTrendChart, responseTimeDistributionChart;
        let messagesHourlyChart, messageTypesChart, messagesDailyChart;
        let agentsPerformanceChart, workloadDistributionChart;

        document.addEventListener('DOMContentLoaded', function() {
            initializeTheme();
            updateClock();
            setInterval(updateClock, 1000);
            checkConnection();
            setupDateFilters();
            setupEventListeners();
        });

        // Theme Management
        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            updateThemeIcon(savedTheme);
        }

        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcon(newTheme);
        }

        function updateThemeIcon(theme) {
            const icon = document.getElementById('themeIcon');
            if (icon) {
                icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }

        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('pt-BR');
            const lastUpdateElement = document.getElementById('lastUpdate');
            if (lastUpdateElement) {
                lastUpdateElement.textContent = `Última atualização: ${timeString}`;
            }
        }

        // Event Listeners
        document.getElementById('themeToggle')?.addEventListener('click', toggleTheme);

        function setupDateFilters() {
            // Definir datas padrão (últimos 7 dias)
            const today = new Date();
            const weekAgo = new Date(today);
            weekAgo.setDate(today.getDate() - 7);

            document.getElementById('startDate').value = weekAgo.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
        }

        function setupEventListeners() {
            document.getElementById('dateFilterForm').addEventListener('submit', function(e) {
                e.preventDefault();
                loadMetrics();
            });

            document.getElementById('quickFilter7days')?.addEventListener('click', function() {
                const today = new Date();
                const weekAgo = new Date(today);
                weekAgo.setDate(today.getDate() - 7);

                document.getElementById('startDate').value = weekAgo.toISOString().split('T')[0];
                document.getElementById('endDate').value = today.toISOString().split('T')[0];
                loadMetrics();
            });
        }

        async function checkConnection() {
            try {
                const response = await fetch('/api/v1/chatwoot/health');
                const data = await response.json();

                const statusElement = document.getElementById('connectionStatus');

                if (data.status === 'healthy') {
                    statusElement.innerHTML = `
                        <span class="status-indicator status-connected">
                            <i class="fas fa-check-circle me-2"></i>Conectado ao Chatwoot
                        </span>
                    `;
                    document.getElementById('filtersSection').style.display = 'block';
                    loadMetrics(); // Carregar dados iniciais
                } else {
                    statusElement.innerHTML = `
                        <span class="status-indicator status-error">
                            <i class="fas fa-exclamation-triangle me-2"></i>Erro na conexão
                        </span>
                    `;
                }
            } catch (error) {
                console.error('Erro ao verificar conexão:', error);
                document.getElementById('connectionStatus').innerHTML = `
                    <span class="status-indicator status-error">
                        <i class="fas fa-times-circle me-2"></i>Falha na conexão
                    </span>
                `;
            }
        }

        async function loadMetrics() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                alert('Por favor, selecione as datas inicial e final.');
                return;
            }

            // Mostrar loading
            showLoading();

            try {
                // Carregar todas as métricas em paralelo
                const [dashboardResponse, satisfactionResponse, responseTimeResponse, volumeResponse, agentsResponse] = await Promise.all([
                    fetch(`/api/v1/chatwoot/dashboard?start_date=${startDate}&end_date=${endDate}`),
                    fetch(`/api/v1/chatwoot/satisfaction?start_date=${startDate}&end_date=${endDate}`),
                    fetch(`/api/v1/chatwoot/response-time?start_date=${startDate}&end_date=${endDate}`),
                    fetch(`/api/v1/chatwoot/volume?start_date=${startDate}&end_date=${endDate}`),
                    fetch(`/api/v1/chatwoot/agents?start_date=${startDate}&end_date=${endDate}`)
                ]);

                const [dashboardData, satisfactionData, responseTimeData, volumeData, agentsData] = await Promise.all([
                    dashboardResponse.json(),
                    satisfactionResponse.json(),
                    responseTimeResponse.json(),
                    volumeResponse.json(),
                    agentsResponse.json()
                ]);

                if (dashboardData.success) {
                    // Atualizar métricas principais
                    updateMainMetrics(dashboardData.data);

                    // Atualizar gráficos da visão geral
                    updateOverviewCharts(dashboardData.data);
                }

                if (satisfactionData.success) {
                    updateSatisfactionTab(satisfactionData.data);
                }

                if (responseTimeData.success) {
                    updateResponseTimeTab(responseTimeData.data);
                }

                if (volumeData.success) {
                    updateVolumeTab(volumeData.data);
                }

                if (agentsData.success) {
                    updateAgentsTab(agentsData.data);
                }

                hideLoading();

            } catch (error) {
                console.error('Erro ao carregar métricas:', error);
                hideLoading();
                alert('Erro ao carregar dados. Tente novamente.');
            }
        }

        function showLoading() {
            // Implementar loading states
            const loadingElements = document.querySelectorAll('.loading');
            loadingElements.forEach(el => el.style.display = 'flex');
        }

        function hideLoading() {
            // Esconder loading states
            const loadingElements = document.querySelectorAll('.loading');
            loadingElements.forEach(el => el.style.display = 'none');
        }

        function updateMainMetrics(data) {
            // Atualizar métricas principais da visão geral
            document.getElementById('totalConversations').textContent = data.conversations?.total || '--';
            document.getElementById('avgResponseTime').textContent = data.responseTime?.averageMinutes ?
                `${Math.round(data.responseTime.averageMinutes)}min` : '--';
            document.getElementById('avgSatisfaction').textContent = data.satisfaction?.averageRating ?
                `${parseFloat(data.satisfaction.averageRating).toFixed(1)}/5` : '--';
            document.getElementById('resolutionRate').textContent = data.conversations?.resolutionRate ?
                `${data.conversations.resolutionRate}%` : '--';

            // Atualizar indicadores de mudança (simulado)
            updateChangeIndicators(data);
        }

        function updateChangeIndicators(data) {
            // Simular mudanças percentuais
            const changes = {
                conversations: '+12%',
                responseTime: '-8%',
                satisfaction: '+5%',
                resolution: '+3%'
            };

            document.getElementById('conversationsChange').innerHTML =
                `<i class="fas fa-arrow-up me-1"></i>${changes.conversations}`;
            document.getElementById('responseTimeChange').innerHTML =
                `<i class="fas fa-arrow-down me-1"></i>${changes.responseTime}`;
            document.getElementById('satisfactionChange').innerHTML =
                `<i class="fas fa-arrow-up me-1"></i>${changes.satisfaction}`;
            document.getElementById('resolutionChange').innerHTML =
                `<i class="fas fa-arrow-up me-1"></i>${changes.resolution}`;
        }

        function updateOverviewCharts(data) {
            // Atualizar gráfico de volume diário
            updateDailyVolumeChart(data.dailyVolume || []);

            // Atualizar gráfico de status
            updateStatusChart(data.conversations || {});

            // Atualizar gráfico por hora
            updateHourlyChart(data.hourlyVolume || []);

            // Atualizar gráfico de resolução
            updateResolutionChart(data.dailyResolution || []);
        }

        function updateDailyVolumeChart(dailyData) {
            const ctx = document.getElementById('dailyVolumeChart').getContext('2d');

            if (dailyVolumeChart) {
                dailyVolumeChart.destroy();
            }

            const labels = dailyData.map(item => new Date(item.date).toLocaleDateString('pt-BR'));
            const data = dailyData.map(item => item.total_conversations);

            dailyVolumeChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Conversas',
                        data: data,
                        borderColor: '#25d366',
                        backgroundColor: 'rgba(37, 211, 102, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        function updateStatusChart(conversationsData) {
            const ctx = document.getElementById('statusChart').getContext('2d');

            if (statusChart) {
                statusChart.destroy();
            }

            const data = [
                conversationsData.resolved || 0,
                conversationsData.open || 0,
                conversationsData.pending || 0
            ];

            statusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Resolvidas', 'Abertas', 'Pendentes'],
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            '#25d366',
                            '#54b3d6',
                            '#ffb84d'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function updateHourlyChart(hourlyData) {
            const ctx = document.getElementById('hourlyChart').getContext('2d');

            if (hourlyChart) {
                hourlyChart.destroy();
            }

            const labels = hourlyData.map(item => `${item.hour}:00`);
            const data = hourlyData.map(item => item.total_conversations);

            hourlyChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Conversas',
                        data: data,
                        backgroundColor: '#128c7e',
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function updateResolutionChart(resolutionData) {
            const ctx = document.getElementById('resolutionChart').getContext('2d');

            if (resolutionChart) {
                resolutionChart.destroy();
            }

            const labels = resolutionData.map(item => new Date(item.date).toLocaleDateString('pt-BR'));
            const data = resolutionData.map(item => item.resolution_rate);

            resolutionChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Taxa de Resolução (%)',
                        data: data,
                        borderColor: '#f15c6d',
                        backgroundColor: 'rgba(241, 92, 109, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            });
        }

        function updateSatisfactionTab(data) {
            // Atualizar métricas de satisfação
            document.getElementById('totalRatings').textContent = data.summary?.total_ratings || '--';
            document.getElementById('positiveRatings').textContent = data.summary?.positive_ratings || '--';
            document.getElementById('negativeRatings').textContent = data.summary?.negative_ratings || '--';
            document.getElementById('satisfactionPercentage').textContent = data.summary?.satisfaction_rate ?
                `${data.summary.satisfaction_rate}%` : '--';

            // Atualizar tabela de agentes
            updateSatisfactionAgentsTable(data.agents || []);
        }

        function updateResponseTimeTab(data) {
            // Atualizar métricas de tempo de resposta
            document.getElementById('avgFirstResponse').textContent = data.summary?.avg_first_response_time_minutes ?
                `${Math.round(data.summary.avg_first_response_time_minutes)}min` : '--';
            document.getElementById('medianResponseTime').textContent = data.summary?.median_response_time_minutes ?
                `${Math.round(data.summary.median_response_time_minutes)}min` : '--';
            document.getElementById('maxResponseTime').textContent = data.summary?.max_response_time_minutes ?
                `${Math.round(data.summary.max_response_time_minutes)}min` : '--';
            document.getElementById('slaCompliance').textContent = data.summary?.sla_compliance ?
                `${data.summary.sla_compliance}%` : '--';

            // Atualizar tabela de agentes
            updateResponseTimeAgentsTable(data.agents || []);
        }

        function updateVolumeTab(data) {
            // Atualizar métricas de volume
            document.getElementById('totalMessages').textContent = data.summary?.total_messages || '--';
            document.getElementById('incomingMessages').textContent = data.summary?.incoming_messages || '--';
            document.getElementById('outgoingMessages').textContent = data.summary?.outgoing_messages || '--';
            document.getElementById('uniqueContacts').textContent = data.summary?.unique_contacts || '--';
        }

        function updateAgentsTab(data) {
            // Atualizar métricas de agentes
            document.getElementById('totalAgents').textContent = data.summary?.total_agents || '--';
            document.getElementById('activeAgents').textContent = data.summary?.active_agents || '--';
            document.getElementById('avgConversationsPerAgent').textContent = data.summary?.avg_conversations_per_agent || '--';
            document.getElementById('topAgentScore').textContent = data.summary?.top_agent_score || '--';

            // Atualizar tabela de ranking
            updateAgentsRankingTable(data.agents || []);
        }

        function updateSatisfactionAgentsTable(agents) {
            const tbody = document.getElementById('satisfactionAgentsTable');

            if (agents.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">Nenhum dado disponível</td></tr>';
                return;
            }

            tbody.innerHTML = agents.map(agent => `
                <tr>
                    <td><strong>${agent.agent_name}</strong></td>
                    <td>${agent.total_ratings}</td>
                    <td>${parseFloat(agent.average_rating).toFixed(1)}/5</td>
                    <td><span class="badge bg-success">${agent.positive_ratings}</span></td>
                    <td><span class="badge bg-danger">${agent.negative_ratings}</span></td>
                    <td>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar bg-success" style="width: ${agent.satisfaction_percentage}%">
                                ${agent.satisfaction_percentage}%
                            </div>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function updateResponseTimeAgentsTable(agents) {
            const tbody = document.getElementById('responseTimeAgentsTable');

            if (agents.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">Nenhum dado disponível</td></tr>';
                return;
            }

            tbody.innerHTML = agents.map(agent => `
                <tr>
                    <td><strong>${agent.agent_name}</strong></td>
                    <td>${agent.total_conversations}</td>
                    <td>${Math.round(agent.avg_first_response_time_minutes)}</td>
                    <td>${Math.round(agent.min_response_time_minutes)}</td>
                    <td>${Math.round(agent.max_response_time_minutes)}</td>
                    <td>
                        <span class="badge ${agent.sla_compliance >= 80 ? 'bg-success' : agent.sla_compliance >= 60 ? 'bg-warning' : 'bg-danger'}">
                            ${agent.sla_compliance}%
                        </span>
                    </td>
                </tr>
            `).join('');
        }

        function updateAgentsRankingTable(agents) {
            const tbody = document.getElementById('agentsRankingTable');

            if (agents.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">Nenhum dado disponível</td></tr>';
                return;
            }

            tbody.innerHTML = agents.map((agent, index) => `
                <tr>
                    <td>
                        <span class="badge ${index < 3 ? 'bg-warning' : 'bg-secondary'}">
                            ${index + 1}º
                        </span>
                    </td>
                    <td><strong>${agent.agent_name}</strong></td>
                    <td>${agent.total_conversations}</td>
                    <td>${Math.round(agent.avg_response_time_minutes)}min</td>
                    <td>${agent.resolution_rate}%</td>
                    <td>${parseFloat(agent.satisfaction_rating).toFixed(1)}/5</td>
                    <td>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar bg-primary" style="width: ${agent.performance_score}%">
                                ${agent.performance_score}
                            </div>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // Inicializar quando a página carregar
        window.addEventListener('load', function() {
            // Carregar dados iniciais se a conexão estiver ok
            setTimeout(checkConnection, 1000);
        });
    </script>
</body>
</html>
