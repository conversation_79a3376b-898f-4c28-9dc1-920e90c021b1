<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard WhatsApp - Sistema de Relatórios</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --whatsapp-green: #25d366;
            --whatsapp-dark-green: #128c7e;
            --whatsapp-light-green: #dcf8c6;
            --whatsapp-gray: #f0f0f0;
            --whatsapp-dark-gray: #303030;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
        }

        /* Header Principal */
        .main-header {
            background: linear-gradient(135deg, var(--whatsapp-green), var(--whatsapp-dark-green));
            box-shadow: 0 2px 10px rgba(37, 211, 102, 0.2);
            padding: 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
            height: 70px;
        }

        .header-nav {
            flex: 1;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: 0;
        }

        .nav-menu-item {
            position: relative;
        }

        .nav-menu-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1.25rem 2rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            border-radius: 0;
            position: relative;
            overflow: hidden;
        }

        .nav-menu-link:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .nav-menu-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: inset 0 -3px 0 white;
        }

        .nav-menu-link i {
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .theme-toggle {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }

        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }

        .current-time {
            color: white;
            font-size: 0.9rem;
            font-weight: 500;
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 180px;
            text-align: center;
        }
        
        .nav-tabs {
            border-bottom: 2px solid var(--whatsapp-green);
            margin-bottom: 2rem;
        }
        
        .nav-tabs .nav-link {
            border: none;
            color: #6c757d;
            font-weight: 500;
            padding: 1rem 1.5rem;
            transition: all 0.3s ease;
        }
        
        .nav-tabs .nav-link:hover {
            color: var(--whatsapp-green);
            border-color: transparent;
        }
        
        .nav-tabs .nav-link.active {
            color: var(--whatsapp-green);
            background-color: transparent;
            border-color: transparent;
            border-bottom: 3px solid var(--whatsapp-green);
        }
        
        .metric-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            background: white;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--whatsapp-green);
            margin-bottom: 0.5rem;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #6c757d;
        }
        
        .loading i {
            font-size: 2rem;
            margin-bottom: 1rem;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .btn-whatsapp {
            background: linear-gradient(135deg, var(--whatsapp-green), var(--whatsapp-dark-green));
            border: none;
            color: white;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-whatsapp:hover {
            background: linear-gradient(135deg, var(--whatsapp-dark-green), var(--whatsapp-green));
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }
        
        .filter-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .form-control:focus {
            border-color: var(--whatsapp-green);
            box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25);
        }
        
        .table-responsive {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .table {
            margin-bottom: 0;
        }
        
        .table thead th {
            background: linear-gradient(135deg, var(--whatsapp-green), var(--whatsapp-dark-green));
            color: white;
            border: none;
            font-weight: 600;
            padding: 1rem;
        }
        
        .table tbody tr:hover {
            background-color: var(--whatsapp-light-green);
        }
        
        .badge-success {
            background-color: var(--whatsapp-green) !important;
        }
        
        .text-whatsapp {
            color: var(--whatsapp-green) !important;
        }
        
        .alert-info {
            background-color: rgba(37, 211, 102, 0.1);
            border-color: var(--whatsapp-green);
            color: var(--whatsapp-dark-green);
        }
        

        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-online {
            background-color: #28a745;
            animation: pulse 2s infinite;
        }
        
        .status-offline {
            background-color: #dc3545;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .quick-filters {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .quick-filter-btn {
            background: transparent;
            border: 2px solid var(--whatsapp-green);
            color: var(--whatsapp-green);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .quick-filter-btn:hover,
        .quick-filter-btn.active {
            background: var(--whatsapp-green);
            color: white;
        }
        
        @media (max-width: 768px) {
            .metric-value {
                font-size: 2rem;
            }

            .chart-container {
                height: 300px;
                padding: 1rem;
            }

            .nav-tabs .nav-link {
                padding: 0.75rem 1rem;
                font-size: 0.875rem;
            }

            /* Header responsivo */
            .header-container {
                padding: 0 1rem;
                height: auto;
                min-height: 70px;
                flex-direction: column;
                gap: 1rem;
                padding-top: 1rem;
                padding-bottom: 1rem;
            }

            .nav-menu {
                flex-direction: row;
                justify-content: center;
                width: 100%;
            }

            .nav-menu-link {
                padding: 0.75rem 1rem;
                font-size: 0.85rem;
                flex-direction: column;
                gap: 0.25rem;
                text-align: center;
                min-width: 80px;
            }

            .nav-menu-link span {
                font-size: 0.75rem;
            }

            .nav-menu-link i {
                font-size: 1.2rem;
            }

            .header-actions {
                gap: 1rem;
                justify-content: center;
            }

            .current-time {
                font-size: 0.8rem;
                padding: 0.4rem 0.8rem;
                min-width: 160px;
            }

            .theme-toggle {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .nav-menu-link span {
                display: none;
            }

            .nav-menu-link {
                min-width: 60px;
                padding: 0.75rem 0.5rem;
            }

            .current-time {
                min-width: 140px;
                font-size: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header Principal -->
    <header class="main-header">
        <div class="header-container">
            <nav class="header-nav">
                <ul class="nav-menu">
                    <li class="nav-menu-item">
                        <a href="/" class="nav-menu-link" data-section="telefonia">
                            <i class="fas fa-phone"></i>
                            <span>Telefonia</span>
                        </a>
                    </li>
                    <li class="nav-menu-item">
                        <a href="/chatwoot" class="nav-menu-link active" data-section="whatsapp">
                            <i class="fab fa-whatsapp"></i>
                            <span>WhatsApp</span>
                        </a>
                    </li>
                    <li class="nav-menu-item">
                        <a href="#" class="nav-menu-link" data-section="consolidado">
                            <i class="fas fa-chart-pie"></i>
                            <span>Consolidado</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="header-actions">
                <button class="theme-toggle" id="themeToggle" title="Alternar tema">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="current-time" id="current-time">31/07/2025, 11:45:59</div>
            </div>
        </div>
    </header>



    <div class="container" style="margin-top: 2rem;">
        <!-- Filtros -->
        <div class="filter-card">
            <div class="row align-items-end">
                <div class="col-md-3">
                    <label for="start_date" class="form-label">
                        <i class="fas fa-calendar-alt me-1"></i>
                        Data Inicial
                    </label>
                    <input type="date" class="form-control" id="start_date">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">
                        <i class="fas fa-calendar-alt me-1"></i>
                        Data Final
                    </label>
                    <input type="date" class="form-control" id="end_date">
                </div>
                <div class="col-md-3">
                    <label for="agent_filter" class="form-label">
                        <i class="fas fa-user me-1"></i>
                        Agente (Opcional)
                    </label>
                    <select class="form-control" id="agent_filter">
                        <option value="">Todos os agentes</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-whatsapp w-100" onclick="loadDashboard()">
                        <i class="fas fa-sync-alt me-2"></i>
                        Atualizar
                    </button>
                </div>
            </div>
            
            <!-- Filtros Rápidos -->
            <div class="mt-3">
                <label class="form-label">Períodos Rápidos:</label>
                <div class="quick-filters">
                    <button class="quick-filter-btn" onclick="setQuickFilter(7)">7 dias</button>
                    <button class="quick-filter-btn" onclick="setQuickFilter(14)">14 dias</button>
                    <button class="quick-filter-btn active" onclick="setQuickFilter(30)">30 dias</button>
                    <button class="quick-filter-btn" onclick="setQuickFilter(90)">90 dias</button>
                </div>
            </div>
        </div>

        <!-- Abas de Navegação -->
        <ul class="nav nav-tabs" id="dashboardTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Visão Geral
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="satisfaction-tab" data-bs-toggle="tab" data-bs-target="#satisfaction" type="button" role="tab">
                    <i class="fas fa-star me-2"></i>
                    Satisfação
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="response-time-tab" data-bs-toggle="tab" data-bs-target="#response-time" type="button" role="tab">
                    <i class="fas fa-clock me-2"></i>
                    Tempo de Resposta
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="volume-tab" data-bs-toggle="tab" data-bs-target="#volume" type="button" role="tab">
                    <i class="fas fa-chart-bar me-2"></i>
                    Volume
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="performance-tab" data-bs-toggle="tab" data-bs-target="#performance" type="button" role="tab">
                    <i class="fas fa-users me-2"></i>
                    Performance
                </button>
            </li>
        </ul>

        <!-- Conteúdo das Abas -->
        <div class="tab-content" id="dashboardTabsContent">
            <!-- Aba: Visão Geral -->
            <div class="tab-pane fade show active" id="overview" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value" id="total-conversations">-</div>
                                <div class="metric-label">Total de Conversas</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-success" id="resolved-conversations">-</div>
                                <div class="metric-label">Conversas Resolvidas</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-info" id="avg-response-time">-</div>
                                <div class="metric-label">Tempo Médio Resposta</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-warning" id="satisfaction-rate">-</div>
                                <div class="metric-label">Taxa de Satisfação</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-8 mb-4">
                        <div class="chart-container">
                            <h5 class="mb-3">
                                <i class="fas fa-chart-line me-2 text-whatsapp"></i>
                                Tendência de Conversas
                            </h5>
                            <div id="overview-trend-chart">
                                <div class="loading">
                                    <i class="fas fa-spinner"></i>
                                    <span>Carregando gráfico...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="chart-container">
                            <h5 class="mb-3">
                                <i class="fas fa-chart-pie me-2 text-whatsapp"></i>
                                Status das Conversas
                            </h5>
                            <div id="overview-status-chart">
                                <div class="loading">
                                    <i class="fas fa-spinner"></i>
                                    <span>Carregando gráfico...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aba: Satisfação -->
            <div class="tab-pane fade" id="satisfaction" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value" id="total-ratings">-</div>
                                <div class="metric-label">Total de Avaliações</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-warning" id="average-rating">-</div>
                                <div class="metric-label">Nota Média</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-success" id="satisfaction-percentage">-</div>
                                <div class="metric-label">Taxa de Satisfação</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-info" id="nps-score">-</div>
                                <div class="metric-label">NPS Score</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="chart-container">
                            <h5 class="mb-3">
                                <i class="fas fa-star me-2 text-whatsapp"></i>
                                Distribuição de Notas
                            </h5>
                            <div id="satisfaction-distribution-chart">
                                <div class="loading">
                                    <i class="fas fa-spinner"></i>
                                    <span>Carregando gráfico...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="chart-container">
                            <h5 class="mb-3">
                                <i class="fas fa-chart-line me-2 text-whatsapp"></i>
                                Tendência de Satisfação
                            </h5>
                            <div id="satisfaction-trend-chart">
                                <div class="loading">
                                    <i class="fas fa-spinner"></i>
                                    <span>Carregando gráfico...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">
                                    <i class="fas fa-table me-2"></i>
                                    Performance por Agente
                                </h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0" id="satisfaction-agents-table">
                                        <thead>
                                            <tr>
                                                <th>Agente</th>
                                                <th>Avaliações</th>
                                                <th>Nota Média</th>
                                                <th>Satisfação</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td colspan="5" class="text-center py-4">
                                                    <div class="loading">
                                                        <i class="fas fa-spinner"></i>
                                                        <span>Carregando dados...</span>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aba: Tempo de Resposta -->
            <div class="tab-pane fade" id="response-time" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value" id="avg-response-minutes">-</div>
                                <div class="metric-label">Tempo Médio (min)</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-success" id="sla-compliance">-</div>
                                <div class="metric-label">SLA Compliance</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-info" id="median-response">-</div>
                                <div class="metric-label">Tempo Mediano</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-warning" id="max-response">-</div>
                                <div class="metric-label">Tempo Máximo</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8 mb-4">
                        <div class="chart-container">
                            <h5 class="mb-3">
                                <i class="fas fa-chart-bar me-2 text-whatsapp"></i>
                                Distribuição por Faixas de Tempo
                            </h5>
                            <div id="response-time-distribution-chart">
                                <div class="loading">
                                    <i class="fas fa-spinner"></i>
                                    <span>Carregando gráfico...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="chart-container">
                            <h5 class="mb-3">
                                <i class="fas fa-clock me-2 text-whatsapp"></i>
                                SLA por Faixa
                            </h5>
                            <div id="sla-breakdown-chart">
                                <div class="loading">
                                    <i class="fas fa-spinner"></i>
                                    <span>Carregando gráfico...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aba: Volume -->
            <div class="tab-pane fade" id="volume" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value" id="total-messages">-</div>
                                <div class="metric-label">Total de Mensagens</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-success" id="unique-contacts">-</div>
                                <div class="metric-label">Contatos Únicos</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-info" id="daily-average">-</div>
                                <div class="metric-label">Média Diária</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-warning" id="peak-hour">-</div>
                                <div class="metric-label">Horário de Pico</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8 mb-4">
                        <div class="chart-container">
                            <h5 class="mb-3">
                                <i class="fas fa-chart-line me-2 text-whatsapp"></i>
                                Volume Diário
                            </h5>
                            <div id="volume-daily-chart">
                                <div class="loading">
                                    <i class="fas fa-spinner"></i>
                                    <span>Carregando gráfico...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="chart-container">
                            <h5 class="mb-3">
                                <i class="fas fa-clock me-2 text-whatsapp"></i>
                                Volume por Hora
                            </h5>
                            <div id="volume-hourly-chart">
                                <div class="loading">
                                    <i class="fas fa-spinner"></i>
                                    <span>Carregando gráfico...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aba: Performance -->
            <div class="tab-pane fade" id="performance" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value" id="total-agents">-</div>
                                <div class="metric-label">Total de Agentes</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-success" id="active-agents">-</div>
                                <div class="metric-label">Agentes Ativos</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-info" id="avg-performance-score">-</div>
                                <div class="metric-label">Score Médio</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card metric-card">
                            <div class="card-body text-center">
                                <div class="metric-value text-warning" id="top-performer-score">-</div>
                                <div class="metric-label">Melhor Score</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8 mb-4">
                        <div class="chart-container">
                            <h5 class="mb-3">
                                <i class="fas fa-chart-bar me-2 text-whatsapp"></i>
                                Ranking de Performance
                            </h5>
                            <div id="performance-ranking-chart">
                                <div class="loading">
                                    <i class="fas fa-spinner"></i>
                                    <span>Carregando gráfico...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">
                                    <i class="fas fa-trophy me-2 text-whatsapp"></i>
                                    Top 3 Performers
                                </h5>
                            </div>
                            <div class="card-body" id="top-performers-list">
                                <div class="loading">
                                    <i class="fas fa-spinner"></i>
                                    <span>Carregando ranking...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Configurações globais
        const API_BASE_URL = '/api/v1/chatwoot';
        let currentData = {};
        let charts = {};

        // Configuração padrão do Chart.js
        Chart.defaults.font.family = 'Inter';
        Chart.defaults.color = '#6c757d';

        // Cores do WhatsApp
        const colors = {
            primary: '#25d366',
            secondary: '#128c7e',
            success: '#28a745',
            warning: '#ffc107',
            danger: '#dc3545',
            info: '#17a2b8',
            light: '#f8f9fa',
            dark: '#343a40'
        };

        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
            loadAgentsList();
            setQuickFilter(30); // Padrão: 30 dias
        });

        function initializeDashboard() {
            // Configurar datas padrão
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);

            document.getElementById('start_date').value = formatDate(startDate);
            document.getElementById('end_date').value = formatDate(endDate);

            // Carregar dados iniciais
            loadDashboard();
        }

        function formatDate(date) {
            return date.toISOString().split('T')[0];
        }

        function setQuickFilter(days) {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days);

            document.getElementById('start_date').value = formatDate(startDate);
            document.getElementById('end_date').value = formatDate(endDate);

            // Atualizar botões ativos
            document.querySelectorAll('.quick-filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // Atualizar período atual
            updateCurrentPeriod();

            // Carregar dados
            loadDashboard();
        }

        function updateCurrentPeriod() {
            const startDate = document.getElementById('start_date').value;
            const endDate = document.getElementById('end_date').value;

            if (startDate && endDate) {
                const start = new Date(startDate).toLocaleDateString('pt-BR');
                const end = new Date(endDate).toLocaleDateString('pt-BR');
                document.getElementById('current-period').textContent = `${start} - ${end}`;
            }
        }

        async function loadAgentsList() {
            try {
                const response = await fetch(`${API_BASE_URL}/agents`);
                const result = await response.json();

                if (result.success) {
                    const select = document.getElementById('agent_filter');
                    select.innerHTML = '<option value="">Todos os agentes</option>';

                    result.data.forEach(agent => {
                        const option = document.createElement('option');
                        option.value = agent.id;
                        option.textContent = agent.name;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Erro ao carregar lista de agentes:', error);
            }
        }

        async function loadDashboard() {
            const startDate = document.getElementById('start_date').value;
            const endDate = document.getElementById('end_date').value;

            if (!startDate || !endDate) {
                showAlert('Por favor, selecione as datas inicial e final.', 'warning');
                return;
            }

            updateCurrentPeriod();
            showLoading(true);

            try {
                // Carregar dados do dashboard
                await Promise.all([
                    loadOverviewData(),
                    loadSatisfactionData(),
                    loadResponseTimeData(),
                    loadVolumeData(),
                    loadPerformanceData()
                ]);

                showAlert('Dashboard atualizado com sucesso!', 'success');
            } catch (error) {
                console.error('Erro ao carregar dashboard:', error);
                showAlert('Erro ao carregar dados do dashboard.', 'danger');
            } finally {
                showLoading(false);
            }
        }

        async function loadOverviewData() {
            try {
                const params = new URLSearchParams({
                    start_date: document.getElementById('start_date').value,
                    end_date: document.getElementById('end_date').value
                });

                const response = await fetch(`${API_BASE_URL}/dashboard?${params}`);
                const result = await response.json();

                if (result.success) {
                    updateOverviewMetrics(result.data);
                    createOverviewCharts(result.data);
                }
            } catch (error) {
                console.error('Erro ao carregar dados de visão geral:', error);
            }
        }

        function updateOverviewMetrics(data) {
            const conversations = data.conversations || {};
            const responseTime = data.response_time || {};
            const satisfaction = data.satisfaction || {};

            document.getElementById('total-conversations').textContent =
                conversations.total_conversations || 0;
            document.getElementById('resolved-conversations').textContent =
                conversations.resolved_conversations || 0;
            document.getElementById('avg-response-time').textContent =
                responseTime.avg_response_minutes ? `${responseTime.avg_response_minutes}min` : '-';
            document.getElementById('satisfaction-rate').textContent =
                satisfaction.satisfaction_rate ? `${satisfaction.satisfaction_rate}%` : '-';
        }

        function createOverviewCharts(data) {
            // Gráfico de tendência (simulado com dados limitados)
            createTrendChart();

            // Gráfico de status das conversas
            createStatusChart(data.conversations || {});
        }

        function createTrendChart() {
            const ctx = document.getElementById('overview-trend-chart');
            if (!ctx) return;

            // Limpar conteúdo anterior
            ctx.innerHTML = '';

            // Criar canvas
            const canvas = document.createElement('canvas');
            ctx.appendChild(canvas);

            // Dados simulados para demonstração
            const labels = [];
            const data = [];
            const today = new Date();

            for (let i = 6; i >= 0; i--) {
                const date = new Date(today);
                date.setDate(date.getDate() - i);
                labels.push(date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' }));
                data.push(Math.floor(Math.random() * 10) + 1); // Dados simulados
            }

            if (charts.trendChart) {
                charts.trendChart.destroy();
            }

            charts.trendChart = new Chart(canvas, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Conversas',
                        data: data,
                        borderColor: colors.primary,
                        backgroundColor: colors.primary + '20',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: '#f0f0f0'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        function createStatusChart(conversations) {
            const ctx = document.getElementById('overview-status-chart');
            if (!ctx) return;

            // Limpar conteúdo anterior
            ctx.innerHTML = '';

            // Criar canvas
            const canvas = document.createElement('canvas');
            ctx.appendChild(canvas);

            const resolved = conversations.resolved_conversations || 0;
            const open = conversations.open_conversations || 0;
            const pending = conversations.pending_conversations || 0;

            if (charts.statusChart) {
                charts.statusChart.destroy();
            }

            charts.statusChart = new Chart(canvas, {
                type: 'doughnut',
                data: {
                    labels: ['Resolvidas', 'Abertas', 'Pendentes'],
                    datasets: [{
                        data: [resolved, open, pending],
                        backgroundColor: [colors.success, colors.danger, colors.warning],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
        }

        async function loadSatisfactionData() {
            // Implementação simplificada - dados limitados no banco
            updateSatisfactionMetrics({
                total_ratings: 0,
                average_rating: 0,
                satisfaction_rate: 0,
                nps_score: 0
            });

            showNoDataMessage('satisfaction-distribution-chart', 'Nenhuma avaliação de satisfação encontrada');
            showNoDataMessage('satisfaction-trend-chart', 'Dados insuficientes para análise de tendência');
            updateSatisfactionTable([]);
        }

        function updateSatisfactionMetrics(data) {
            document.getElementById('total-ratings').textContent = data.total_ratings || 0;
            document.getElementById('average-rating').textContent =
                data.average_rating ? data.average_rating.toFixed(1) : '-';
            document.getElementById('satisfaction-percentage').textContent =
                data.satisfaction_rate ? `${data.satisfaction_rate}%` : '-';
            document.getElementById('nps-score').textContent = data.nps_score || '-';
        }

        function updateSatisfactionTable(agents) {
            const tbody = document.querySelector('#satisfaction-agents-table tbody');

            if (agents.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center py-4 text-muted">
                            <i class="fas fa-info-circle me-2"></i>
                            Nenhum dado de satisfação disponível
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = agents.map(agent => `
                <tr>
                    <td><strong>${agent.agent_name}</strong></td>
                    <td>${agent.total_ratings}</td>
                    <td>${agent.average_rating ? agent.average_rating.toFixed(1) : '-'}</td>
                    <td>
                        <span class="badge ${agent.satisfaction_rate >= 80 ? 'bg-success' : agent.satisfaction_rate >= 60 ? 'bg-warning' : 'bg-danger'}">
                            ${agent.satisfaction_rate ? agent.satisfaction_rate.toFixed(1) : 0}%
                        </span>
                    </td>
                    <td>
                        <span class="status-indicator ${agent.total_ratings > 0 ? 'status-online' : 'status-offline'}"></span>
                        ${agent.total_ratings > 0 ? 'Ativo' : 'Sem dados'}
                    </td>
                </tr>
            `).join('');
        }

        async function loadResponseTimeData() {
            try {
                const params = new URLSearchParams({
                    start_date: document.getElementById('start_date').value,
                    end_date: document.getElementById('end_date').value
                });

                const agentId = document.getElementById('agent_filter').value;
                if (agentId) {
                    params.append('agent_id', agentId);
                }

                const response = await fetch(`${API_BASE_URL}/response-time?${params}`);
                const result = await response.json();

                if (result.success) {
                    updateResponseTimeMetrics(result.data.summary || {});
                    createResponseTimeCharts(result.data);
                }
            } catch (error) {
                console.error('Erro ao carregar dados de tempo de resposta:', error);
                updateResponseTimeMetrics({});
            }
        }

        function updateResponseTimeMetrics(summary) {
            document.getElementById('avg-response-minutes').textContent =
                summary.avg_response_minutes ? `${summary.avg_response_minutes}min` : '-';
            document.getElementById('sla-compliance').textContent =
                summary.sla_compliance ? `${summary.sla_compliance}%` : '-';
            document.getElementById('median-response').textContent =
                summary.median_response_minutes ? `${summary.median_response_minutes}min` : '-';
            document.getElementById('max-response').textContent =
                summary.max_response_minutes ? `${summary.max_response_minutes}min` : '-';
        }

        function createResponseTimeCharts(data) {
            // Gráfico de distribuição por faixas
            createResponseTimeDistributionChart();

            // Gráfico de SLA
            createSLAChart();
        }

        function createResponseTimeDistributionChart() {
            const ctx = document.getElementById('response-time-distribution-chart');
            if (!ctx) return;

            ctx.innerHTML = '';
            const canvas = document.createElement('canvas');
            ctx.appendChild(canvas);

            // Dados simulados para demonstração
            const labels = ['0-2min', '2-5min', '5-10min', '10-30min', '>30min'];
            const data = [40, 30, 20, 8, 2]; // Percentuais simulados

            if (charts.responseDistChart) {
                charts.responseDistChart.destroy();
            }

            charts.responseDistChart = new Chart(canvas, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Conversas (%)',
                        data: data,
                        backgroundColor: [
                            colors.success,
                            colors.info,
                            colors.warning,
                            colors.danger,
                            '#6c757d'
                        ],
                        borderRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            });
        }

        function createSLAChart() {
            const ctx = document.getElementById('sla-breakdown-chart');
            if (!ctx) return;

            ctx.innerHTML = '';
            const canvas = document.createElement('canvas');
            ctx.appendChild(canvas);

            if (charts.slaChart) {
                charts.slaChart.destroy();
            }

            charts.slaChart = new Chart(canvas, {
                type: 'doughnut',
                data: {
                    labels: ['Dentro do SLA', 'Fora do SLA'],
                    datasets: [{
                        data: [75, 25], // Dados simulados
                        backgroundColor: [colors.success, colors.danger],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        async function loadVolumeData() {
            try {
                const params = new URLSearchParams({
                    start_date: document.getElementById('start_date').value,
                    end_date: document.getElementById('end_date').value
                });

                const response = await fetch(`${API_BASE_URL}/volume?${params}`);
                const result = await response.json();

                if (result.success) {
                    updateVolumeMetrics(result.data.summary || {});
                    createVolumeCharts(result.data);
                }
            } catch (error) {
                console.error('Erro ao carregar dados de volume:', error);
                updateVolumeMetrics({});
            }
        }

        function updateVolumeMetrics(summary) {
            document.getElementById('total-messages').textContent = summary.total_messages || 0;
            document.getElementById('unique-contacts').textContent = summary.unique_contacts || 0;
            document.getElementById('daily-average').textContent =
                summary.daily_average ? summary.daily_average.toFixed(1) : '-';
            document.getElementById('peak-hour').textContent = summary.peak_hour || '-';
        }

        function createVolumeCharts(data) {
            createDailyVolumeChart();
            createHourlyVolumeChart();
        }

        function createDailyVolumeChart() {
            const ctx = document.getElementById('volume-daily-chart');
            if (!ctx) return;

            ctx.innerHTML = '';
            const canvas = document.createElement('canvas');
            ctx.appendChild(canvas);

            // Dados simulados para os últimos 7 dias
            const labels = [];
            const data = [];
            const today = new Date();

            for (let i = 6; i >= 0; i--) {
                const date = new Date(today);
                date.setDate(date.getDate() - i);
                labels.push(date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' }));
                data.push(Math.floor(Math.random() * 15) + 1);
            }

            if (charts.dailyVolumeChart) {
                charts.dailyVolumeChart.destroy();
            }

            charts.dailyVolumeChart = new Chart(canvas, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Conversas',
                        data: data,
                        borderColor: colors.primary,
                        backgroundColor: colors.primary + '20',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function createHourlyVolumeChart() {
            const ctx = document.getElementById('volume-hourly-chart');
            if (!ctx) return;

            ctx.innerHTML = '';
            const canvas = document.createElement('canvas');
            ctx.appendChild(canvas);

            // Dados simulados por hora (8h às 18h)
            const labels = ['8h', '9h', '10h', '11h', '12h', '13h', '14h', '15h', '16h', '17h', '18h'];
            const data = [2, 5, 8, 12, 15, 10, 14, 18, 16, 12, 8];

            if (charts.hourlyVolumeChart) {
                charts.hourlyVolumeChart.destroy();
            }

            charts.hourlyVolumeChart = new Chart(canvas, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Conversas',
                        data: data,
                        backgroundColor: colors.info,
                        borderRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        async function loadPerformanceData() {
            try {
                const params = new URLSearchParams({
                    start_date: document.getElementById('start_date').value,
                    end_date: document.getElementById('end_date').value
                });

                const response = await fetch(`${API_BASE_URL}/performance?${params}`);
                const result = await response.json();

                if (result.success) {
                    updatePerformanceMetrics(result.data.summary || {});
                    createPerformanceCharts(result.data);
                    updateTopPerformers(result.data.top_performers || []);
                }
            } catch (error) {
                console.error('Erro ao carregar dados de performance:', error);
                updatePerformanceMetrics({});
            }
        }

        function updatePerformanceMetrics(summary) {
            document.getElementById('total-agents').textContent = summary.total_agents || 0;
            document.getElementById('active-agents').textContent = summary.active_agents || 0;
            document.getElementById('avg-performance-score').textContent =
                summary.avg_performance_score ? summary.avg_performance_score.toFixed(1) : '-';
            document.getElementById('top-performer-score').textContent =
                summary.best_performer ? summary.best_performer.performance_score.toFixed(1) : '-';
        }

        function createPerformanceCharts(data) {
            createPerformanceRankingChart(data.agents || []);
        }

        function createPerformanceRankingChart(agents) {
            const ctx = document.getElementById('performance-ranking-chart');
            if (!ctx) return;

            ctx.innerHTML = '';
            const canvas = document.createElement('canvas');
            ctx.appendChild(canvas);

            if (agents.length === 0) {
                showNoDataMessage('performance-ranking-chart', 'Dados de performance não disponíveis');
                return;
            }

            const labels = agents.map(agent => agent.agent_name);
            const scores = agents.map(agent => agent.performance_score);

            if (charts.performanceChart) {
                charts.performanceChart.destroy();
            }

            charts.performanceChart = new Chart(canvas, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Score de Performance',
                        data: scores,
                        backgroundColor: colors.primary,
                        borderRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 10
                        }
                    }
                }
            });
        }

        function updateTopPerformers(performers) {
            const container = document.getElementById('top-performers-list');

            if (performers.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle me-2"></i>
                        Dados insuficientes para ranking
                    </div>
                `;
                return;
            }

            container.innerHTML = performers.map((performer, index) => `
                <div class="d-flex align-items-center mb-3 ${index < performers.length - 1 ? 'border-bottom pb-3' : ''}">
                    <div class="me-3">
                        <span class="badge bg-${index === 0 ? 'warning' : index === 1 ? 'secondary' : 'dark'} rounded-pill">
                            ${index + 1}º
                        </span>
                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-bold">${performer.agent_name}</div>
                        <small class="text-muted">Score: ${performer.performance_score.toFixed(1)}</small>
                    </div>
                    <div class="text-end">
                        <i class="fas fa-trophy text-${index === 0 ? 'warning' : index === 1 ? 'secondary' : 'dark'}"></i>
                    </div>
                </div>
            `).join('');
        }

        function showNoDataMessage(containerId, message) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `
                    <div class="d-flex flex-column align-items-center justify-content-center h-100 text-muted">
                        <i class="fas fa-chart-line fa-3x mb-3 opacity-50"></i>
                        <p class="mb-0">${message}</p>
                    </div>
                `;
            }
        }

        function showLoading(show) {
            const loadingElements = document.querySelectorAll('.loading');
            loadingElements.forEach(element => {
                element.style.display = show ? 'flex' : 'none';
            });
        }

        function showAlert(message, type = 'info') {
            // Remover alertas existentes
            const existingAlerts = document.querySelectorAll('.alert-custom');
            existingAlerts.forEach(alert => alert.remove());

            // Criar novo alerta
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show alert-custom`;
            alert.style.position = 'fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.style.minWidth = '300px';

            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alert);

            // Auto-remover após 5 segundos
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }

        // Event listeners para mudança de abas
        document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', function(event) {
                const targetId = event.target.getAttribute('data-bs-target');

                // Redimensionar gráficos quando a aba for mostrada
                setTimeout(() => {
                    Object.values(charts).forEach(chart => {
                        if (chart && typeof chart.resize === 'function') {
                            chart.resize();
                        }
                    });
                }, 100);
            });
        });

        // Função para atualizar o relógio
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleString('pt-BR', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            const clockElement = document.getElementById('current-time');
            if (clockElement) {
                clockElement.textContent = timeString;
            }
        }

        // Atualizar relógio a cada segundo
        setInterval(updateClock, 1000);
        updateClock(); // Atualizar imediatamente

        // Funcionalidade do botão de tema (placeholder)
        document.getElementById('themeToggle')?.addEventListener('click', function() {
            const icon = this.querySelector('i');
            const isDark = icon.classList.contains('fa-sun');

            if (isDark) {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
                // Aqui você pode adicionar lógica para tema claro
                showAlert('Tema claro ativado', 'info');
            } else {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
                // Aqui você pode adicionar lógica para tema escuro
                showAlert('Tema escuro ativado', 'info');
            }
        });

        // Adicionar efeitos hover nos links do menu
        document.querySelectorAll('.nav-menu-link').forEach(link => {
            link.addEventListener('mouseenter', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateY(-2px)';
                }
            });

            link.addEventListener('mouseleave', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateY(0)';
                }
            });
        });
    </script>
</body>
</html>
