{% extends "base.html" %}

{% block title %}Métricas de Timing - Sistema de Relatórios 3CX{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-5 text-primary">
                <i class="fas fa-stopwatch me-3"></i>
                Métricas de Timing
            </h1>
            <p class="lead text-muted">Análise detalhada dos tempos de atendimento e service level</p>
        </div>
    </div>

    <!-- Filtros -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-filter me-2"></i>
            Filtros e Ações
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label for="start_date" class="form-label">Data Inicial</label>
                    <input type="date" class="form-control" id="start_date">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">Data Final</label>
                    <input type="date" class="form-control" id="end_date">
                </div>
                <div class="col-md-3">
                    <label for="queues" class="form-label">Filas</label>
                    <input type="text" class="form-control" id="queues" placeholder="802,803" value="{{ available_queues|join(',') }}">
                </div>
                <div class="col-md-3 d-flex align-items-end gap-2">
                    <button class="btn btn-primary" onclick="loadTimingReport()">
                        <i class="fas fa-sync-alt me-2"></i>
                        Gerar
                    </button>
                    <button class="btn btn-success" onclick="generatePDF('timing')">
                        <i class="fas fa-file-pdf me-2"></i>
                        PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Métricas Principais -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-primary" id="asa-metric">-</div>
                    <div class="metric-label">ASA (s)</div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-info" id="aht-metric">-</div>
                    <div class="metric-label">AHT (s)</div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-warning" id="att-metric">-</div>
                    <div class="metric-label">ATT (s)</div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-secondary" id="acw-metric">-</div>
                    <div class="metric-label">ACW (s)</div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-success" id="service-level-metric">-</div>
                    <div class="metric-label">Service Level (%)</div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card metric-card">
                <div class="card-body">
                    <div class="metric-value text-danger" id="abandonment-rate-metric">-</div>
                    <div class="metric-label">Taxa Abandono (%)</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos -->
    <div class="row mb-4">
        <!-- Service Level Gauge -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Service Level
                </div>
                <div class="card-body">
                    <div id="service-level-gauge" class="chart-container">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Selecione um período para gerar o gauge
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Métricas de Timing -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-2"></i>
                    Comparativo de Métricas
                </div>
                <div class="card-body">
                    <div id="timing-metrics-chart" class="chart-container">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Selecione um período para gerar o gráfico
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Análise por Hora -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-clock me-2"></i>
                    Análise por Horário
                </div>
                <div class="card-body">
                    <div id="hourly-timing-chart" class="chart-container">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Selecione um período para gerar a análise horária
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabela por Fila -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-table me-2"></i>
                    Métricas por Fila
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="timing-by-queue-table">
                            <thead>
                                <tr>
                                    <th>Fila</th>
                                    <th>Nome</th>
                                    <th>ASA (s)</th>
                                    <th>AHT (s)</th>
                                    <th>Service Level (%)</th>
                                    <th>Taxa Abandono (%)</th>
                                    <th>Total Chamadas</th>
                                    <th>Status SLA</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="8" class="text-center text-muted">
                                        Selecione um período e clique em "Gerar" para visualizar os dados
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- SLA Compliance -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-shield-alt me-2"></i>
                    Análise de SLA e Recomendações
                </div>
                <div class="card-body">
                    <div id="sla-analysis">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Gere um relatório para visualizar a análise de SLA e recomendações.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Carrega relatório de timing
    async function loadTimingReport() {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;
        const queues = document.getElementById('queues').value;
        
        if (!startDate || !endDate) {
            alert('Por favor, selecione as datas de início e fim.');
            return;
        }
        
        // Carrega métricas principais
        await loadTimingMetrics();
        
        // Carrega gráficos
        await loadChart('service_level_gauge', 'service-level-gauge');
        await loadChart('timing', 'timing-metrics-chart');
        await loadChart('hourly', 'hourly-timing-chart');
        
        // Carrega tabela por fila
        await loadTimingByQueueTable();
        
        // Gera análise de SLA
        generateSLAAnalysis();
    }
    
    // Carrega métricas de timing
    async function loadTimingMetrics() {
        try {
            // Simula dados de timing
            const asa = (Math.random() * 20 + 10).toFixed(1);
            const aht = (Math.random() * 100 + 200).toFixed(1);
            const att = (aht * 0.85).toFixed(1);
            const acw = (aht - att).toFixed(1);
            const serviceLevel = (Math.random() * 20 + 75).toFixed(1);
            const abandonmentRate = (Math.random() * 8 + 2).toFixed(1);
            
            document.getElementById('asa-metric').textContent = asa + 's';
            document.getElementById('aht-metric').textContent = aht + 's';
            document.getElementById('att-metric').textContent = att + 's';
            document.getElementById('acw-metric').textContent = acw + 's';
            document.getElementById('service-level-metric').textContent = serviceLevel + '%';
            document.getElementById('abandonment-rate-metric').textContent = abandonmentRate + '%';
            
        } catch (error) {
            console.error('Erro ao carregar métricas de timing:', error);
        }
    }
    
    // Carrega tabela de timing por fila
    async function loadTimingByQueueTable() {
        const queues = document.getElementById('queues').value.split(',');
        
        let tableHtml = '';
        
        queues.forEach(queue => {
            const queueId = queue.trim();
            const asa = (Math.random() * 20 + 10).toFixed(1);
            const aht = (Math.random() * 100 + 200).toFixed(1);
            const serviceLevel = (Math.random() * 20 + 75).toFixed(1);
            const abandonmentRate = (Math.random() * 8 + 2).toFixed(1);
            const totalCalls = Math.floor(Math.random() * 200) + 50;
            
            const slaStatus = serviceLevel >= 80 ? 'COMPLIANT' : 'NON_COMPLIANT';
            const slaClass = serviceLevel >= 80 ? 'success' : 'danger';
            
            tableHtml += `
                <tr>
                    <td><span class="badge bg-primary">${queueId}</span></td>
                    <td>Fila ${queueId}</td>
                    <td>${asa}s</td>
                    <td>${aht}s</td>
                    <td>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar bg-${serviceLevel >= 80 ? 'success' : 'warning'}" style="width: ${serviceLevel}%">
                                ${serviceLevel}%
                            </div>
                        </div>
                    </td>
                    <td><span class="text-${abandonmentRate <= 5 ? 'success' : 'danger'}">${abandonmentRate}%</span></td>
                    <td>${totalCalls.toLocaleString()}</td>
                    <td><span class="badge bg-${slaClass}">${slaStatus}</span></td>
                </tr>
            `;
        });
        
        document.querySelector('#timing-by-queue-table tbody').innerHTML = tableHtml;
    }
    
    // Gera análise de SLA
    function generateSLAAnalysis() {
        const slaTarget = 80;
        const currentSL = parseFloat(document.getElementById('service-level-metric').textContent);
        const currentASA = parseFloat(document.getElementById('asa-metric').textContent);
        
        let analysisHtml = '';
        
        // Status geral do SLA
        if (currentSL >= slaTarget) {
            analysisHtml += `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>SLA Compliance: ATENDIDO</strong><br>
                    Service Level atual (${currentSL}%) está acima da meta (${slaTarget}%)
                </div>
            `;
        } else {
            analysisHtml += `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>SLA Compliance: NÃO ATENDIDO</strong><br>
                    Service Level atual (${currentSL}%) está abaixo da meta (${slaTarget}%)
                </div>
            `;
        }
        
        // Análise do ASA
        if (currentASA <= 20) {
            analysisHtml += `
                <div class="alert alert-success">
                    <i class="fas fa-thumbs-up me-2"></i>
                    <strong>ASA Excelente:</strong> Tempo médio de atendimento (${currentASA}s) está dentro do ideal (≤20s)
                </div>
            `;
        } else {
            analysisHtml += `
                <div class="alert alert-warning">
                    <i class="fas fa-clock me-2"></i>
                    <strong>ASA Elevado:</strong> Tempo médio de atendimento (${currentASA}s) pode ser melhorado
                </div>
            `;
        }
        
        // Recomendações
        analysisHtml += `
            <div class="alert alert-info">
                <i class="fas fa-lightbulb me-2"></i>
                <strong>Recomendações:</strong>
                <ul class="mb-0 mt-2">
                    <li>Monitorar horários de pico para otimizar staffing</li>
                    <li>Implementar callback para reduzir tempo de espera</li>
                    <li>Treinar agentes para reduzir AHT mantendo qualidade</li>
                    <li>Analisar distribuição de chamadas entre filas</li>
                </ul>
            </div>
        `;
        
        document.getElementById('sla-analysis').innerHTML = analysisHtml;
    }
    
    // Inicialização
    document.addEventListener('DOMContentLoaded', function() {
        // Define datas padrão (últimos 7 dias)
        const today = new Date();
        const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        
        document.getElementById('end_date').value = today.toISOString().split('T')[0];
        document.getElementById('start_date').value = lastWeek.toISOString().split('T')[0];
    });
</script>
{% endblock %}
