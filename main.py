"""
Aplicação principal do Sistema de Relatórios 3CX
"""
from fastapi import FastAPI, Request, Form, Query
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from datetime import date, datetime, timedelta
import tempfile
import os
import logging

from config import settings
from src.api.reports import router as reports_router
from src.api.chatwoot import router as chatwoot_router
from src.clients.threecx_client import threecx_client
from src.clients.chatwoot_client import chatwoot_client
from src.services.metrics_calculator import metrics_calculator
from src.services.chart_generator import chart_generator
from src.services.pdf_generator import pdf_generator
from src.services.chatwoot_metrics import chatwoot_metrics_service

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Criação da aplicação FastAPI
app = FastAPI(
    title="Sistema de Relatórios 3CX - Amvox",
    description="Sistema completo de análise de métricas de pós-venda",
    version="2.1.0"
)

# Configuração de templates e arquivos estáticos
templates = Jinja2Templates(directory="templates")

# Cria diretórios se não existirem
os.makedirs("static", exist_ok=True)
os.makedirs("templates", exist_ok=True)
os.makedirs("temp", exist_ok=True)

app.mount("/static", StaticFiles(directory="static"), name="static")

# Inclui routers da API
app.include_router(reports_router)
app.include_router(chatwoot_router)


@app.on_event("startup")
async def startup_event():
    """Eventos de inicialização da aplicação"""
    try:
        # Conectar ao Chatwoot
        await chatwoot_client.connect()
        logger.info("Conexão com Chatwoot estabelecida")
    except Exception as e:
        logger.error(f"Erro ao conectar com Chatwoot: {e}")


@app.on_event("shutdown")
async def shutdown_event():
    """Eventos de finalização da aplicação"""
    try:
        # Desconectar do Chatwoot
        await chatwoot_client.disconnect()
        logger.info("Conexão com Chatwoot fechada")
    except Exception as e:
        logger.error(f"Erro ao desconectar do Chatwoot: {e}")


@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """Página inicial do sistema"""
    return templates.TemplateResponse("index.html", {
        "request": request,
        "title": "Sistema de Relatórios - Amvox",
        "available_queues": settings.available_queues_list
    })


@app.get("/reports/distribution", response_class=HTMLResponse)
async def distribution_page(request: Request):
    """Página de relatório de distribuição"""
    return templates.TemplateResponse("distribution.html", {
        "request": request,
        "title": "Relatório de Distribuição",
        "available_queues": settings.available_queues_list
    })


@app.get("/reports/satisfaction", response_class=HTMLResponse)
async def satisfaction_page(request: Request):
    """Página de relatório de satisfação"""
    return templates.TemplateResponse("satisfaction.html", {
        "request": request,
        "title": "Pesquisa de Satisfação",
        "available_queues": settings.available_queues_list
    })


@app.get("/reports/timing", response_class=HTMLResponse)
async def timing_page(request: Request):
    """Página de métricas de timing"""
    return templates.TemplateResponse("timing.html", {
        "request": request,
        "title": "Métricas de Timing",
        "available_queues": settings.available_queues_list
    })


@app.get("/reports/agents", response_class=HTMLResponse)
async def agents_page(request: Request):
    """Página de performance de agentes"""
    return templates.TemplateResponse("agents.html", {
        "request": request,
        "title": "Performance de Agentes",
        "available_queues": settings.available_queues_list
    })


@app.get("/reports/comparative", response_class=HTMLResponse)
async def comparative_page(request: Request):
    """Página de relatório comparativo"""
    return templates.TemplateResponse("comparative.html", {
        "request": request,
        "title": "Análise Comparativa",
        "available_queues": settings.available_queues_list
    })


@app.post("/generate-pdf/{report_type}")
async def generate_pdf_report(
    report_type: str,
    start_date: date = Form(...),
    end_date: date = Form(...),
    queues: str = Form(None)
):
    """Gera relatório em PDF"""
    try:
        # Processa lista de filas
        if queues:
            queue_list = [int(q.strip()) for q in queues.split(",")]
        else:
            queue_list = settings.available_queues_list
        
        # Obtém dados baseado no tipo de relatório
        if report_type == "distribution":
            data = await threecx_client.get_distribution_report(start_date, end_date, queue_list)
            data.update({"period_start": start_date, "period_end": end_date})
        elif report_type == "satisfaction":
            data = await threecx_client.get_satisfaction_survey(start_date, end_date, queue_list)
            satisfaction_metrics = metrics_calculator.calculate_satisfaction_metrics(data)
            data = {
                "period_start": start_date,
                "period_end": end_date,
                "metrics": satisfaction_metrics.dict(),
                "comments": ["Exemplo de comentário 1", "Exemplo de comentário 2"]
            }
        elif report_type == "timing":
            data = await threecx_client.get_timing_metrics(start_date, end_date, queue_list)
            timing_metrics = metrics_calculator.calculate_timing_metrics(data)
            data = {
                "period_start": start_date,
                "period_end": end_date,
                "overall_metrics": timing_metrics.dict()
            }
        elif report_type == "agents":
            data = await threecx_client.get_agent_performance(start_date, end_date, queue_list)
            agents_performance = metrics_calculator.calculate_agent_performance(data)
            data = {
                "period_start": start_date,
                "period_end": end_date,
                "agents": [agent.dict() for agent in agents_performance],
                "team_averages": {},
                "top_performers": [agent.agent_name for agent in agents_performance[:3]],
                "improvement_opportunities": ["Exemplo de oportunidade"]
            }
        else:
            raise ValueError(f"Tipo de relatório inválido: {report_type}")
        
        # Gera arquivo PDF temporário
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
        temp_path = temp_file.name
        temp_file.close()
        
        # Gera PDF baseado no tipo
        if report_type == "distribution":
            pdf_path = pdf_generator.generate_distribution_report(data, temp_path)
        elif report_type == "satisfaction":
            pdf_path = pdf_generator.generate_satisfaction_report(data, temp_path)
        elif report_type == "timing":
            pdf_path = pdf_generator.generate_timing_report(data, temp_path)
        elif report_type == "agents":
            pdf_path = pdf_generator.generate_agent_report(data, temp_path)
        
        # Retorna arquivo PDF
        filename = f"relatorio_{report_type}_{start_date}_{end_date}.pdf"
        return FileResponse(
            pdf_path,
            media_type="application/pdf",
            filename=filename,
            background=lambda: os.unlink(pdf_path)  # Remove arquivo após download
        )
    
    except Exception as e:
        logger.error(f"Erro ao gerar PDF: {str(e)}")
        return {"error": f"Erro ao gerar PDF: {str(e)}"}


@app.get("/api/charts/{chart_type}")
async def get_chart(
    chart_type: str,
    start_date: date = Query(...),
    end_date: date = Query(...),
    queues: str = Query(None)
):
    """Retorna gráfico em HTML"""
    try:
        # Processa lista de filas
        if queues:
            queue_list = [int(q.strip()) for q in queues.split(",")]
        else:
            queue_list = settings.available_queues_list
        
        # Obtém dados baseado no tipo de gráfico
        if chart_type == "distribution":
            data = await threecx_client.get_distribution_report(start_date, end_date, queue_list)
            chart_html = chart_generator.create_distribution_chart(data)
        elif chart_type == "timing":
            data = await threecx_client.get_timing_metrics(start_date, end_date, queue_list)
            chart_html = chart_generator.create_timing_metrics_chart({"overall_metrics": metrics_calculator.calculate_timing_metrics(data).dict()})
        elif chart_type == "satisfaction":
            data = await threecx_client.get_satisfaction_survey(start_date, end_date, queue_list)
            satisfaction_metrics = metrics_calculator.calculate_satisfaction_metrics(data)
            chart_html = chart_generator.create_satisfaction_chart({"metrics": satisfaction_metrics.dict()})
        elif chart_type == "agents":
            data = await threecx_client.get_agent_performance(start_date, end_date, queue_list)
            agents_performance = metrics_calculator.calculate_agent_performance(data)
            chart_html = chart_generator.create_agent_performance_chart({"agents": [agent.dict() for agent in agents_performance]})
        elif chart_type == "service_level_gauge":
            data = await threecx_client.get_timing_metrics(start_date, end_date, queue_list)
            timing_metrics = metrics_calculator.calculate_timing_metrics(data)
            chart_html = chart_generator.create_service_level_gauge(timing_metrics.service_level)
        elif chart_type == "hourly":
            data = await threecx_client.get_distribution_report(start_date, end_date, queue_list)
            # Simula dados por hora
            hourly_data = {
                f"{hour:02d}:00": {"calls": 20 + (hour % 5) * 10, "service_level": 85 + (hour % 4) * 3}
                for hour in range(8, 18)
            }
            chart_html = chart_generator.create_hourly_distribution_chart(hourly_data)
        else:
            return {"error": f"Tipo de gráfico inválido: {chart_type}"}
        
        return HTMLResponse(content=chart_html)
    
    except Exception as e:
        logger.error(f"Erro ao gerar gráfico: {str(e)}")
        return {"error": f"Erro ao gerar gráfico: {str(e)}"}


# Rotas do Chatwoot
@app.get("/chatwoot", response_class=HTMLResponse)
async def chatwoot_dashboard(request: Request):
    """Dashboard principal do Chatwoot"""
    return templates.TemplateResponse("chatwoot_dashboard.html", {
        "request": request,
        "title": "Dashboard Chatwoot - Métricas WhatsApp"
    })


@app.get("/chatwoot/satisfaction", response_class=HTMLResponse)
async def chatwoot_satisfaction(request: Request):
    """Relatório de satisfação do Chatwoot"""
    return templates.TemplateResponse("chatwoot_satisfaction.html", {
        "request": request,
        "title": "Relatório de Satisfação - Chatwoot"
    })


@app.get("/chatwoot/response-time", response_class=HTMLResponse)
async def chatwoot_response_time(request: Request):
    """Relatório de tempo de resposta do Chatwoot"""
    return templates.TemplateResponse("chatwoot_response_time.html", {
        "request": request,
        "title": "Tempo de Resposta - Chatwoot"
    })


@app.get("/chatwoot/volume", response_class=HTMLResponse)
async def chatwoot_volume(request: Request):
    """Relatório de volume de conversas do Chatwoot"""
    return templates.TemplateResponse("chatwoot_volume.html", {
        "request": request,
        "title": "Volume de Conversas - Chatwoot"
    })


@app.get("/chatwoot/performance", response_class=HTMLResponse)
async def chatwoot_performance(request: Request):
    """Relatório de performance geral do Chatwoot"""
    return templates.TemplateResponse("chatwoot_performance.html", {
        "request": request,
        "title": "Performance Geral - Chatwoot"
    })


# Rotas para geração de PDF do Chatwoot
@app.post("/generate-pdf/chatwoot-satisfaction")
async def generate_chatwoot_satisfaction_pdf(
    start_date: date = Form(...),
    end_date: date = Form(...)
):
    """Gera PDF do relatório de satisfação do Chatwoot"""
    try:
        # Buscar dados de satisfação
        satisfaction_data = await chatwoot_metrics_service.get_satisfaction_report(
            start_date, end_date
        )

        # Gerar PDF
        pdf_content = await pdf_generator.generate_chatwoot_satisfaction_pdf(
            satisfaction_data, start_date, end_date
        )

        # Salvar arquivo temporário
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
            tmp_file.write(pdf_content)
            tmp_file_path = tmp_file.name

        # Retornar arquivo
        return FileResponse(
            tmp_file_path,
            media_type="application/pdf",
            filename=f"satisfacao_chatwoot_{start_date}_{end_date}.pdf",
            background=lambda: os.unlink(tmp_file_path)
        )

    except Exception as e:
        logger.error(f"Erro ao gerar PDF de satisfação: {str(e)}")
        return {"error": f"Erro ao gerar PDF: {str(e)}"}


@app.post("/generate-pdf/chatwoot-response-time")
async def generate_chatwoot_response_time_pdf(
    start_date: date = Form(...),
    end_date: date = Form(...)
):
    """Gera PDF do relatório de tempo de resposta do Chatwoot"""
    try:
        # Buscar dados de tempo de resposta
        response_time_data = await chatwoot_metrics_service.get_response_time_report(
            start_date, end_date
        )

        # Gerar PDF
        pdf_content = await pdf_generator.generate_chatwoot_response_time_pdf(
            response_time_data, start_date, end_date
        )

        # Salvar arquivo temporário
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
            tmp_file.write(pdf_content)
            tmp_file_path = tmp_file.name

        # Retornar arquivo
        return FileResponse(
            tmp_file_path,
            media_type="application/pdf",
            filename=f"tempo_resposta_chatwoot_{start_date}_{end_date}.pdf",
            background=lambda: os.unlink(tmp_file_path)
        )

    except Exception as e:
        logger.error(f"Erro ao gerar PDF de tempo de resposta: {str(e)}")
        return {"error": f"Erro ao gerar PDF: {str(e)}"}


@app.post("/generate-pdf/chatwoot-volume")
async def generate_chatwoot_volume_pdf(
    start_date: date = Form(...),
    end_date: date = Form(...)
):
    """Gera PDF do relatório de volume do Chatwoot"""
    try:
        # Buscar dados de volume
        volume_data = await chatwoot_metrics_service.get_volume_report(
            start_date, end_date
        )

        # Gerar PDF
        pdf_content = await pdf_generator.generate_chatwoot_volume_pdf(
            volume_data, start_date, end_date
        )

        # Salvar arquivo temporário
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
            tmp_file.write(pdf_content)
            tmp_file_path = tmp_file.name

        # Retornar arquivo
        return FileResponse(
            tmp_file_path,
            media_type="application/pdf",
            filename=f"volume_chatwoot_{start_date}_{end_date}.pdf",
            background=lambda: os.unlink(tmp_file_path)
        )

    except Exception as e:
        logger.error(f"Erro ao gerar PDF de volume: {str(e)}")
        return {"error": f"Erro ao gerar PDF: {str(e)}"}


@app.post("/generate-pdf/chatwoot-performance")
async def generate_chatwoot_performance_pdf(
    start_date: date = Form(...),
    end_date: date = Form(...)
):
    """Gera PDF do relatório de performance do Chatwoot"""
    try:
        # Buscar dados de performance (reutilizar lógica da API)
        satisfaction_data = await chatwoot_metrics_service.get_satisfaction_report(
            start_date, end_date
        )
        response_time_data = await chatwoot_metrics_service.get_response_time_report(
            start_date, end_date
        )

        # Gerar PDF
        pdf_content = await pdf_generator.generate_chatwoot_performance_pdf(
            satisfaction_data, response_time_data, start_date, end_date
        )

        # Salvar arquivo temporário
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
            tmp_file.write(pdf_content)
            tmp_file_path = tmp_file.name

        # Retornar arquivo
        return FileResponse(
            tmp_file_path,
            media_type="application/pdf",
            filename=f"performance_chatwoot_{start_date}_{end_date}.pdf",
            background=lambda: os.unlink(tmp_file_path)
        )

    except Exception as e:
        logger.error(f"Erro ao gerar PDF de performance: {str(e)}")
        return {"error": f"Erro ao gerar PDF: {str(e)}"}


@app.get("/health")
async def health_check():
    """Endpoint de health check"""
    try:
        # Testa conexão com 3CX
        is_connected_3cx = await threecx_client.test_connection()

        # Testa conexão com Chatwoot
        is_connected_chatwoot = await chatwoot_client.test_connection()

        return {
            "status": "healthy" if (is_connected_3cx and is_connected_chatwoot) else "degraded",
            "timestamp": datetime.now().isoformat(),
            "3cx_connection": is_connected_3cx,
            "chatwoot_connection": is_connected_chatwoot,
            "version": "2.1.0"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "version": "2.1.0"
        }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.app_host,
        port=settings.app_port,
        reload=settings.debug
    )
