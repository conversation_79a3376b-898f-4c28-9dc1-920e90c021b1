#!/usr/bin/env python3
"""
Script de teste para o serviço de métricas do Chatwoot
"""
import asyncio
import sys
import os
from datetime import date, timedelta

# Adicionar o diretório raiz ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import settings
from src.clients.chatwoot_client import chatwoot_client
from src.services.chatwoot_metrics import chatwoot_metrics_service


async def test_connection():
    """Testa a conexão com o banco Chatwoot"""
    print("🔍 Testando conexão com Chatwoot...")
    
    try:
        await chatwoot_client.connect()
        is_connected = await chatwoot_client.test_connection()
        
        if is_connected:
            print("✅ Conexão com Chatwoot estabelecida com sucesso!")
            return True
        else:
            print("❌ Falha na conexão com Chatwoot")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao conectar com Chatwoot: {e}")
        return False


async def test_basic_queries():
    """Testa queries básicas"""
    print("\n🔍 Testando queries básicas...")
    
    try:
        # Testar lista de agentes
        agents = await chatwoot_client.get_agents_list()
        print(f"✅ Encontrados {len(agents)} agentes")
        
        if agents:
            print("   Agentes encontrados:")
            for agent in agents[:3]:  # Mostrar apenas os 3 primeiros
                print(f"   - {agent['name']} (ID: {agent['id']})")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro nas queries básicas: {e}")
        return False


async def test_metrics_queries():
    """Testa queries de métricas"""
    print("\n🔍 Testando queries de métricas...")
    
    # Definir período de teste (últimos 30 dias)
    end_date = date.today()
    start_date = end_date - timedelta(days=30)
    
    try:
        # Testar métricas de satisfação
        print("   Testando métricas de satisfação...")
        satisfaction_data = await chatwoot_client.get_satisfaction_metrics(start_date, end_date)
        print(f"   ✅ Encontrados dados de satisfação para {len(satisfaction_data)} agentes")
        
        # Testar métricas de tempo de resposta
        print("   Testando métricas de tempo de resposta...")
        response_time_data = await chatwoot_client.get_response_time_metrics(start_date, end_date)
        print(f"   ✅ Encontrados dados de tempo de resposta para {len(response_time_data)} agentes")
        
        # Testar volume de conversas
        print("   Testando volume de conversas...")
        volume_data = await chatwoot_client.get_conversation_volume(start_date, end_date)
        print(f"   ✅ Encontrados dados de volume para {len(volume_data)} dias")
        
        # Testar dashboard summary
        print("   Testando resumo do dashboard...")
        dashboard_data = await chatwoot_client.get_dashboard_summary(start_date, end_date)
        print(f"   ✅ Dados do dashboard carregados com sucesso")
        print(f"       Total de conversas: {dashboard_data['conversations']['total']}")
        print(f"       Taxa de satisfação: {dashboard_data['satisfaction']['satisfaction_rate']}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro nas queries de métricas: {e}")
        return False


async def test_service_layer():
    """Testa a camada de serviços"""
    print("\n🔍 Testando camada de serviços...")
    
    # Definir período de teste
    end_date = date.today()
    start_date = end_date - timedelta(days=7)
    
    try:
        # Testar relatório de satisfação
        print("   Testando relatório de satisfação...")
        satisfaction_report = await chatwoot_metrics_service.get_satisfaction_report(start_date, end_date)
        print(f"   ✅ Relatório de satisfação gerado")
        print(f"       Agentes: {satisfaction_report['summary']['total_agents']}")
        print(f"       Taxa média: {satisfaction_report['summary']['satisfaction_rate']}%")
        
        # Testar relatório de tempo de resposta
        print("   Testando relatório de tempo de resposta...")
        response_report = await chatwoot_metrics_service.get_response_time_report(start_date, end_date)
        print(f"   ✅ Relatório de tempo de resposta gerado")
        print(f"       Agentes: {response_report['summary']['total_agents']}")
        print(f"       Tempo médio: {response_report['summary']['average_response_time']} min")
        
        # Testar dados do dashboard
        print("   Testando dados do dashboard...")
        dashboard_data = await chatwoot_metrics_service.get_dashboard_data(start_date, end_date)
        print(f"   ✅ Dados do dashboard gerados com sucesso")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na camada de serviços: {e}")
        return False


async def test_configuration():
    """Testa as configurações"""
    print("\n🔍 Testando configurações...")
    
    try:
        print(f"   Host: {settings.chatwoot_db_host}")
        print(f"   Porta: {settings.chatwoot_db_port}")
        print(f"   Banco: {settings.chatwoot_db_name}")
        print(f"   Usuário: {settings.chatwoot_db_user}")
        print(f"   Account ID: {settings.chatwoot_account_id}")
        print(f"   URL de conexão: {settings.chatwoot_database_url}")
        
        print("✅ Configurações carregadas com sucesso")
        return True
        
    except Exception as e:
        print(f"❌ Erro nas configurações: {e}")
        return False


async def cleanup():
    """Limpa recursos"""
    try:
        await chatwoot_client.disconnect()
        print("\n🧹 Recursos limpos com sucesso")
    except Exception as e:
        print(f"\n⚠️  Erro na limpeza: {e}")


async def main():
    """Função principal de teste"""
    print("🚀 Iniciando testes do serviço Chatwoot...")
    print("=" * 50)
    
    # Lista de testes
    tests = [
        ("Configurações", test_configuration),
        ("Conexão", test_connection),
        ("Queries Básicas", test_basic_queries),
        ("Queries de Métricas", test_metrics_queries),
        ("Camada de Serviços", test_service_layer),
    ]
    
    results = []
    
    # Executar testes
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erro no teste {test_name}: {e}")
            results.append((test_name, False))
    
    # Limpar recursos
    await cleanup()
    
    # Resumo dos resultados
    print("\n" + "=" * 50)
    print("📊 RESUMO DOS TESTES")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSOU" if result else "❌ FALHOU"
        print(f"{test_name:.<30} {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"Total: {passed}/{total} testes passaram")
    
    if passed == total:
        print("🎉 Todos os testes passaram! O serviço está funcionando corretamente.")
        return 0
    else:
        print("⚠️  Alguns testes falharam. Verifique as configurações e conexões.")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️  Testes interrompidos pelo usuário")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 Erro fatal: {e}")
        sys.exit(1)
