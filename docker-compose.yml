version: '3.8'

services:
  amvox-reports:
    build: .
    container_name: amvox_reports_3cx
    ports:
      - "8000:8000"
    environment:
      # Configurações do 3CX
      - THREECX_HOST=${THREECX_HOST:-ticmobilerb.ddns.net}
      - THREECX_PORT=${THREECX_PORT:-443}
      - THREECX_USERNAME=${THREECX_USERNAME:-admin}
      - THREECX_PASSWORD=${THREECX_PASSWORD:-Tic@2024}
      - THREECX_SECURE=${THREECX_SECURE:-true}

      # Configurações do Servidor
      - HOST=${HOST:-0.0.0.0}
      - PORT=${PORT:-8000}
      - DEBUG=${DEBUG:-false}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}

      # Configurações de Cache
      - CACHE_TTL=${CACHE_TTL:-300}
      - MAX_CACHE_SIZE=${MAX_CACHE_SIZE:-100}

      # Configurações de Timeout
      - REQUEST_TIMEOUT=${REQUEST_TIMEOUT:-30}
      - CONNECTION_TIMEOUT=${CONNECTION_TIMEOUT:-10}

      # Configurações de Retry
      - MAX_RETRIES=${MAX_RETRIES:-3}
      - RETRY_DELAY=${RETRY_DELAY:-1}

      # Filas Disponíveis
      - AVAILABLE_QUEUES=${AVAILABLE_QUEUES:-802,803}

      # Configurações do Chatwoot (Opcional)
      - CHATWOOT_DB_HOST=${CHATWOOT_DB_HOST:-n8n_chatwoot_postgres}
      - CHATWOOT_DB_PORT=${CHATWOOT_DB_PORT:-5432}
      - CHATWOOT_DB_NAME=${CHATWOOT_DB_NAME:-chatwoot}
      - CHATWOOT_DB_USER=${CHATWOOT_DB_USER:-chatwoot}
      - CHATWOOT_DB_PASSWORD=${CHATWOOT_DB_PASSWORD:-chatwoot_password_123}
      - CHATWOOT_ACCOUNT_ID=${CHATWOOT_ACCOUNT_ID:-1}
    networks:
      - default
      - n8n_net
    volumes:
      - ./main_simple.py:/app/main_simple.py
      - ./src:/app/src
      - ./temp:/app/temp
      - ./logs:/app/logs
      - ./static:/app/static
    restart: unless-stopped
    depends_on:
      - amvox-data

  amvox-data:
    image: busybox
    volumes:
      - amvox-data:/data

volumes:
  amvox-data:
    driver: local

networks:
  n8n_net:
    external: true
    name: n8n-host_n8n_net
