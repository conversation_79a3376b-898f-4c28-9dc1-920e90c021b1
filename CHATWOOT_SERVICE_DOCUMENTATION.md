# 📊 Serviço de Métricas Chatwoot - Documentação

## 📋 **Visão Geral**

O novo serviço de métricas do Chatwoot foi integrado ao sistema de relatórios, permitindo análise completa das métricas de atendimento via WhatsApp. O serviço conecta diretamente ao banco de dados do Chatwoot e oferece dashboards interativos, relatórios detalhados e APIs para exportação de dados.

---

## 🚀 **Funcionalidades Implementadas**

### **1. Dashboard Principal**
- **URL**: `/chatwoot`
- **Descrição**: Visão geral das métricas principais
- **Recursos**:
  - Cards com métricas resumidas (satisfação, tempo de resposta, volume, agentes)
  - Gráficos de tendência de volume (7 dias)
  - Distribuição de satisfação em pizza
  - Filtros de data com opções rápidas (hoje, 7 dias, 30 dias)
  - Links para relatórios detalhados

### **2. Relatório de Satisfação**
- **URL**: `/chatwoot/satisfaction`
- **Descrição**: Análise detalhada das avaliações de clientes
- **Recursos**:
  - Resumo com total de avaliações, média e taxa de satisfação
  - Gráfico de barras por agente
  - Gráfico de pizza com distribuição (positivas/neutras/negativas)
  - Tabela detalhada por agente com badges coloridos
  - Filtro por agente específico
  - Exportação de dados em JSON

### **3. Relatório de Tempo de Resposta**
- **URL**: `/chatwoot/response-time`
- **Descrição**: Análise dos tempos de primeira resposta
- **Recursos**:
  - Resumo com total de conversas, tempo médio e melhor agente
  - Gráfico de barras com tempo por agente
  - Tabela com detalhes (mínimo, máximo, média)
  - Badges coloridos por performance
  - Filtro por agente específico

### **4. Relatório de Volume**
- **URL**: `/chatwoot/volume`
- **Descrição**: Análise do volume de conversas
- **Recursos**:
  - Resumo com total, média diária e dia de pico
  - Gráfico de linha com tendência diária
  - Gráfico de barras empilhadas por status (resolvidas/pendentes/abertas)
  - Identificação automática de picos de volume

### **5. Relatório de Performance Geral**
- **URL**: `/chatwoot/performance`
- **Descrição**: Visão consolidada de performance por agente
- **Recursos**:
  - Resumo geral com médias consolidadas
  - Cards por agente com métricas combinadas
  - Classificação visual por performance (excelente/boa/média/ruim)
  - Filtro por agente específico

---

## 🔧 **APIs Disponíveis**

### **Endpoints Principais**

#### **1. Health Check**
```
GET /api/v1/chatwoot/health
```
Verifica status da conexão com o banco Chatwoot.

#### **2. Dashboard**
```
GET /api/v1/chatwoot/dashboard?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD
```
Retorna dados resumidos para o dashboard.

#### **3. Satisfação**
```
GET /api/v1/chatwoot/satisfaction?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD&agent_id=ID
```
Retorna métricas de satisfação. `agent_id` é opcional.

#### **4. Tempo de Resposta**
```
GET /api/v1/chatwoot/response-time?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD&agent_id=ID
```
Retorna métricas de tempo de resposta. `agent_id` é opcional.

#### **5. Volume**
```
GET /api/v1/chatwoot/volume?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD
```
Retorna métricas de volume de conversas.

#### **6. Performance**
```
GET /api/v1/chatwoot/performance?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD&agent_id=ID
```
Retorna relatório consolidado de performance. `agent_id` é opcional.

#### **7. Lista de Agentes**
```
GET /api/v1/chatwoot/agents
```
Retorna lista de agentes disponíveis.

#### **8. Exportação**
```
GET /api/v1/chatwoot/export/satisfaction?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD&format=json
```
Exporta dados de satisfação. Formato CSV será implementado futuramente.

---

## ⚙️ **Configuração**

### **Variáveis de Ambiente**

Adicione as seguintes configurações no arquivo `.env` ou configure diretamente:

```env
# Configurações do Banco Chatwoot
CHATWOOT_DB_HOST=localhost
CHATWOOT_DB_PORT=3024
CHATWOOT_DB_NAME=chatwoot
CHATWOOT_DB_USER=chatwoot
CHATWOOT_DB_PASSWORD=chatwoot123
CHATWOOT_ACCOUNT_ID=1
```

### **Dependências**

O serviço requer a biblioteca `asyncpg` para conexão PostgreSQL:

```bash
pip install asyncpg>=0.28.0
```

---

## 🎨 **Interface do Usuário**

### **Menu de Navegação**

O menu lateral foi expandido com uma nova seção "CHATWOOT" contendo:

- **Dashboard WhatsApp**: Visão geral das métricas
- **Satisfação WhatsApp**: Relatório de avaliações
- **Tempo de Resposta**: Análise de tempos de resposta
- **Volume WhatsApp**: Análise de volume de conversas
- **Performance WhatsApp**: Relatório consolidado

### **Design Responsivo**

- Interface adaptável para desktop, tablet e mobile
- Cards com gradientes coloridos para cada tipo de métrica
- Gráficos interativos usando Plotly.js
- Filtros intuitivos com opções rápidas de data
- Loading states e tratamento de erros

### **Cores e Temas**

- **Satisfação**: Verde (sucesso)
- **Tempo de Resposta**: Azul (informação)
- **Volume**: Rosa/Roxo (destaque)
- **Performance**: Azul claro (profissional)

---

## 📊 **Métricas Calculadas**

### **Satisfação**
- **Taxa de Satisfação**: % de avaliações 4-5 estrelas
- **Avaliação Média**: Média ponderada das notas
- **Distribuição**: Contagem por categoria (positiva/neutra/negativa)

### **Tempo de Resposta**
- **Tempo Médio**: Média do tempo de primeira resposta em minutos
- **Tempo Mínimo/Máximo**: Extremos por agente
- **Performance**: Classificação baseada em thresholds

### **Volume**
- **Total de Conversas**: Soma no período
- **Média Diária**: Volume médio por dia
- **Distribuição por Status**: Abertas/Pendentes/Resolvidas
- **Dia de Pico**: Dia com maior volume

### **Performance**
- **Score Consolidado**: Combinação de satisfação e tempo de resposta
- **Classificação**: Excelente/Boa/Média/Ruim
- **Ranking**: Ordenação por performance geral

---

## 🔒 **Segurança e Validação**

### **Validações Implementadas**
- Validação de intervalos de data (máximo 1 ano)
- Sanitização de parâmetros de entrada
- Tratamento de erros de conexão
- Rate limiting (configurável)

### **Conexão Segura**
- Pool de conexões para otimização
- Timeout configurável
- Reconexão automática em caso de falha
- Logs detalhados para monitoramento

---

## 🚦 **Status e Monitoramento**

### **Health Check**
O endpoint `/api/v1/chatwoot/health` verifica:
- Conectividade com o banco Chatwoot
- Versão do PostgreSQL
- Status da aplicação

### **Logs**
O sistema registra:
- Conexões e desconexões
- Erros de query
- Tempos de resposta
- Uso de recursos

---

## 📈 **Próximas Implementações**

### **Funcionalidades Planejadas**
- [ ] Exportação em CSV e Excel
- [ ] Relatórios agendados por email
- [ ] Alertas automáticos para métricas críticas
- [ ] Integração com webhooks
- [ ] Dashboard em tempo real
- [ ] Análise de sentimento das mensagens
- [ ] Métricas de SLA
- [ ] Comparativos históricos

### **Melhorias Técnicas**
- [ ] Cache Redis para queries pesadas
- [ ] Compressão de dados
- [ ] Paginação para grandes volumes
- [ ] Otimização de queries
- [ ] Testes automatizados

---

## 🛠️ **Solução de Problemas**

### **Problemas Comuns**

#### **Erro de Conexão**
```
Erro: Não foi possível conectar ao banco Chatwoot
```
**Solução**: Verificar configurações de rede e credenciais.

#### **Dados Não Encontrados**
```
Nenhum dado encontrado para o período selecionado
```
**Solução**: Verificar se há dados no período ou expandir o intervalo.

#### **Timeout de Query**
```
Timeout na execução da query
```
**Solução**: Reduzir o período ou otimizar a query.

### **Logs Úteis**
```bash
# Verificar logs da aplicação
tail -f logs/app.log

# Verificar conexão com Chatwoot
docker logs n8n_chatwoot_postgres
```

---

## 📞 **Suporte**

Para dúvidas ou problemas:

1. Verificar logs da aplicação
2. Testar conectividade com `/api/v1/chatwoot/health`
3. Consultar documentação da API do Chatwoot
4. Verificar configurações do banco de dados

---

**📝 Documento criado em:** 2025-01-30  
**🔄 Última atualização:** 2025-01-30  
**👨‍💻 Autor:** Augment Agent  
**📋 Status:** Implementado e Funcional
