#!/usr/bin/env python3
"""
Script de teste para a API do Sistema de Relatórios 3CX
"""
import asyncio
import httpx
from datetime import date, timedelta
import json


async def test_3cx_connection():
    """Testa conexão com a API do 3CX"""
    print("🔍 Testando conexão com 3CX...")
    
    url = "http://ticmobilerb.ddns.net/gdacip/apijson.php"
    
    payload = {
        "user": "amvox",
        "password": "super_7894",
        "data_inicial": "2025-01-01",
        "data_final": "2025-01-01",
        "filas": [802, 803],
        "relatorio": "Distribuição"
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(url, json=payload)
            print(f"✅ Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"📊 Dados recebidos: {json.dumps(data, indent=2, ensure_ascii=False)}")
            else:
                print(f"❌ Erro: {response.text}")
                
    except Exception as e:
        print(f"❌ Erro na conexão: {str(e)}")


async def test_local_api():
    """Testa a API local"""
    print("\n🔍 Testando API local...")
    
    base_url = "http://localhost:8000"
    
    # Testa health check
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{base_url}/health")
            print(f"✅ Health Check: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"📊 Status: {data.get('status')}")
                print(f"🔗 Conexão 3CX: {data.get('3cx_connection')}")
            
    except Exception as e:
        print(f"❌ Erro na API local: {str(e)}")


async def test_reports_api():
    """Testa endpoints de relatórios"""
    print("\n🔍 Testando endpoints de relatórios...")
    
    base_url = "http://localhost:8000"
    
    # Parâmetros de teste
    today = date.today()
    yesterday = today - timedelta(days=1)
    
    params = {
        "start_date": yesterday.isoformat(),
        "end_date": today.isoformat(),
        "queues": [802, 803]
    }
    
    endpoints = [
        "/api/v1/reports/distribution",
        "/api/v1/reports/satisfaction",
        "/api/v1/reports/timing-metrics",
        "/api/v1/reports/agent-performance"
    ]
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            for endpoint in endpoints:
                print(f"\n📊 Testando {endpoint}...")
                
                response = await client.post(f"{base_url}{endpoint}", params=params)
                print(f"Status: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ Sucesso: {data.get('message')}")
                    print(f"📈 Tipo: {data.get('report_type')}")
                else:
                    print(f"❌ Erro: {response.text}")
                    
    except Exception as e:
        print(f"❌ Erro nos relatórios: {str(e)}")


async def test_charts_api():
    """Testa endpoints de gráficos"""
    print("\n🔍 Testando endpoints de gráficos...")
    
    base_url = "http://localhost:8000"
    
    # Parâmetros de teste
    today = date.today()
    yesterday = today - timedelta(days=1)
    
    params = {
        "start_date": yesterday.isoformat(),
        "end_date": today.isoformat(),
        "queues": "802,803"
    }
    
    chart_types = [
        "distribution",
        "timing",
        "satisfaction",
        "service_level_gauge",
        "hourly"
    ]
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            for chart_type in chart_types:
                print(f"\n📈 Testando gráfico {chart_type}...")
                
                response = await client.get(f"{base_url}/api/charts/{chart_type}", params=params)
                print(f"Status: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"✅ Gráfico gerado com sucesso")
                    print(f"📏 Tamanho: {len(response.content)} bytes")
                else:
                    print(f"❌ Erro: {response.text}")
                    
    except Exception as e:
        print(f"❌ Erro nos gráficos: {str(e)}")


def print_banner():
    """Imprime banner do teste"""
    print("=" * 60)
    print("🧪 TESTE DO SISTEMA DE RELATÓRIOS 3CX - AMVOX")
    print("=" * 60)


def print_summary():
    """Imprime resumo dos testes"""
    print("\n" + "=" * 60)
    print("📋 RESUMO DOS TESTES")
    print("=" * 60)
    print("✅ Testes concluídos!")
    print("📖 Consulte os logs acima para detalhes")
    print("🌐 Acesse http://localhost:8000 para usar a interface web")
    print("📚 Documentação: 3CX_Reports_Documentation.html")


async def main():
    """Função principal de teste"""
    print_banner()
    
    # Executa todos os testes
    await test_3cx_connection()
    await test_local_api()
    await test_reports_api()
    await test_charts_api()
    
    print_summary()


if __name__ == "__main__":
    asyncio.run(main())
