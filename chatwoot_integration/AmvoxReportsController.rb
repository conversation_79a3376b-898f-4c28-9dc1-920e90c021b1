# Arquivo: /app/app/controllers/api/v1/accounts/amvox_reports_controller.rb

class Api::V1::Accounts::AmvoxReportsController < Api::V1::Accounts::BaseController
  before_action :check_authorization

  def index
    # Renderizar a página integrada dos relatórios Amvox
    render json: {
      success: true,
      data: {
        integration_url: 'https://amvoxreport.aplopes.com/',
        user_data: {
          id: Current.user.id,
          name: Current.user.name,
          email: Current.user.email,
          avatar_url: Current.user.avatar_url
        },
        account_data: {
          id: Current.account.id,
          name: Current.account.name,
          timezone: Current.account.timezone || 'UTC'
        },
        permissions: {
          can_view_reports: true,
          can_export_data: Current.user.administrator? || Current.user.agent?,
          can_manage_settings: Current.user.administrator?
        },
        tabs: [
          {
            id: 'dashboard',
            label: 'Dashboard',
            icon: 'i-lucide-layout-dashboard',
            url: 'https://amvoxreport.aplopes.com/',
            enabled: true
          },
          {
            id: 'telefonia',
            label: 'Telefonia',
            icon: 'i-lucide-phone',
            url: 'https://amvoxreport.aplopes.com/',
            enabled: true
          },
          {
            id: 'whatsapp',
            label: 'WhatsApp',
            icon: 'i-lucide-message-circle',
            url: 'https://amvoxreport.aplopes.com/chatwoot',
            enabled: true
          },
          {
            id: 'consolidado',
            label: 'Consolidado',
            icon: 'i-lucide-chart-pie',
            url: 'https://amvoxreport.aplopes.com/consolidado',
            enabled: Current.user.administrator?
          }
        ],
        metadata: {
          integration_version: '1.0.0',
          last_sync: Time.current.iso8601,
          features: {
            real_time_sync: true,
            cross_domain_auth: true,
            responsive_design: true,
            fullscreen_mode: true
          }
        }
      },
      message: 'Dados de integração carregados com sucesso'
    }
  end

  private

  def check_authorization
    # Verificar se o usuário tem permissão para acessar relatórios
    unless Current.user.agent? || Current.user.administrator?
      render json: { 
        error: 'Acesso negado. Apenas agentes e administradores podem acessar relatórios.' 
      }, status: :forbidden
      return
    end

    # Log de acesso para auditoria
    Rails.logger.info "Usuário #{Current.user.email} acessou relatórios Amvox integrados na conta #{Current.account.name}"
  end
end
