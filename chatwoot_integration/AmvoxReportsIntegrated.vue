<template>
  <div class="amvox-reports-container h-full flex flex-col bg-n-solid-1">
    <!-- Header integrado com Chatwoot -->
    <div class="amvox-header flex items-center justify-between p-4 bg-n-solid-2 border-b border-n-weak">
      <div class="flex items-center gap-3">
        <div class="flex items-center gap-2">
          <i class="i-lucide-chart-spline text-xl text-n-brand"></i>
          <h1 class="text-xl font-semibold text-n-slate-12">Relatórios Amvox</h1>
        </div>
        <div class="flex items-center gap-2 text-sm text-n-slate-11">
          <span class="size-2 bg-green-500 rounded-full animate-pulse"></span>
          <span>Sistema Integrado</span>
        </div>
      </div>
      
      <div class="flex items-center gap-2">
        <!-- <PERSON><PERSON><PERSON> de sincronização -->
        <button 
          @click="syncData"
          :disabled="syncing"
          class="flex items-center gap-2 px-3 py-1.5 text-sm bg-n-alpha-2 hover:bg-n-alpha-3 rounded-lg transition-colors"
          title="Sincronizar dados"
        >
          <i :class="['i-lucide-refresh-cw', { 'animate-spin': syncing }]"></i>
          <span v-if="!syncing">Sincronizar</span>
          <span v-else>Sincronizando...</span>
        </button>
        
        <!-- Botão de tela cheia -->
        <button 
          @click="toggleFullscreen"
          class="flex items-center gap-2 px-3 py-1.5 text-sm bg-n-alpha-2 hover:bg-n-alpha-3 rounded-lg transition-colors"
          title="Tela cheia"
        >
          <i class="i-lucide-maximize-2"></i>
        </button>
        
        <!-- Botão de nova aba -->
        <button 
          @click="openInNewTab"
          class="flex items-center gap-2 px-3 py-1.5 text-sm bg-n-brand text-white hover:bg-n-brand/90 rounded-lg transition-colors"
        >
          <i class="i-lucide-external-link"></i>
          <span>Nova Aba</span>
        </button>
      </div>
    </div>

    <!-- Barra de navegação integrada -->
    <div class="amvox-nav flex items-center gap-1 p-2 bg-n-solid-2 border-b border-n-weak">
      <button 
        v-for="tab in tabs" 
        :key="tab.id"
        @click="switchTab(tab)"
        :class="[
          'flex items-center gap-2 px-3 py-1.5 text-sm rounded-lg transition-colors',
          activeTab === tab.id 
            ? 'bg-n-brand text-white' 
            : 'text-n-slate-11 hover:bg-n-alpha-2'
        ]"
      >
        <i :class="tab.icon"></i>
        <span>{{ tab.label }}</span>
      </button>
    </div>

    <!-- Container principal com iframe -->
    <div class="amvox-content flex-1 relative overflow-hidden">
      <!-- Loading overlay -->
      <div 
        v-if="loading"
        class="absolute inset-0 flex items-center justify-center bg-n-solid-1 z-10"
      >
        <div class="text-center">
          <div class="size-12 border-4 border-n-brand border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p class="text-n-slate-11">Carregando sistema de relatórios...</p>
          <p class="text-sm text-n-slate-10 mt-1">Conectando com {{ currentUrl }}</p>
        </div>
      </div>

      <!-- Error overlay -->
      <div 
        v-if="error"
        class="absolute inset-0 flex items-center justify-center bg-n-solid-1 z-10"
      >
        <div class="text-center max-w-md">
          <i class="i-lucide-alert-triangle text-4xl text-red-500 mb-4"></i>
          <h3 class="text-lg font-semibold text-n-slate-12 mb-2">Erro ao carregar relatórios</h3>
          <p class="text-n-slate-11 mb-4">{{ error }}</p>
          <button 
            @click="retryLoad"
            class="px-4 py-2 bg-n-brand text-white rounded-lg hover:bg-n-brand/90"
          >
            Tentar novamente
          </button>
        </div>
      </div>

      <!-- Iframe principal -->
      <iframe 
        ref="reportFrame"
        :src="currentUrl"
        class="w-full h-full border-0"
        :class="{ 'opacity-0': loading || error }"
        title="Relatórios Amvox"
        @load="onFrameLoad"
        @error="onFrameError"
        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
      />
    </div>

    <!-- Status bar -->
    <div class="amvox-status flex items-center justify-between px-4 py-2 bg-n-solid-2 border-t border-n-weak text-xs text-n-slate-10">
      <div class="flex items-center gap-4">
        <span>Status: {{ connectionStatus }}</span>
        <span>Última atualização: {{ lastUpdate }}</span>
      </div>
      <div class="flex items-center gap-2">
        <span>Usuário: {{ currentUser?.name || 'N/A' }}</span>
        <span>•</span>
        <span>Conta: {{ currentAccount?.name || 'N/A' }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'

const store = useStore()
const { t } = useI18n()

// Estado reativo
const loading = ref(true)
const error = ref(null)
const syncing = ref(false)
const activeTab = ref('dashboard')
const reportFrame = ref(null)
const connectionStatus = ref('Conectando...')
const lastUpdate = ref(new Date().toLocaleTimeString('pt-BR'))

// Dados do usuário e conta
const currentUser = computed(() => store.getters.getCurrentUser)
const currentAccount = computed(() => store.getters.getCurrentAccount)

// Configuração das abas
const tabs = ref([
  { id: 'dashboard', label: 'Dashboard', icon: 'i-lucide-layout-dashboard', url: 'https://amvoxreport.aplopes.com/' },
  { id: 'telefonia', label: 'Telefonia', icon: 'i-lucide-phone', url: 'https://amvoxreport.aplopes.com/' },
  { id: 'whatsapp', label: 'WhatsApp', icon: 'i-lucide-message-circle', url: 'https://amvoxreport.aplopes.com/chatwoot' },
  { id: 'consolidado', label: 'Consolidado', icon: 'i-lucide-chart-pie', url: 'https://amvoxreport.aplopes.com/consolidado' }
])

const currentUrl = computed(() => {
  const tab = tabs.value.find(t => t.id === activeTab.value)
  return tab?.url || 'https://amvoxreport.aplopes.com/'
})

// Métodos
const onFrameLoad = () => {
  loading.value = false
  error.value = null
  connectionStatus.value = 'Conectado'
  lastUpdate.value = new Date().toLocaleTimeString('pt-BR')

  // Enviar dados do usuário para o iframe
  sendUserDataToFrame()
}

const onFrameError = () => {
  loading.value = false
  error.value = 'Não foi possível carregar o sistema de relatórios. Verifique sua conexão.'
  connectionStatus.value = 'Erro de conexão'
}

const retryLoad = () => {
  error.value = null
  loading.value = true
  connectionStatus.value = 'Reconectando...'

  // Recarregar iframe
  if (reportFrame.value) {
    reportFrame.value.src = reportFrame.value.src
  }
}

const switchTab = (tab) => {
  if (activeTab.value !== tab.id) {
    activeTab.value = tab.id
    loading.value = true
    connectionStatus.value = 'Carregando...'
  }
}

const syncData = async () => {
  syncing.value = true

  try {
    // Simular sincronização
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Enviar comando de sincronização para o iframe
    sendMessageToFrame({
      type: 'SYNC_DATA',
      timestamp: Date.now()
    })

    lastUpdate.value = new Date().toLocaleTimeString('pt-BR')
  } catch (err) {
    console.error('Erro na sincronização:', err)
  } finally {
    syncing.value = false
  }
}

const toggleFullscreen = () => {
  if (document.fullscreenElement) {
    document.exitFullscreen()
  } else {
    document.documentElement.requestFullscreen()
  }
}

const openInNewTab = () => {
  window.open(currentUrl.value, '_blank', 'noopener,noreferrer')
}

const sendUserDataToFrame = () => {
  const userData = {
    type: 'USER_DATA',
    user: {
      id: currentUser.value?.id,
      name: currentUser.value?.name,
      email: currentUser.value?.email,
      avatar: currentUser.value?.avatar_url
    },
    account: {
      id: currentAccount.value?.id,
      name: currentAccount.value?.name
    },
    timestamp: Date.now()
  }

  sendMessageToFrame(userData)
}

const sendMessageToFrame = (message) => {
  if (reportFrame.value && reportFrame.value.contentWindow) {
    try {
      reportFrame.value.contentWindow.postMessage(message, 'https://amvoxreport.aplopes.com')
    } catch (err) {
      console.warn('Erro ao enviar mensagem para iframe:', err)
    }
  }
}

// Listener para mensagens do iframe
const handleFrameMessage = (event) => {
  if (event.origin !== 'https://amvoxreport.aplopes.com') {
    return
  }

  const { type, data } = event.data

  switch (type) {
    case 'FRAME_READY':
      console.log('Frame pronto para comunicação')
      sendUserDataToFrame()
      break

    case 'NAVIGATION_REQUEST':
      if (data.tab && tabs.value.find(t => t.id === data.tab)) {
        switchTab(tabs.value.find(t => t.id === data.tab))
      }
      break

    case 'ERROR_REPORT':
      console.error('Erro reportado pelo iframe:', data)
      break

    default:
      console.log('Mensagem recebida do iframe:', event.data)
  }
}

// Lifecycle
onMounted(() => {
  window.addEventListener('message', handleFrameMessage)

  // Configurar timeout para loading
  setTimeout(() => {
    if (loading.value) {
      onFrameError()
    }
  }, 30000) // 30 segundos timeout
})

onUnmounted(() => {
  window.removeEventListener('message', handleFrameMessage)
})
</script>

<style scoped>
.amvox-reports-container {
  font-family: inherit;
}

.amvox-header {
  backdrop-filter: blur(8px);
}

.amvox-nav {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.amvox-nav::-webkit-scrollbar {
  display: none;
}

.amvox-content iframe {
  transition: opacity 0.3s ease;
}

@media (max-width: 768px) {
  .amvox-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .amvox-nav {
    overflow-x: auto;
    flex-wrap: nowrap;
  }

  .amvox-status {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
    text-align: center;
  }
}
</style>
