/* Estilos para integração com Chatwoot */
/* Adicionar ao seu sistema (https://amvoxreport.aplopes.com/) */

/* Ajustes quando carregado dentro do Chatwoot */
.chatwoot-integrated {
  /* Remover margens e paddings desnecessários */
  margin: 0 !important;
  padding: 0 !important;
  
  /* Garantir altura total */
  min-height: 100vh;
  height: 100vh;
  overflow: hidden;
}

/* Ocultar header próprio quando integrado */
.chatwoot-integrated .main-header,
.chatwoot-integrated [data-hide-in-chatwoot] {
  display: none !important;
}

/* Ajustar container principal */
.chatwoot-integrated .container,
.chatwoot-integrated .main-content {
  max-width: none !important;
  padding: 0 !important;
  margin: 0 !important;
  height: 100vh;
}

/* Ajustar cards e componentes */
.chatwoot-integrated .card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Ajustar gráficos para melhor visualização */
.chatwoot-integrated .chart-container {
  background: white;
  border-radius: 8px;
  padding: 1rem;
}

/* Responsividade aprimorada */
.chatwoot-integrated .row {
  margin: 0;
}

.chatwoot-integrated .col-md-6,
.chatwoot-integrated .col-lg-4 {
  padding: 0.5rem;
}

/* Ajustar cores para combinar com Chatwoot */
.chatwoot-integrated {
  --primary-color: #1f93ff;
  --success-color: #00d084;
  --warning-color: #ffab00;
  --danger-color: #ff6b6b;
  --info-color: #17a2b8;
  
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --text-primary: #2d3748;
  --text-secondary: #718096;
  --border-color: #e2e8f0;
}

/* Tema escuro quando Chatwoot estiver em modo escuro */
.chatwoot-integrated[data-theme="dark"] {
  --bg-primary: #1a202c;
  --bg-secondary: #2d3748;
  --text-primary: #f7fafc;
  --text-secondary: #a0aec0;
  --border-color: #4a5568;
}

/* Indicador de integração */
.chatwoot-integration-badge {
  position: fixed;
  top: 10px;
  right: 10px;
  background: #00d084;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  z-index: 9999;
  opacity: 0.8;
}

.chatwoot-integration-badge::before {
  content: "🔗";
  margin-right: 4px;
}

/* Animações suaves */
.chatwoot-integrated * {
  transition: all 0.2s ease;
}

/* Ajustes para elementos específicos do seu sistema */
.chatwoot-integrated .metric-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

.chatwoot-integrated .metric-value {
  color: var(--primary-color);
}

.chatwoot-integrated .btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.chatwoot-integrated .btn-success {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

/* Ajustes para tabelas */
.chatwoot-integrated .table {
  background: var(--bg-primary);
  color: var(--text-primary);
}

.chatwoot-integrated .table th {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

.chatwoot-integrated .table td {
  border-color: var(--border-color);
}

/* Scrollbar customizada */
.chatwoot-integrated ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.chatwoot-integrated ::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.chatwoot-integrated ::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.chatwoot-integrated ::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* Ajustes para mobile */
@media (max-width: 768px) {
  .chatwoot-integrated {
    font-size: 14px;
  }
  
  .chatwoot-integrated .card {
    margin-bottom: 1rem;
  }
  
  .chatwoot-integrated .chart-container {
    height: 250px !important;
  }
}

/* Loading state */
.chatwoot-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--bg-primary);
}

.chatwoot-loading::after {
  content: "";
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
