#!/bin/bash

# Script de instalação da integração Amvox Reports no Chatwoot
# Execute este script no servidor onde o Chatwoot está rodando

set -e

echo "🚀 Iniciando instalação da integração Amvox Reports no Chatwoot..."

# Configurações
CHATWOOT_CONTAINER="186378ce9e24"  # Ajustar conforme necessário
BACKUP_DIR="/tmp/chatwoot_backup_$(date +%Y%m%d_%H%M%S)"
INTEGRATION_DIR="$(pwd)/chatwoot_integration"

# Função para log
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# Função para backup
backup_file() {
    local file_path="$1"
    local container="$2"
    
    log "Fazendo backup de $file_path"
    mkdir -p "$BACKUP_DIR/$(dirname $file_path)"
    docker exec "$container" cat "$file_path" > "$BACKUP_DIR/$file_path" 2>/dev/null || true
}

# Verificar se o container existe
if ! docker ps | grep -q "$CHATWOOT_CONTAINER"; then
    log "❌ Container do Chatwoot não encontrado: $CHATWOOT_CONTAINER"
    log "Containers disponíveis:"
    docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}"
    exit 1
fi

log "✅ Container do Chatwoot encontrado: $CHATWOOT_CONTAINER"

# Criar diretório de backup
mkdir -p "$BACKUP_DIR"
log "📁 Backup será salvo em: $BACKUP_DIR"

# 1. Backup dos arquivos originais
log "📋 Fazendo backup dos arquivos originais..."
backup_file "/app/app/javascript/dashboard/components-next/sidebar/Sidebar.vue" "$CHATWOOT_CONTAINER"
backup_file "/app/config/routes.rb" "$CHATWOOT_CONTAINER"

# 2. Modificar Sidebar.vue
log "🔧 Modificando Sidebar.vue..."
docker exec "$CHATWOOT_CONTAINER" bash -c "
# Fazer backup interno
cp /app/app/javascript/dashboard/components-next/sidebar/Sidebar.vue /app/app/javascript/dashboard/components-next/sidebar/Sidebar.vue.backup

# Substituir a seção de Reports
sed -i '/name: '\''Reports'\''/,/},$/c\
        {\
          name: '\''Reports'\'',\
          label: t('\''SIDEBAR.REPORTS'\''),\
          icon: '\''i-lucide-chart-spline'\'',\
          to: accountScopedRoute('\''amvox_reports_integrated'\''),\
          meta: {\
            external: true,\
            integration: '\''amvox'\'',\
            url: '\''https://amvoxreport.aplopes.com/'\''\
          }\
        },' /app/app/javascript/dashboard/components-next/sidebar/Sidebar.vue
"

# 3. Adicionar rota
log "🛣️ Adicionando rota..."
docker exec "$CHATWOOT_CONTAINER" bash -c "
# Backup do routes.rb
cp /app/config/routes.rb /app/config/routes.rb.backup

# Adicionar nova rota após a seção de reports
sed -i '/resources :reports, only: \[:index\]/a\
        get '\''amvox_reports_integrated'\'', to: '\''amvox_reports#index'\''' /app/config/routes.rb
"

# 4. Criar controller
log "🎮 Criando controller..."
docker exec "$CHATWOOT_CONTAINER" bash -c "
mkdir -p /app/app/controllers/api/v1/accounts/
cat > /app/app/controllers/api/v1/accounts/amvox_reports_controller.rb << 'EOF'
class Api::V1::Accounts::AmvoxReportsController < Api::V1::Accounts::BaseController
  before_action :check_authorization

  def index
    render json: {
      success: true,
      data: {
        integration_url: 'https://amvoxreport.aplopes.com/',
        user_data: {
          id: Current.user.id,
          name: Current.user.name,
          email: Current.user.email,
          avatar_url: Current.user.avatar_url
        },
        account_data: {
          id: Current.account.id,
          name: Current.account.name,
          timezone: Current.account.timezone || 'UTC'
        },
        permissions: {
          can_view_reports: true,
          can_export_data: Current.user.administrator? || Current.user.agent?,
          can_manage_settings: Current.user.administrator?
        },
        metadata: {
          integration_version: '1.0.0',
          last_sync: Time.current.iso8601
        }
      },
      message: 'Integração Amvox carregada com sucesso'
    }
  end

  private

  def check_authorization
    unless Current.user.agent? || Current.user.administrator?
      render json: { 
        error: 'Acesso negado. Apenas agentes e administradores podem acessar relatórios.' 
      }, status: :forbidden
      return
    end

    Rails.logger.info \"Usuário #{Current.user.email} acessou relatórios Amvox integrados\"
  end
end
EOF
"

# 5. Criar componente Vue
log "🖼️ Criando componente Vue..."
docker exec "$CHATWOOT_CONTAINER" bash -c "
mkdir -p /app/app/javascript/dashboard/routes/dashboard/amvox-reports/
"

# Copiar o componente Vue
docker cp "$INTEGRATION_DIR/AmvoxReportsIntegrated.vue" "$CHATWOOT_CONTAINER:/app/app/javascript/dashboard/routes/dashboard/amvox-reports/Index.vue"

# 6. Recompilar assets
log "🔨 Recompilando assets..."
docker exec "$CHATWOOT_CONTAINER" bash -c "
cd /app
bundle exec rails assets:precompile RAILS_ENV=production
"

# 7. Reiniciar Chatwoot
log "🔄 Reiniciando Chatwoot..."
docker restart "$CHATWOOT_CONTAINER"

# Aguardar container subir
log "⏳ Aguardando Chatwoot reiniciar..."
sleep 30

# Verificar se está funcionando
log "🔍 Verificando instalação..."
if docker exec "$CHATWOOT_CONTAINER" curl -s http://localhost:3000/health > /dev/null; then
    log "✅ Chatwoot reiniciado com sucesso!"
else
    log "⚠️ Chatwoot pode estar com problemas. Verificando logs..."
    docker logs "$CHATWOOT_CONTAINER" --tail 20
fi

# Instruções finais
log "🎉 Instalação concluída!"
echo ""
echo "📋 PRÓXIMOS PASSOS:"
echo "1. Acesse o Chatwoot: http://localhost:3000"
echo "2. Faça login como administrador ou agente"
echo "3. Clique em 'Relatórios' no menu lateral"
echo "4. Sua aplicação será carregada integrada!"
echo ""
echo "📁 Backup dos arquivos originais em: $BACKUP_DIR"
echo ""
echo "🔧 Para reverter a instalação:"
echo "docker exec $CHATWOOT_CONTAINER cp /app/app/javascript/dashboard/components-next/sidebar/Sidebar.vue.backup /app/app/javascript/dashboard/components-next/sidebar/Sidebar.vue"
echo "docker exec $CHATWOOT_CONTAINER cp /app/config/routes.rb.backup /app/config/routes.rb"
echo "docker restart $CHATWOOT_CONTAINER"
echo ""
echo "📞 Suporte: Verifique os logs em caso de problemas"
echo "docker logs $CHATWOOT_CONTAINER"
