// Arquivo para adicionar ao seu sistema (https://amvoxreport.aplopes.com/)
// Adicionar no <head> de todas as páginas do seu sistema

(function() {
    'use strict';
    
    // Configuração da comunicação com Chatwoot
    const CHATWOOT_ORIGIN = 'http://localhost:3000'; // Ajustar para produção
    let chatwootUserData = null;
    let isInChatwootFrame = false;
    
    // Detectar se está dentro do iframe do Chatwoot
    function detectChatwootFrame() {
        try {
            isInChatwootFrame = window.self !== window.top;
            return isInChatwootFrame;
        } catch (e) {
            isInChatwootFrame = true;
            return true;
        }
    }
    
    // Enviar mensagem para o Chatwoot
    function sendToChatwoot(message) {
        if (isInChatwootFrame && window.parent) {
            try {
                window.parent.postMessage(message, CHATWOOT_ORIGIN);
            } catch (error) {
                console.warn('Erro ao enviar mensagem para Chatwoot:', error);
            }
        }
    }
    
    // Receber mensagens do Chatwoot
    function handleChatwootMessage(event) {
        // Verificar origem por segurança
        if (event.origin !== CHATWOOT_ORIGIN) {
            return;
        }
        
        const { type, user, account, timestamp } = event.data;
        
        switch (type) {
            case 'USER_DATA':
                chatwootUserData = { user, account, timestamp };
                console.log('Dados do usuário Chatwoot recebidos:', chatwootUserData);
                
                // Aplicar dados do usuário na interface
                applyChatwootUserData(chatwootUserData);
                break;
                
            case 'SYNC_DATA':
                console.log('Solicitação de sincronização recebida');
                handleDataSync();
                break;
                
            default:
                console.log('Mensagem do Chatwoot:', event.data);
        }
    }
    
    // Aplicar dados do usuário na interface
    function applyChatwootUserData(data) {
        if (!data || !data.user) return;
        
        // Atualizar elementos da interface com dados do usuário
        const userElements = document.querySelectorAll('[data-chatwoot-user]');
        userElements.forEach(element => {
            const field = element.getAttribute('data-chatwoot-user');
            if (data.user[field]) {
                element.textContent = data.user[field];
            }
        });
        
        // Atualizar avatar se existir
        const avatarElements = document.querySelectorAll('[data-chatwoot-avatar]');
        avatarElements.forEach(element => {
            if (data.user.avatar) {
                element.src = data.user.avatar;
                element.alt = data.user.name || 'Avatar';
            }
        });
        
        // Atualizar informações da conta
        const accountElements = document.querySelectorAll('[data-chatwoot-account]');
        accountElements.forEach(element => {
            const field = element.getAttribute('data-chatwoot-account');
            if (data.account && data.account[field]) {
                element.textContent = data.account[field];
            }
        });
        
        // Disparar evento customizado
        window.dispatchEvent(new CustomEvent('chatwootUserDataReceived', {
            detail: data
        }));
    }
    
    // Sincronizar dados
    function handleDataSync() {
        // Recarregar dados ou atualizar gráficos
        if (typeof window.loadDashboard === 'function') {
            window.loadDashboard();
        }
        
        // Disparar evento de sincronização
        window.dispatchEvent(new CustomEvent('chatwootSyncRequested'));
    }
    
    // Notificar Chatwoot sobre navegação
    function notifyNavigation(tab) {
        sendToChatwoot({
            type: 'NAVIGATION_REQUEST',
            data: { tab }
        });
    }
    
    // Reportar erro para Chatwoot
    function reportError(error) {
        sendToChatwoot({
            type: 'ERROR_REPORT',
            data: {
                message: error.message,
                stack: error.stack,
                timestamp: Date.now()
            }
        });
    }
    
    // Inicialização
    function init() {
        detectChatwootFrame();
        
        if (isInChatwootFrame) {
            console.log('Sistema carregado dentro do Chatwoot');
            
            // Adicionar classe CSS para ajustes visuais
            document.body.classList.add('chatwoot-integrated');
            
            // Ocultar elementos desnecessários quando integrado
            const elementsToHide = document.querySelectorAll('[data-hide-in-chatwoot]');
            elementsToHide.forEach(el => el.style.display = 'none');
            
            // Listener para mensagens do Chatwoot
            window.addEventListener('message', handleChatwootMessage);
            
            // Notificar que o frame está pronto
            sendToChatwoot({
                type: 'FRAME_READY',
                timestamp: Date.now()
            });
            
            // Capturar erros e reportar
            window.addEventListener('error', (event) => {
                reportError(event.error);
            });
        }
    }
    
    // Aguardar DOM carregar
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // Expor funções globalmente para uso
    window.ChatwootIntegration = {
        isIntegrated: () => isInChatwootFrame,
        getUserData: () => chatwootUserData,
        sendMessage: sendToChatwoot,
        notifyNavigation,
        reportError
    };
    
})();
