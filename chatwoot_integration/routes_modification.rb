# Modificação para /app/config/routes.rb
# Adicionar dentro do namespace :api, :v1, :accounts

# LOCALIZAR A SEÇÃO DE REPORTS (aproximadamente linha 45-60):
# resources :reports, only: [:index] do
#   collection do
#     get :summary
#     get :bot_summary
#     get :agents
#     get :inboxes
#     get :labels
#     get :teams
#     get :conversations
#     get :conversation_traffic
#     get :bot_metrics
#   end
# end

# ADICIONAR APÓS A SEÇÃO DE REPORTS:
get 'amvox_reports_integrated', to: 'amvox_reports#index'

# OU SUBSTITUIR COMPLETAMENTE A SEÇÃO DE REPORTS POR:
# resources :reports, only: [] do
#   collection do
#     get :amvox_integrated, to: 'amvox_reports#index'
#   end
# end

# ROTA COMPLETA FICARÁ:
# GET /api/v1/accounts/:account_id/amvox_reports_integrated
