// Script para modificar o Sidebar.vue do Chatwoot
// Localização: /app/app/javascript/dashboard/components-next/sidebar/Sidebar.vue

// MODIFICAÇÃO NA LINHA ~287
// Substituir a seção de Reports por:

const newReportsConfig = {
  name: 'Reports',
  label: t('SIDEBAR.REPORTS'),
  icon: 'i-lucide-chart-spline',
  to: accountScopedRoute('amvox_reports_integrated'),
  // Remove children - sem submenus
  // Adiciona metadados para integração
  meta: {
    external: true,
    integration: 'amvox',
    url: 'https://amvoxreport.aplopes.com/'
  }
};

// CÓDIGO ORIGINAL A SER SUBSTITUÍDO:
/*
{
  name: 'Reports',
  label: t('SIDEBAR.REPORTS'),
  icon: 'i-lucide-chart-spline',
  children: [
    {
      name: 'Report Overview',
      label: t('SIDEBAR.REPORTS_OVERVIEW'),
      to: accountScopedRoute('account_overview_reports'),
    },
    {
      name: 'Report Conversation',
      label: t('SIDEBAR.REPORTS_CONVERSATION'),
      to: accountScopedRoute('conversation_reports'),
    },
    ...reportRoutes.value,
    {
      name: 'Reports CSAT',
      label: t('SIDEBAR.CSAT'),
      to: accountScopedRoute('csat_reports'),
    },
    {
      name: 'Reports SLA',
      label: t('SIDEBAR.REPORTS_SLA'),
      to: accountScopedRoute('sla_reports'),
    },
    {
      name: 'Reports Bot',
      label: t('SIDEBAR.REPORTS_BOT'),
      to: accountScopedRoute('bot_reports'),
    },
  ],
}
*/

// NOVO CÓDIGO:
/*
{
  name: 'Reports',
  label: t('SIDEBAR.REPORTS'),
  icon: 'i-lucide-chart-spline',
  to: accountScopedRoute('amvox_reports_integrated'),
  meta: {
    external: true,
    integration: 'amvox',
    url: 'https://amvoxreport.aplopes.com/'
  }
}
*/
